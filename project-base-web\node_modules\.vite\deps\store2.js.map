{"version": 3, "sources": ["../../store2/dist/store2.js"], "sourcesContent": ["/*! store2 - v2.14.4 - 2024-12-26\n* Copyright (c) 2024 <PERSON>; Licensed MIT */\n;(function(window, define) {\n    var _ = {\n        version: \"2.14.4\",\n        areas: {},\n        apis: {},\n        nsdelim: '.',\n\n        // utilities\n        inherit: function(api, o) {\n            for (var p in api) {\n                if (!o.hasOwnProperty(p)) {\n                    Object.defineProperty(o, p, Object.getOwnPropertyDescriptor(api, p));\n                }\n            }\n            return o;\n        },\n        stringify: function(d, fn) {\n            return d === undefined || typeof d === \"function\" ? d+'' : JSON.stringify(d,fn||_.replace);\n        },\n        parse: function(s, fn) {\n            // if it doesn't parse, return as is\n            try{ return JSON.parse(s,fn||_.revive); }catch(e){ return s; }\n        },\n\n        // extension hooks\n        fn: function(name, fn) {\n            _.storeAPI[name] = fn;\n            for (var api in _.apis) {\n                _.apis[api][name] = fn;\n            }\n        },\n        get: function(area, key){ return area.getItem(key); },\n        set: function(area, key, string){ area.setItem(key, string); },\n        remove: function(area, key){ area.removeItem(key); },\n        key: function(area, i){ return area.key(i); },\n        length: function(area){ return area.length; },\n        clear: function(area){ area.clear(); },\n\n        // core functions\n        Store: function(id, area, namespace) {\n            var store = _.inherit(_.storeAPI, function(key, data, overwrite) {\n                if (arguments.length === 0){ return store.getAll(); }\n                if (typeof data === \"function\"){ return store.transact(key, data, overwrite); }// fn=data, alt=overwrite\n                if (data !== undefined){ return store.set(key, data, overwrite); }\n                if (typeof key === \"string\" || typeof key === \"number\"){ return store.get(key); }\n                if (typeof key === \"function\"){ return store.each(key); }\n                if (!key){ return store.clear(); }\n                return store.setAll(key, data);// overwrite=data, data=key\n            });\n            store._id = id;\n            try {\n                var testKey = '__store2_test';\n                area.setItem(testKey, 'ok');\n                store._area = area;\n                area.removeItem(testKey);\n            } catch (e) {\n                store._area = _.storage('fake');\n            }\n            store._ns = namespace || '';\n            if (!_.areas[id]) {\n                _.areas[id] = store._area;\n            }\n            if (!_.apis[store._ns+store._id]) {\n                _.apis[store._ns+store._id] = store;\n            }\n            return store;\n        },\n        storeAPI: {\n            // admin functions\n            area: function(id, area) {\n                var store = this[id];\n                if (!store || !store.area) {\n                    store = _.Store(id, area, this._ns);//new area-specific api in this namespace\n                    if (!this[id]){ this[id] = store; }\n                }\n                return store;\n            },\n            namespace: function(namespace, singleArea, delim) {\n                delim = delim || this._delim || _.nsdelim;\n                if (!namespace){\n                    return this._ns ? this._ns.substring(0,this._ns.length-delim.length) : '';\n                }\n                var ns = namespace, store = this[ns];\n                if (!store || !store.namespace) {\n                    store = _.Store(this._id, this._area, this._ns+ns+delim);//new namespaced api\n                    store._delim = delim;\n                    if (!this[ns]){ this[ns] = store; }\n                    if (!singleArea) {\n                        for (var name in _.areas) {\n                            store.area(name, _.areas[name]);\n                        }\n                    }\n                }\n                return store;\n            },\n            isFake: function(force) {\n                if (force) {\n                    this._real = this._area;\n                    this._area = _.storage('fake');\n                } else if (force === false) {\n                    this._area = this._real || this._area;\n                }\n                return this._area.name === 'fake';\n            },\n            toString: function() {\n                return 'store'+(this._ns?'.'+this.namespace():'')+'['+this._id+']';\n            },\n\n            // storage functions\n            has: function(key) {\n                if (this._area.has) {\n                    return this._area.has(this._in(key));//extension hook\n                }\n                return !!(this._in(key) in this._area);\n            },\n            size: function(){ return this.keys().length; },\n            each: function(fn, fill) {// fill is used by keys(fillList) and getAll(fillList))\n                for (var i=0, m=_.length(this._area); i<m; i++) {\n                    var key = this._out(_.key(this._area, i));\n                    if (key !== undefined) {\n                        if (fn.call(this, key, this.get(key), fill) === false) {\n                            break;\n                        }\n                    }\n                    if (m > _.length(this._area)) { m--; i--; }// in case of removeItem\n                }\n                return fill || this;\n            },\n            keys: function(fillList) {\n                return this.each(function(k, v, list){ list.push(k); }, fillList || []);\n            },\n            get: function(key, alt) {\n                var s = _.get(this._area, this._in(key)),\n                    fn;\n                if (typeof alt === \"function\") {\n                    fn = alt;\n                    alt = null;\n                }\n                return s !== null ? _.parse(s, fn) :\n                    alt != null ? alt : s;\n            },\n            getAll: function(fillObj) {\n                return this.each(function(k, v, all){ all[k] = v; }, fillObj || {});\n            },\n            transact: function(key, fn, alt) {\n                var val = this.get(key, alt),\n                    ret = fn(val);\n                this.set(key, ret === undefined ? val : ret);\n                return this;\n            },\n            set: function(key, data, overwrite) {\n                var d = this.get(key),\n                    replacer;\n                if (d != null && overwrite === false) {\n                    return data;\n                }\n                if (typeof overwrite === \"function\") {\n                    replacer = overwrite;\n                    overwrite = undefined;\n                }\n                return _.set(this._area, this._in(key), _.stringify(data, replacer), overwrite) || d;\n            },\n            setAll: function(data, overwrite) {\n                var changed, val;\n                for (var key in data) {\n                    val = data[key];\n                    if (this.set(key, val, overwrite) !== val) {\n                        changed = true;\n                    }\n                }\n                return changed;\n            },\n            add: function(key, data, replacer) {\n                var d = this.get(key);\n                if (d instanceof Array) {\n                    data = d.concat(data);\n                } else if (d !== null) {\n                    var type = typeof d;\n                    if (type === typeof data && type === 'object') {\n                        for (var k in data) {\n                            d[k] = data[k];\n                        }\n                        data = d;\n                    } else {\n                        data = d + data;\n                    }\n                }\n                _.set(this._area, this._in(key), _.stringify(data, replacer));\n                return data;\n            },\n            remove: function(key, alt) {\n                var d = this.get(key, alt);\n                _.remove(this._area, this._in(key));\n                return d;\n            },\n            clear: function() {\n                if (!this._ns) {\n                    _.clear(this._area);\n                } else {\n                    this.each(function(k){ _.remove(this._area, this._in(k)); }, 1);\n                }\n                return this;\n            },\n            clearAll: function() {\n                var area = this._area;\n                for (var id in _.areas) {\n                    if (_.areas.hasOwnProperty(id)) {\n                        this._area = _.areas[id];\n                        this.clear();\n                    }\n                }\n                this._area = area;\n                return this;\n            },\n\n            // internal use functions\n            _in: function(k) {\n                if (typeof k !== \"string\"){ k = _.stringify(k); }\n                return this._ns ? this._ns + k : k;\n            },\n            _out: function(k) {\n                return this._ns ?\n                    k && k.indexOf(this._ns) === 0 ?\n                        k.substring(this._ns.length) :\n                        undefined : // so each() knows to skip it\n                    k;\n            }\n        },// end _.storeAPI\n        storage: function(name) {\n            return _.inherit(_.storageAPI, { items: {}, name: name });\n        },\n        storageAPI: {\n            length: 0,\n            has: function(k){ return this.items.hasOwnProperty(k); },\n            key: function(i) {\n                var c = 0;\n                for (var k in this.items){\n                    if (this.has(k) && i === c++) {\n                        return k;\n                    }\n                }\n            },\n            setItem: function(k, v) {\n                if (!this.has(k)) {\n                    this.length++;\n                }\n                this.items[k] = v;\n            },\n            removeItem: function(k) {\n                if (this.has(k)) {\n                    delete this.items[k];\n                    this.length--;\n                }\n            },\n            getItem: function(k){ return this.has(k) ? this.items[k] : null; },\n            clear: function(){ for (var k in this.items){ this.removeItem(k); } }\n        }// end _.storageAPI\n    };\n\n    var store =\n        // safely set this up (throws error in IE10/32bit mode for local files)\n        _.Store(\"local\", (function(){try{ return localStorage; }catch(e){}})());\n    store.local = store;// for completeness\n    store._ = _;// for extenders and debuggers...\n    // safely setup store.session (throws exception in FF for file:/// urls)\n    store.area(\"session\", (function(){try{ return sessionStorage; }catch(e){}})());\n    store.area(\"page\", _.storage(\"page\"));\n\n    if (typeof define === 'function' && define.amd !== undefined) {\n        define('store2', [], function () {\n            return store;\n        });\n    } else if (typeof module !== 'undefined' && module.exports) {\n        module.exports = store;\n    } else {\n        // expose the primary store fn to the global object and save conflicts\n        if (window.store){ _.conflict = window.store; }\n        window.store = store;\n    }\n\n})(this, this && this.define);\n"], "mappings": ";;;;;AAAA;AAAA;AAEC,KAAC,SAAS,QAAQ,QAAQ;AACvB,UAAI,IAAI;AAAA,QACJ,SAAS;AAAA,QACT,OAAO,CAAC;AAAA,QACR,MAAM,CAAC;AAAA,QACP,SAAS;AAAA;AAAA,QAGT,SAAS,SAAS,KAAK,GAAG;AACtB,mBAAS,KAAK,KAAK;AACf,gBAAI,CAAC,EAAE,eAAe,CAAC,GAAG;AACtB,qBAAO,eAAe,GAAG,GAAG,OAAO,yBAAyB,KAAK,CAAC,CAAC;AAAA,YACvE;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AAAA,QACA,WAAW,SAAS,GAAG,IAAI;AACvB,iBAAO,MAAM,UAAa,OAAO,MAAM,aAAa,IAAE,KAAK,KAAK,UAAU,GAAE,MAAI,EAAE,OAAO;AAAA,QAC7F;AAAA,QACA,OAAO,SAAS,GAAG,IAAI;AAEnB,cAAG;AAAE,mBAAO,KAAK,MAAM,GAAE,MAAI,EAAE,MAAM;AAAA,UAAG,SAAO,GAAE;AAAE,mBAAO;AAAA,UAAG;AAAA,QACjE;AAAA;AAAA,QAGA,IAAI,SAAS,MAAM,IAAI;AACnB,YAAE,SAAS,IAAI,IAAI;AACnB,mBAAS,OAAO,EAAE,MAAM;AACpB,cAAE,KAAK,GAAG,EAAE,IAAI,IAAI;AAAA,UACxB;AAAA,QACJ;AAAA,QACA,KAAK,SAAS,MAAM,KAAI;AAAE,iBAAO,KAAK,QAAQ,GAAG;AAAA,QAAG;AAAA,QACpD,KAAK,SAAS,MAAM,KAAK,QAAO;AAAE,eAAK,QAAQ,KAAK,MAAM;AAAA,QAAG;AAAA,QAC7D,QAAQ,SAAS,MAAM,KAAI;AAAE,eAAK,WAAW,GAAG;AAAA,QAAG;AAAA,QACnD,KAAK,SAAS,MAAM,GAAE;AAAE,iBAAO,KAAK,IAAI,CAAC;AAAA,QAAG;AAAA,QAC5C,QAAQ,SAAS,MAAK;AAAE,iBAAO,KAAK;AAAA,QAAQ;AAAA,QAC5C,OAAO,SAAS,MAAK;AAAE,eAAK,MAAM;AAAA,QAAG;AAAA;AAAA,QAGrC,OAAO,SAAS,IAAI,MAAM,WAAW;AACjC,cAAIA,SAAQ,EAAE,QAAQ,EAAE,UAAU,SAAS,KAAK,MAAM,WAAW;AAC7D,gBAAI,UAAU,WAAW,GAAE;AAAE,qBAAOA,OAAM,OAAO;AAAA,YAAG;AACpD,gBAAI,OAAO,SAAS,YAAW;AAAE,qBAAOA,OAAM,SAAS,KAAK,MAAM,SAAS;AAAA,YAAG;AAC9E,gBAAI,SAAS,QAAU;AAAE,qBAAOA,OAAM,IAAI,KAAK,MAAM,SAAS;AAAA,YAAG;AACjE,gBAAI,OAAO,QAAQ,YAAY,OAAO,QAAQ,UAAS;AAAE,qBAAOA,OAAM,IAAI,GAAG;AAAA,YAAG;AAChF,gBAAI,OAAO,QAAQ,YAAW;AAAE,qBAAOA,OAAM,KAAK,GAAG;AAAA,YAAG;AACxD,gBAAI,CAAC,KAAI;AAAE,qBAAOA,OAAM,MAAM;AAAA,YAAG;AACjC,mBAAOA,OAAM,OAAO,KAAK,IAAI;AAAA,UACjC,CAAC;AACD,UAAAA,OAAM,MAAM;AACZ,cAAI;AACA,gBAAI,UAAU;AACd,iBAAK,QAAQ,SAAS,IAAI;AAC1B,YAAAA,OAAM,QAAQ;AACd,iBAAK,WAAW,OAAO;AAAA,UAC3B,SAAS,GAAG;AACR,YAAAA,OAAM,QAAQ,EAAE,QAAQ,MAAM;AAAA,UAClC;AACA,UAAAA,OAAM,MAAM,aAAa;AACzB,cAAI,CAAC,EAAE,MAAM,EAAE,GAAG;AACd,cAAE,MAAM,EAAE,IAAIA,OAAM;AAAA,UACxB;AACA,cAAI,CAAC,EAAE,KAAKA,OAAM,MAAIA,OAAM,GAAG,GAAG;AAC9B,cAAE,KAAKA,OAAM,MAAIA,OAAM,GAAG,IAAIA;AAAA,UAClC;AACA,iBAAOA;AAAA,QACX;AAAA,QACA,UAAU;AAAA;AAAA,UAEN,MAAM,SAAS,IAAI,MAAM;AACrB,gBAAIA,SAAQ,KAAK,EAAE;AACnB,gBAAI,CAACA,UAAS,CAACA,OAAM,MAAM;AACvB,cAAAA,SAAQ,EAAE,MAAM,IAAI,MAAM,KAAK,GAAG;AAClC,kBAAI,CAAC,KAAK,EAAE,GAAE;AAAE,qBAAK,EAAE,IAAIA;AAAA,cAAO;AAAA,YACtC;AACA,mBAAOA;AAAA,UACX;AAAA,UACA,WAAW,SAAS,WAAW,YAAY,OAAO;AAC9C,oBAAQ,SAAS,KAAK,UAAU,EAAE;AAClC,gBAAI,CAAC,WAAU;AACX,qBAAO,KAAK,MAAM,KAAK,IAAI,UAAU,GAAE,KAAK,IAAI,SAAO,MAAM,MAAM,IAAI;AAAA,YAC3E;AACA,gBAAI,KAAK,WAAWA,SAAQ,KAAK,EAAE;AACnC,gBAAI,CAACA,UAAS,CAACA,OAAM,WAAW;AAC5B,cAAAA,SAAQ,EAAE,MAAM,KAAK,KAAK,KAAK,OAAO,KAAK,MAAI,KAAG,KAAK;AACvD,cAAAA,OAAM,SAAS;AACf,kBAAI,CAAC,KAAK,EAAE,GAAE;AAAE,qBAAK,EAAE,IAAIA;AAAA,cAAO;AAClC,kBAAI,CAAC,YAAY;AACb,yBAAS,QAAQ,EAAE,OAAO;AACtB,kBAAAA,OAAM,KAAK,MAAM,EAAE,MAAM,IAAI,CAAC;AAAA,gBAClC;AAAA,cACJ;AAAA,YACJ;AACA,mBAAOA;AAAA,UACX;AAAA,UACA,QAAQ,SAAS,OAAO;AACpB,gBAAI,OAAO;AACP,mBAAK,QAAQ,KAAK;AAClB,mBAAK,QAAQ,EAAE,QAAQ,MAAM;AAAA,YACjC,WAAW,UAAU,OAAO;AACxB,mBAAK,QAAQ,KAAK,SAAS,KAAK;AAAA,YACpC;AACA,mBAAO,KAAK,MAAM,SAAS;AAAA,UAC/B;AAAA,UACA,UAAU,WAAW;AACjB,mBAAO,WAAS,KAAK,MAAI,MAAI,KAAK,UAAU,IAAE,MAAI,MAAI,KAAK,MAAI;AAAA,UACnE;AAAA;AAAA,UAGA,KAAK,SAAS,KAAK;AACf,gBAAI,KAAK,MAAM,KAAK;AAChB,qBAAO,KAAK,MAAM,IAAI,KAAK,IAAI,GAAG,CAAC;AAAA,YACvC;AACA,mBAAO,CAAC,EAAE,KAAK,IAAI,GAAG,KAAK,KAAK;AAAA,UACpC;AAAA,UACA,MAAM,WAAU;AAAE,mBAAO,KAAK,KAAK,EAAE;AAAA,UAAQ;AAAA,UAC7C,MAAM,SAAS,IAAI,MAAM;AACrB,qBAAS,IAAE,GAAG,IAAE,EAAE,OAAO,KAAK,KAAK,GAAG,IAAE,GAAG,KAAK;AAC5C,kBAAI,MAAM,KAAK,KAAK,EAAE,IAAI,KAAK,OAAO,CAAC,CAAC;AACxC,kBAAI,QAAQ,QAAW;AACnB,oBAAI,GAAG,KAAK,MAAM,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,MAAM,OAAO;AACnD;AAAA,gBACJ;AAAA,cACJ;AACA,kBAAI,IAAI,EAAE,OAAO,KAAK,KAAK,GAAG;AAAE;AAAK;AAAA,cAAK;AAAA,YAC9C;AACA,mBAAO,QAAQ;AAAA,UACnB;AAAA,UACA,MAAM,SAAS,UAAU;AACrB,mBAAO,KAAK,KAAK,SAAS,GAAG,GAAG,MAAK;AAAE,mBAAK,KAAK,CAAC;AAAA,YAAG,GAAG,YAAY,CAAC,CAAC;AAAA,UAC1E;AAAA,UACA,KAAK,SAAS,KAAK,KAAK;AACpB,gBAAI,IAAI,EAAE,IAAI,KAAK,OAAO,KAAK,IAAI,GAAG,CAAC,GACnC;AACJ,gBAAI,OAAO,QAAQ,YAAY;AAC3B,mBAAK;AACL,oBAAM;AAAA,YACV;AACA,mBAAO,MAAM,OAAO,EAAE,MAAM,GAAG,EAAE,IAC7B,OAAO,OAAO,MAAM;AAAA,UAC5B;AAAA,UACA,QAAQ,SAAS,SAAS;AACtB,mBAAO,KAAK,KAAK,SAAS,GAAG,GAAG,KAAI;AAAE,kBAAI,CAAC,IAAI;AAAA,YAAG,GAAG,WAAW,CAAC,CAAC;AAAA,UACtE;AAAA,UACA,UAAU,SAAS,KAAK,IAAI,KAAK;AAC7B,gBAAI,MAAM,KAAK,IAAI,KAAK,GAAG,GACvB,MAAM,GAAG,GAAG;AAChB,iBAAK,IAAI,KAAK,QAAQ,SAAY,MAAM,GAAG;AAC3C,mBAAO;AAAA,UACX;AAAA,UACA,KAAK,SAAS,KAAK,MAAM,WAAW;AAChC,gBAAI,IAAI,KAAK,IAAI,GAAG,GAChB;AACJ,gBAAI,KAAK,QAAQ,cAAc,OAAO;AAClC,qBAAO;AAAA,YACX;AACA,gBAAI,OAAO,cAAc,YAAY;AACjC,yBAAW;AACX,0BAAY;AAAA,YAChB;AACA,mBAAO,EAAE,IAAI,KAAK,OAAO,KAAK,IAAI,GAAG,GAAG,EAAE,UAAU,MAAM,QAAQ,GAAG,SAAS,KAAK;AAAA,UACvF;AAAA,UACA,QAAQ,SAAS,MAAM,WAAW;AAC9B,gBAAI,SAAS;AACb,qBAAS,OAAO,MAAM;AAClB,oBAAM,KAAK,GAAG;AACd,kBAAI,KAAK,IAAI,KAAK,KAAK,SAAS,MAAM,KAAK;AACvC,0BAAU;AAAA,cACd;AAAA,YACJ;AACA,mBAAO;AAAA,UACX;AAAA,UACA,KAAK,SAAS,KAAK,MAAM,UAAU;AAC/B,gBAAI,IAAI,KAAK,IAAI,GAAG;AACpB,gBAAI,aAAa,OAAO;AACpB,qBAAO,EAAE,OAAO,IAAI;AAAA,YACxB,WAAW,MAAM,MAAM;AACnB,kBAAI,OAAO,OAAO;AAClB,kBAAI,SAAS,OAAO,QAAQ,SAAS,UAAU;AAC3C,yBAAS,KAAK,MAAM;AAChB,oBAAE,CAAC,IAAI,KAAK,CAAC;AAAA,gBACjB;AACA,uBAAO;AAAA,cACX,OAAO;AACH,uBAAO,IAAI;AAAA,cACf;AAAA,YACJ;AACA,cAAE,IAAI,KAAK,OAAO,KAAK,IAAI,GAAG,GAAG,EAAE,UAAU,MAAM,QAAQ,CAAC;AAC5D,mBAAO;AAAA,UACX;AAAA,UACA,QAAQ,SAAS,KAAK,KAAK;AACvB,gBAAI,IAAI,KAAK,IAAI,KAAK,GAAG;AACzB,cAAE,OAAO,KAAK,OAAO,KAAK,IAAI,GAAG,CAAC;AAClC,mBAAO;AAAA,UACX;AAAA,UACA,OAAO,WAAW;AACd,gBAAI,CAAC,KAAK,KAAK;AACX,gBAAE,MAAM,KAAK,KAAK;AAAA,YACtB,OAAO;AACH,mBAAK,KAAK,SAAS,GAAE;AAAE,kBAAE,OAAO,KAAK,OAAO,KAAK,IAAI,CAAC,CAAC;AAAA,cAAG,GAAG,CAAC;AAAA,YAClE;AACA,mBAAO;AAAA,UACX;AAAA,UACA,UAAU,WAAW;AACjB,gBAAI,OAAO,KAAK;AAChB,qBAAS,MAAM,EAAE,OAAO;AACpB,kBAAI,EAAE,MAAM,eAAe,EAAE,GAAG;AAC5B,qBAAK,QAAQ,EAAE,MAAM,EAAE;AACvB,qBAAK,MAAM;AAAA,cACf;AAAA,YACJ;AACA,iBAAK,QAAQ;AACb,mBAAO;AAAA,UACX;AAAA;AAAA,UAGA,KAAK,SAAS,GAAG;AACb,gBAAI,OAAO,MAAM,UAAS;AAAE,kBAAI,EAAE,UAAU,CAAC;AAAA,YAAG;AAChD,mBAAO,KAAK,MAAM,KAAK,MAAM,IAAI;AAAA,UACrC;AAAA,UACA,MAAM,SAAS,GAAG;AACd,mBAAO,KAAK,MACR,KAAK,EAAE,QAAQ,KAAK,GAAG,MAAM,IACzB,EAAE,UAAU,KAAK,IAAI,MAAM,IAC3B;AAAA;AAAA,cACJ;AAAA;AAAA,UACR;AAAA,QACJ;AAAA;AAAA,QACA,SAAS,SAAS,MAAM;AACpB,iBAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,OAAO,CAAC,GAAG,KAAW,CAAC;AAAA,QAC5D;AAAA,QACA,YAAY;AAAA,UACR,QAAQ;AAAA,UACR,KAAK,SAAS,GAAE;AAAE,mBAAO,KAAK,MAAM,eAAe,CAAC;AAAA,UAAG;AAAA,UACvD,KAAK,SAAS,GAAG;AACb,gBAAI,IAAI;AACR,qBAAS,KAAK,KAAK,OAAM;AACrB,kBAAI,KAAK,IAAI,CAAC,KAAK,MAAM,KAAK;AAC1B,uBAAO;AAAA,cACX;AAAA,YACJ;AAAA,UACJ;AAAA,UACA,SAAS,SAAS,GAAG,GAAG;AACpB,gBAAI,CAAC,KAAK,IAAI,CAAC,GAAG;AACd,mBAAK;AAAA,YACT;AACA,iBAAK,MAAM,CAAC,IAAI;AAAA,UACpB;AAAA,UACA,YAAY,SAAS,GAAG;AACpB,gBAAI,KAAK,IAAI,CAAC,GAAG;AACb,qBAAO,KAAK,MAAM,CAAC;AACnB,mBAAK;AAAA,YACT;AAAA,UACJ;AAAA,UACA,SAAS,SAAS,GAAE;AAAE,mBAAO,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AAAA,UAAM;AAAA,UACjE,OAAO,WAAU;AAAE,qBAAS,KAAK,KAAK,OAAM;AAAE,mBAAK,WAAW,CAAC;AAAA,YAAG;AAAA,UAAE;AAAA,QACxE;AAAA;AAAA,MACJ;AAEA,UAAI;AAAA;AAAA,QAEA,EAAE,MAAM,SAAU,WAAU;AAAC,cAAG;AAAE,mBAAO;AAAA,UAAc,SAAO,GAAE;AAAA,UAAC;AAAA,QAAC,EAAG,CAAC;AAAA;AAC1E,YAAM,QAAQ;AACd,YAAM,IAAI;AAEV,YAAM,KAAK,WAAY,WAAU;AAAC,YAAG;AAAE,iBAAO;AAAA,QAAgB,SAAO,GAAE;AAAA,QAAC;AAAA,MAAC,EAAG,CAAC;AAC7E,YAAM,KAAK,QAAQ,EAAE,QAAQ,MAAM,CAAC;AAEpC,UAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,QAAW;AAC1D,eAAO,UAAU,CAAC,GAAG,WAAY;AAC7B,iBAAO;AAAA,QACX,CAAC;AAAA,MACL,WAAW,OAAO,WAAW,eAAe,OAAO,SAAS;AACxD,eAAO,UAAU;AAAA,MACrB,OAAO;AAEH,YAAI,OAAO,OAAM;AAAE,YAAE,WAAW,OAAO;AAAA,QAAO;AAC9C,eAAO,QAAQ;AAAA,MACnB;AAAA,IAEJ,GAAG,SAAM,WAAQ,QAAK,MAAM;AAAA;AAAA;", "names": ["store"]}