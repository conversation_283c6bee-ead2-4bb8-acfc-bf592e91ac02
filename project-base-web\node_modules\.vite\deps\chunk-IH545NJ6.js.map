{"version": 3, "sources": ["../../js-binary-schema-parser/src/parsers/uint8.js"], "sourcesContent": ["// Default stream and parsers for Uint8TypedArray data type\r\n\r\nexport const buildStream = uint8Data => ({\r\n  data: uint8Data,\r\n  pos: 0\r\n})\r\n\r\nexport const readByte = () => stream => {\r\n  return stream.data[stream.pos++]\r\n}\r\n\r\nexport const peekByte = (offset = 0) => stream => {\r\n  return stream.data[stream.pos + offset]\r\n}\r\n\r\nexport const readBytes = length => stream => {\r\n  return stream.data.subarray(stream.pos, (stream.pos += length))\r\n}\r\n\r\nexport const peekBytes = length => stream => {\r\n  return stream.data.subarray(stream.pos, stream.pos + length)\r\n}\r\n\r\nexport const readString = length => stream => {\r\n  return Array.from(readBytes(length)(stream))\r\n    .map(value => String.fromCharCode(value))\r\n    .join('')\r\n}\r\n\r\nexport const readUnsigned = littleEndian => stream => {\r\n  const bytes = readBytes(2)(stream)\r\n  return littleEndian ? (bytes[1] << 8) + bytes[0] : (bytes[0] << 8) + bytes[1]\r\n}\r\n\r\nexport const readArray = (byteSize, totalOrFunc) => (\r\n  stream,\r\n  result,\r\n  parent\r\n) => {\r\n  const total =\r\n    typeof totalOrFunc === 'function'\r\n      ? totalOrFunc(stream, result, parent)\r\n      : totalOrFunc\r\n\r\n  const parser = readBytes(byteSize)\r\n  const arr = new Array(total)\r\n  for (var i = 0; i < total; i++) {\r\n    arr[i] = parser(stream)\r\n  }\r\n  return arr\r\n}\r\n\r\nconst subBitsTotal = (bits, startIndex, length) => {\r\n  var result = 0\r\n  for (var i = 0; i < length; i++) {\r\n    result += bits[startIndex + i] && 2 ** (length - i - 1)\r\n  }\r\n  return result\r\n}\r\n\r\nexport const readBits = schema => stream => {\r\n  const byte = readByte()(stream)\r\n  // convert the byte to bit array\r\n  const bits = new Array(8)\r\n  for (var i = 0; i < 8; i++) {\r\n    bits[7 - i] = !!(byte & (1 << i))\r\n  }\r\n  // convert the bit array to values based on the schema\r\n  return Object.keys(schema).reduce((res, key) => {\r\n    const def = schema[key]\r\n    if (def.length) {\r\n      res[key] = subBitsTotal(bits, def.index, def.length)\r\n    } else {\r\n      res[key] = bits[def.index]\r\n    }\r\n    return res\r\n  }, {})\r\n}\r\n"], "mappings": ";AAEO,IAAM,cAAc,gBAAc;AAAA,EACvC,MAAM;AAAA,EACN,KAAK;AACP;AAEO,IAAM,WAAW,MAAM,YAAU;AACtC,SAAO,OAAO,KAAK,OAAO,KAAK;AACjC;AAEO,IAAM,WAAW,CAAC,SAAS,MAAM,YAAU;AAChD,SAAO,OAAO,KAAK,OAAO,MAAM,MAAM;AACxC;AAEO,IAAM,YAAY,YAAU,YAAU;AAC3C,SAAO,OAAO,KAAK,SAAS,OAAO,KAAM,OAAO,OAAO,MAAO;AAChE;AAEO,IAAM,YAAY,YAAU,YAAU;AAC3C,SAAO,OAAO,KAAK,SAAS,OAAO,KAAK,OAAO,MAAM,MAAM;AAC7D;AAEO,IAAM,aAAa,YAAU,YAAU;AAC5C,SAAO,MAAM,KAAK,UAAU,MAAM,EAAE,MAAM,CAAC,EACxC,IAAI,WAAS,OAAO,aAAa,KAAK,CAAC,EACvC,KAAK,EAAE;AACZ;AAEO,IAAM,eAAe,kBAAgB,YAAU;AACpD,QAAM,QAAQ,UAAU,CAAC,EAAE,MAAM;AACjC,SAAO,gBAAgB,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC;AAC9E;AAEO,IAAM,YAAY,CAAC,UAAU,gBAAgB,CAClD,QACA,QACA,WACG;AACH,QAAM,QACJ,OAAO,gBAAgB,aACnB,YAAY,QAAQ,QAAQ,MAAM,IAClC;AAEN,QAAM,SAAS,UAAU,QAAQ;AACjC,QAAM,MAAM,IAAI,MAAM,KAAK;AAC3B,WAAS,IAAI,GAAG,IAAI,OAAO,KAAK;AAC9B,QAAI,CAAC,IAAI,OAAO,MAAM;AAAA,EACxB;AACA,SAAO;AACT;AAEA,IAAM,eAAe,CAAC,MAAM,YAAY,WAAW;AACjD,MAAI,SAAS;AACb,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,cAAU,KAAK,aAAa,CAAC,KAAK,MAAM,SAAS,IAAI;AAAA,EACvD;AACA,SAAO;AACT;AAEO,IAAM,WAAW,YAAU,YAAU;AAC1C,QAAM,OAAO,SAAS,EAAE,MAAM;AAE9B,QAAM,OAAO,IAAI,MAAM,CAAC;AACxB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,SAAK,IAAI,CAAC,IAAI,CAAC,EAAE,OAAQ,KAAK;AAAA,EAChC;AAEA,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,CAAC,KAAK,QAAQ;AAC9C,UAAM,MAAM,OAAO,GAAG;AACtB,QAAI,IAAI,QAAQ;AACd,UAAI,GAAG,IAAI,aAAa,MAAM,IAAI,OAAO,IAAI,MAAM;AAAA,IACrD,OAAO;AACL,UAAI,GAAG,IAAI,KAAK,IAAI,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;", "names": []}