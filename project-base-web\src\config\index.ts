/*
 * @Description:
 * @Version: 1.0
 * @Autor: lcm
 * @Date: 2025-07-21 09:26:54
 * @LastEditors: lcm
 * @LastEditTime: 2025-07-21 17:25:44
 */
let { host } = window.location;
const protocol = window.location?.protocol;
const wsProtocol = protocol === 'https:' ? 'wss' : 'ws';
if (host.includes('localhost')) {
  host = '***********';
}
const config = {
  tenantCode: '',
  hostUrl: `${protocol}//${host}`,
  appApi: `${protocol}//${host}/gateway`,
  wsApi: `${wsProtocol}://${host}/gateway/systemx/webSocket`,
  previewResourceUrl: `${protocol}//${host}/osr/resource/`,
  downloadUrl: `${protocol}//${host}/osr/temp/`,
};
window.baseConfig = config;
export default config;
