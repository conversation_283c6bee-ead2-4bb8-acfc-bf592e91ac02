{"version": 3, "sources": ["../../js-binary-schema-parser/src/index.js"], "sourcesContent": ["export const parse = (stream, schema, result = {}, parent = result) => {\r\n  if (Array.isArray(schema)) {\r\n    schema.forEach(partSchema => parse(stream, partSchema, result, parent))\r\n  } else if (typeof schema === 'function') {\r\n    schema(stream, result, parent, parse)\r\n  } else {\r\n    const key = Object.keys(schema)[0]\r\n    if (Array.isArray(schema[key])) {\r\n      parent[key] = {}\r\n      parse(stream, schema[key], result, parent[key])\r\n    } else {\r\n      parent[key] = schema[key](stream, result, parent, parse)\r\n    }\r\n  }\r\n  return result\r\n}\r\n\r\nexport const conditional = (schema, conditionFunc) => (\r\n  stream,\r\n  result,\r\n  parent,\r\n  parse\r\n) => {\r\n  if (conditionFunc(stream, result, parent)) {\r\n    parse(stream, schema, result, parent)\r\n  }\r\n}\r\n\r\nexport const loop = (schema, continueFunc) => (\r\n  stream,\r\n  result,\r\n  parent,\r\n  parse\r\n) => {\r\n  const arr = []\r\n  let lastStreamPos = stream.pos;\r\n  while (continueFunc(stream, result, parent)) {\r\n    const newParent = {}\r\n    parse(stream, schema, result, newParent)\r\n    // cases when whole file is parsed but no termination is there and stream position is not getting updated as well\r\n    // it falls into infinite recursion, null check to avoid the same\r\n    if(stream.pos === lastStreamPos) {\r\n      break\r\n    }\r\n    lastStreamPos = stream.pos\r\n    arr.push(newParent)\r\n  }\r\n  return arr\r\n}\r\n"], "mappings": ";AAAO,IAAM,QAAQ,CAAC,QAAQ,QAAQ,SAAS,CAAC,GAAG,SAAS,WAAW;AACrE,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,WAAO,QAAQ,gBAAc,MAAM,QAAQ,YAAY,QAAQ,MAAM,CAAC;AAAA,EACxE,WAAW,OAAO,WAAW,YAAY;AACvC,WAAO,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EACtC,OAAO;AACL,UAAM,MAAM,OAAO,KAAK,MAAM,EAAE,CAAC;AACjC,QAAI,MAAM,QAAQ,OAAO,GAAG,CAAC,GAAG;AAC9B,aAAO,GAAG,IAAI,CAAC;AACf,YAAM,QAAQ,OAAO,GAAG,GAAG,QAAQ,OAAO,GAAG,CAAC;AAAA,IAChD,OAAO;AACL,aAAO,GAAG,IAAI,OAAO,GAAG,EAAE,QAAQ,QAAQ,QAAQ,KAAK;AAAA,IACzD;AAAA,EACF;AACA,SAAO;AACT;AAEO,IAAM,cAAc,CAAC,QAAQ,kBAAkB,CACpD,QACA,QACA,QACAA,WACG;AACH,MAAI,cAAc,QAAQ,QAAQ,MAAM,GAAG;AACzC,IAAAA,OAAM,QAAQ,QAAQ,QAAQ,MAAM;AAAA,EACtC;AACF;AAEO,IAAM,OAAO,CAAC,QAAQ,iBAAiB,CAC5C,QACA,QACA,QACAA,WACG;AACH,QAAM,MAAM,CAAC;AACb,MAAI,gBAAgB,OAAO;AAC3B,SAAO,aAAa,QAAQ,QAAQ,MAAM,GAAG;AAC3C,UAAM,YAAY,CAAC;AACnB,IAAAA,OAAM,QAAQ,QAAQ,QAAQ,SAAS;AAGvC,QAAG,OAAO,QAAQ,eAAe;AAC/B;AAAA,IACF;AACA,oBAAgB,OAAO;AACvB,QAAI,KAAK,SAAS;AAAA,EACpB;AACA,SAAO;AACT;", "names": ["parse"]}