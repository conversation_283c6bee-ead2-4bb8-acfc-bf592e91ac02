{"version": 3, "sources": ["../../vue3-ts-jsoneditor/dist/styles-handler-nPP1xL5s.js"], "sourcesContent": ["const n = (t, o) => {\n  if (window != null && window.document.getElementById(t)) return;\n  const a = window == null ? void 0 : window.document.getElementsByTagName(\"head\")[0], e = window == null ? void 0 : window.document.createElement(\"style\");\n  e.setAttribute(\"id\", t), e.textContent = o, a.appendChild(e);\n}, d = async () => {\n  const { fullWidthButton: t } = await import(\"./string-styles-DT93GIY_.js\");\n  n(\"full-width-button\", t);\n}, l = async () => {\n  const { darkTheme: t } = await import(\"./string-styles-DT93GIY_.js\");\n  n(\"dark-theme\", t);\n};\nexport {\n  l as setDarkThemeStyle,\n  d as setFullWidthButtonStyle\n};\n"], "mappings": ";;;AAAA,IAAM,IAAI,CAAC,GAAG,MAAM;AAClB,MAAI,UAAU,QAAQ,OAAO,SAAS,eAAe,CAAC,EAAG;AACzD,QAAM,IAAI,UAAU,OAAO,SAAS,OAAO,SAAS,qBAAqB,MAAM,EAAE,CAAC,GAAG,IAAI,UAAU,OAAO,SAAS,OAAO,SAAS,cAAc,OAAO;AACxJ,IAAE,aAAa,MAAM,CAAC,GAAG,EAAE,cAAc,GAAG,EAAE,YAAY,CAAC;AAC7D;AAJA,IAIG,IAAI,YAAY;AACjB,QAAM,EAAE,iBAAiB,EAAE,IAAI,MAAM,OAAO,sCAA6B;AACzE,IAAE,qBAAqB,CAAC;AAC1B;AAPA,IAOG,IAAI,YAAY;AACjB,QAAM,EAAE,WAAW,EAAE,IAAI,MAAM,OAAO,sCAA6B;AACnE,IAAE,cAAc,CAAC;AACnB;", "names": []}