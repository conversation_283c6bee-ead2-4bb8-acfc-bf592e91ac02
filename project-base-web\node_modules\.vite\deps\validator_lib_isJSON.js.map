{"version": 3, "sources": ["../../validator/lib/isJSON.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isJSON;\nvar _assertString = _interopRequireDefault(require(\"./util/assertString\"));\nvar _merge = _interopRequireDefault(require(\"./util/merge\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar default_json_options = {\n  allow_primitives: false\n};\nfunction isJSON(str, options) {\n  (0, _assertString.default)(str);\n  try {\n    options = (0, _merge.default)(options, default_json_options);\n    var primitives = [];\n    if (options.allow_primitives) {\n      primitives = [null, false, true];\n    }\n    var obj = JSON.parse(str);\n    return primitives.includes(obj) || !!obj && _typeof(obj) === 'object';\n  } catch (e) {/* ignore */}\n  return false;\n}\nmodule.exports = exports.default;\nmodule.exports.default = exports.default;"], "mappings": ";;;;;;;;;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB,uBAAuB,sBAA8B;AACzE,QAAI,SAAS,uBAAuB,eAAuB;AAC3D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,aAAS,QAAQ,GAAG;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAAE,eAAO,OAAOA;AAAA,MAAG,IAAI,SAAUA,IAAG;AAAE,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAG,GAAG,QAAQ,CAAC;AAAA,IAAG;AAC7T,QAAI,uBAAuB;AAAA,MACzB,kBAAkB;AAAA,IACpB;AACA,aAAS,OAAO,KAAK,SAAS;AAC5B,OAAC,GAAG,cAAc,SAAS,GAAG;AAC9B,UAAI;AACF,mBAAW,GAAG,OAAO,SAAS,SAAS,oBAAoB;AAC3D,YAAI,aAAa,CAAC;AAClB,YAAI,QAAQ,kBAAkB;AAC5B,uBAAa,CAAC,MAAM,OAAO,IAAI;AAAA,QACjC;AACA,YAAI,MAAM,KAAK,MAAM,GAAG;AACxB,eAAO,WAAW,SAAS,GAAG,KAAK,CAAC,CAAC,OAAO,QAAQ,GAAG,MAAM;AAAA,MAC/D,SAAS,GAAG;AAAA,MAAa;AACzB,aAAO;AAAA,IACT;AACA,WAAO,UAAU,QAAQ;AACzB,WAAO,QAAQ,UAAU,QAAQ;AAAA;AAAA;", "names": ["o"]}