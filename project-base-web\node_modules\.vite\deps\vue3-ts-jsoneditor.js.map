{"version": 3, "sources": ["../../vue3-ts-jsoneditor/dist/index.mjs"], "sourcesContent": ["import { defineComponent as K, inject as G, ref as c, computed as h, reactive as X, watch as p, onMounted as Y, nextTick as $, onBeforeUnmount as Z, createElementBlock as ee, openBlock as te, withModifiers as ae, normalizeStyle as ne, normalizeClass as oe, renderSlot as re, createCommentVNode as ue } from \"vue\";\nimport { renderJSONSchemaEnum as we, renderValue as Be } from \"./vanilla-jsoneditor-CH8w8edm.js\";\nconst le = [\n  \"selection\",\n  \"mainMenuBar\",\n  \"navigationBar\",\n  \"statusBar\",\n  \"askToFormat\",\n  \"readOnly\",\n  \"indentation\",\n  \"tabSize\",\n  \"escapeControlCharacters\",\n  \"escapeUnicodeCharacters\",\n  \"flattenColumns\",\n  \"validator\",\n  \"onClassName\",\n  \"onRenderValue\",\n  \"onRenderMenu\"\n], se = [\n  \"selection\",\n  \"mode\",\n  \"mainMenuBar\",\n  \"navigationBar\",\n  \"statusBar\",\n  \"askToFormat\",\n  \"readOnly\",\n  \"indentation\",\n  \"tabSize\",\n  \"escapeControlCharacters\",\n  \"escapeUnicodeCharacters\",\n  \"flattenColumns\",\n  \"validator\",\n  \"parser\",\n  \"validationParser\",\n  \"pathParser\",\n  \"onClassName\",\n  \"onRenderValue\",\n  \"onRenderMenu\"\n], ie = (a = {}, s) => {\n  const u = {};\n  for (const n of se) {\n    const d = s[n] !== void 0 ? s[n] : a[n];\n    d !== void 0 && (u[n] = d);\n  }\n  return u;\n}, de = `\n  <svg \n    class=\"fa-icon svelte-1dof0an\" \n    viewBox=\"0 0 1024 1024\" \n    version=\"1.1\"\n     xmlns=\"http://www.w3.org/2000/svg\" \n     p-id=\"1927\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" \n     width=\"24\" \n     height=\"24\"\n  >\n    <path d=\"M63.989383 105.442494l0 268.396843c0 18.935258 15.368012 34.304294 34.304294 34.304294 18.936281 0 34.304294-15.369036 34.304294-34.304294L132.597971 180.156126l218.107483 218.176045c12.82919 12.830213 33.618679 12.830213 46.515407 0 12.830213-12.897751 12.830213-33.686217 0-46.51643l-218.176045-218.107483 193.683211 0c18.935258 0 34.304294-15.369036 34.304294-34.304294 0-18.935258-15.369036-34.304294-34.304294-34.304294L104.331183 65.09967C79.288834 65.09967 63.989383 77.999468 63.989383 105.442494L63.989383 105.442494z\" p-id=\"1928\" fill=\"#e6e6e6\"></path><path d=\"M917.688719 65.09967 649.290853 65.09967c-18.935258 0-34.304294 15.369036-34.304294 34.304294 0 18.936281 15.369036 34.304294 34.304294 34.304294l193.683211 0-218.176045 218.107483c-12.830213 12.82919-12.830213 33.618679 0 46.51643 12.897751 12.830213 33.686217 12.830213 46.515407 0L889.420909 180.156126l0 193.683211c0 18.935258 15.369036 34.304294 34.304294 34.304294 18.936281 0 34.304294-15.369036 34.304294-34.304294L958.029496 105.442494C958.029496 77.999468 942.79963 65.09967 917.688719 65.09967L917.688719 65.09967z\" p-id=\"1929\" fill=\"#e6e6e6\"></path>\n    <path d=\"M104.331183 957.013353l268.397866 0c18.935258 0 34.304294-15.368012 34.304294-34.304294 0-18.936281-15.369036-34.304294-34.304294-34.304294L179.045839 888.404766l218.176045-218.107483c12.830213-12.82919 12.830213-33.618679 0-46.515407-12.897751-12.830213-33.686217-12.830213-46.515407 0l-218.107483 218.176045L132.598994 648.27471c0-18.935258-15.368012-34.304294-34.304294-34.304294-18.936281 0-34.304294 15.369036-34.304294 34.304294l0 268.397866C63.989383 944.115602 79.288834 957.013353 104.331183 957.013353L104.331183 957.013353z\" p-id=\"1930\" fill=\"#e6e6e6\"></path>\n    <path d=\"M958.029496 916.671553 958.029496 648.27471c0-18.935258-15.368012-34.304294-34.304294-34.304294-18.935258 0-34.304294 15.369036-34.304294 34.304294l0 193.683211L671.313425 623.781876c-12.82919-12.830213-33.618679-12.830213-46.515407 0-12.830213 12.897751-12.830213 33.686217 0 46.515407l218.176045 218.107483L649.290853 888.404766c-18.935258 0-34.304294 15.368012-34.304294 34.304294 0 18.936281 15.369036 34.304294 34.304294 34.304294l268.397866 0C942.79963 957.013353 958.029496 944.115602 958.029496 916.671553L958.029496 916.671553z\" p-id=\"1931\" fill=\"#e6e6e6\"></path>\n  </svg>\n`, P = (a, s, u = !1) => {\n  const n = a && typeof a[s] < \"u\";\n  return !n && u && console.warn(\"Typeguard warn!!! The object has no property: \", s), n;\n}, ce = K({\n  name: \"JsonEditor\",\n  props: {\n    /**\n     * ### modelValue: unknown\n     * Pass the JSON value or string to be rendered in the JSONEditor.\n     * */\n    modelValue: [Object, Array, Number, String, Boolean, String, null],\n    /**\n     * ### value: unknown\n     * props value is an alternative to modelValue\n     * Pass the JSON value or string to be rendered in the JSONEditor.\n     * */\n    value: {\n      type: [Object, Array, Number, String, Boolean, String, null],\n      default: void 0\n    },\n    /**\n     * ### json: unknown\n     * Pass the JSON value to be rendered in the JSONEditor.\n     * */\n    json: {\n      type: [Object, Array, Number, String, Boolean, null],\n      default: void 0\n    },\n    /**\n     * ### text: string\n     * Pass the JSON string to be rendered in the JSONEditor.\n     * */\n    text: String,\n    /**\n     * ### jsonString: string\n     * Same as prop 'text'. Pass the JSON string to be rendered in the JSONEditor.\n     * */\n    jsonString: String,\n    /**\n     * ### selection: JSONEditorSelection | null.\n     * The current selected contents. You can use two-way binding using bind:selection. The tree mode\n     * supports MultiSelection, KeySelection, ValueSelection, InsideSelection, or AfterSelection. The\n     * table mode supports ValueSelection, and text mode supports TextSelection.\n     * */\n    selection: {\n      type: Object,\n      default: void 0\n    },\n    /**\n     * ### mode: 'tree' | 'text' | 'table'.\n     * Open the editor in 'tree' mode (default) or 'text' mode (formerly: code mode).\n     * */\n    mode: {\n      type: String,\n      default: \"tree\"\n    },\n    /**\n     * ### mainMenuBar: boolean\n     * Show the main menu bar. Default value is true.\n     * */\n    mainMenuBar: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### navigationBar: boolean\n     * Show the navigation bar with, where you can see the selected path and navigate through your\n     * document from there. Default value is true.\n     * */\n    navigationBar: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### statusBar: boolean\n     * Show a status bar at the bottom of the 'text' editor, showing information about the cursor\n     * location and selected contents. Default value is true.\n     * */\n    statusBar: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### askToFormat: boolean\n     * When true (default), the user will be asked whether he/she wants to format the JSON document\n     * when a compact document is loaded or pasted in 'text' mode. Only applicable to 'text' mode.\n     */\n    askToFormat: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### readOnly: boolean\n     * Open the editor in read-only mode: no changes can be made, non-relevant buttons are hidden\n     * from the menu, and the context menu is not enabled. Default value is false.\n     * */\n    readOnly: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### indentation: number | string\n     * Number of spaces use for indentation when stringifying JSON, or a string to be used as indentation\n     * like '\\t' to use a tab as indentation, or ' ' to use 4 spaces (which is equivalent to configuring\n     * indentation: 4). See also property tabSize.\n     * */\n    indentation: [String, Number],\n    /**\n     * ### tabSize: number\n     * When indentation is configured as a tab character (indentation: '\\t'), tabSize configures how\n     * large a tab character is rendered. Default value is 4. Only applicable to text mode.\n     * */\n    tabSize: Number,\n    /**\n     * ### escapeControlCharacters: boolean.\n     * False by default. When true, control characters like newline and tab are rendered as escaped\n     * characters \\n and \\t. Only applicable for 'tree' mode, in 'text' mode control characters are\n     * always escaped.\n     * */\n    escapeControlCharacters: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### escapeUnicodeCharacters: boolean.\n     * False by default. When true, unicode characters like ☎ and 😀 are rendered escaped\n     * like \\u260e and \\ud83d\\ude00.\n     * */\n    escapeUnicodeCharacters: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### flattenColumns: boolean.\n     * True by default. Only applicable to 'table' mode. When true, nested object properties\n     * will be displayed each in their own column, with the nested path as column name. When false,\n     * nested objects will be rendered inline, and double-clicking them will open them in a popup\n     * */\n    flattenColumns: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### validator: function (json: unknown): ValidationError[].\n     * Validate the JSON document. For example use the built-in JSON Schema validator\n     * powered by Ajv:\n     * ```ts\n     *  import { createAjvValidator } from 'svelte-jsoneditor'\n     *  const validator = createAjvValidator(schema, schemaDefinitions)\n     * ```\n     * */\n    validator: Function,\n    /**\n     * ### parser: JSON = JSON\n     * Configure a custom JSON parser, like lossless-json. By default, the native JSON\n     * parser of JavaScript is used. The JSON interface is an object with a parse and\n     * stringify function.\n     * */\n    parser: Object,\n    /**\n     * ### validationParser: JSONParser = JSON\n     * Only applicable when a validator is provided. This is the same as parser, except\n     * that this parser is used to parse the data before sending it to the validator.\n     * Configure a custom JSON parser that is used to parse JSON before passing it to the\n     * validator. By default, the built-in JSON parser is used. When passing a custom\n     * validationParser, make sure the output of the parser is supported by the configured\n     * validator. So, when the validationParser can output bigint numbers or other numeric\n     * types, the validator must also support that. In tree mode, when parser is not equal\n     * to validationParser, the JSON document will be converted before it is passed to the\n     * validator via validationParser.parse(parser.stringify(json))\n     * */\n    validationParser: Object,\n    /**\n     * ### pathParser: JSONPathParser\n     * An optional object with a parse and stringify method to parse and stringify a JSONPath,\n     * which is an array with property names. The pathParser is used in the path editor in the\n     * navigation bar, which is opened by clicking the edit button on the right side of the\n     * navigation bar. The pathParser.parse function is allowed to throw an Error when the input\n     * is invalid. By default, a JSON Path notation is used, which looks like $.data[2].nested.property.\n     * Alternatively, it is possible to use for example a JSON Pointer notation\n     * like /data/2/nested/property or something custom-made. Related helper functions:\n     * parseJSONPath and stringifyJSONPath, parseJSONPointer and compileJSONPointer\n     * */\n    pathParser: Object,\n    /**\n     * ### queryLanguagesIds: QueryLanguageId[].\n     * Configure one or multiple query language that can be used in the Transform modal.\n     * An array of available query languages id's\n     * [javascript', 'lodash', 'jmespath']\n     * */\n    queryLanguagesIds: Array,\n    /**\n     * ### queryLanguageId: string.\n     * The id of the currently selected query language.\n     * 'javascript' | 'lodash' | 'jmespath'\n     * */\n    queryLanguageId: String,\n    /**\n     * ### onClassName(path: Path, value: any): string | undefined.\n     * Add a custom class name to specific nodes, based on their path and/or value.\n     * */\n    onClassName: Function,\n    /**\n     * ### onRenderValue(props: RenderValueProps) : RenderValueComponentDescription[]\n     *\n     * ## EXPERIMENTAL! This API will most likely change in future versions.\n     * Customize rendering of the values. By default, renderValue is used, which renders a value as an\n     * editable div and depending on the value can also render a boolean toggle, a color picker, and a\n     * timestamp tag. Multiple components can be rendered alongside each other, like the boolean toggle\n     * and color picker being rendered left from the editable div. Built in value renderer components:\n     *\n     *  > EditableValue, ReadonlyValue, BooleanToggle, ColorPicker, TimestampTag, EnumValue.\n     *\n     *\n     * ```ts\n     *  import { renderJSONSchemaEnum, renderValue } from 'svelte-jsoneditor'\n     *\n     *  function onRenderValue(props) {\n     *    // use the enum renderer, and fallback on the default renderer\n     *    return renderJSONSchemaEnum(props, schema, schemaDefinitions) || renderValue(props)\n     *  }\n     * ```\n     * */\n    onRenderValue: Function,\n    /**\n     * ### onRenderMenu(items: MenuItem[], context: { mode: 'tree' | 'text' | 'table', modal: boolean }) : MenuItem[] | undefined.\n     * Callback which can be used to make changes to the menu items. New items can be added, or existing items can be removed or\n     * reorganized. When the function returns undefined, the original items will be applied. Using the context values mode and\n     * modal, different actions can be taken depending on the mode of the editor and whether the editor is rendered inside a\n     * modal or not.\n     *\n     *  A menu item MenuItem can be one of the following types:\n     *\n     *  - Button:\n     *  ```ts\n     *  interface MenuButtonItem {\n     *    onClick: () => void\n     *    icon?: FontAwesomeIcon\n     *    text?: string\n     *    title?: string\n     *    className?: string\n     *    disabled?: boolean\n     *  }\n     *  ```\n     *\n     *  - Separator (gray vertical line between a group of items):\n     *  ```ts\n     *    interface MenuSeparatorItem {\n     *      separator: true\n     *    }\n     *  ```\n     *\n     *  - Space (fills up empty space):\n     *  ```ts\n     *    interface MenuSpaceItem {\n     *      space: true\n     *    }\n     *  ```\n     * */\n    onRenderMenu: Function,\n    /**\n     * ### height: string | number\n     * Height of render container\n     * */\n    height: [String, Number],\n    /**\n     * ### fullWidthButton: boolean\n     * Show full screen button\n     * */\n    fullWidthButton: {\n      type: Boolean,\n      default: void 0\n    },\n    /**\n     * ### darkTheme: boolean\n     * Switch to dark theme\n     * */\n    darkTheme: {\n      type: Boolean,\n      default: void 0\n    }\n  },\n  emits: [\n    \"update:modelValue\",\n    \"update:json\",\n    \"update:text\",\n    \"update:jsonString\",\n    \"update:selection\",\n    \"change\",\n    \"error\",\n    \"change-mode\",\n    \"update:mode\",\n    \"change-query-language\",\n    \"focus\",\n    \"blur\"\n  ],\n  setup(a, { expose: s, emit: u }) {\n    const n = G(\"jsonEditorOptions\", {}), d = c(), i = c(null), g = c(!1), m = c(!1), y = c(!1), v = c(\"tree\"), r = c(null), T = h(() => {\n      const e = a.height || (n == null ? void 0 : n.height);\n      return e && !g.value ? {\n        height: e + \"px\"\n      } : {};\n    }), B = h(() => a.darkTheme || (n == null ? void 0 : n.darkTheme)), w = h(() => a.queryLanguagesIds || (n == null ? void 0 : n.queryLanguagesIds)), N = h(() => a.queryLanguageId || (n == null ? void 0 : n.queryLanguageId)), f = X({}), V = async () => {\n      var t;\n      if (typeof window > \"u\" || typeof w.value > \"u\" || !((t = w.value) != null && t.length))\n        return;\n      for (const l of w.value)\n        if (!f[l])\n          switch (l) {\n            case \"javascript\": {\n              const { javascriptQueryLanguage: o } = await import(\"./vanilla-jsoneditor-CH8w8edm.js\");\n              f[l] = o;\n              break;\n            }\n            case \"lodash\": {\n              const { lodashQueryLanguage: o } = await import(\"./vanilla-jsoneditor-CH8w8edm.js\");\n              f[l] = o;\n              break;\n            }\n            case \"jmespath\": {\n              const { jmespathQueryLanguage: o } = await import(\"./vanilla-jsoneditor-CH8w8edm.js\");\n              f[l] = o;\n              break;\n            }\n            // Add these new languages:\n            case \"jsonquery\": {\n              const { jsonQueryLanguage: o } = await import(\"./vanilla-jsoneditor-CH8w8edm.js\");\n              f[l] = o;\n              break;\n            }\n            case \"jsonpath\": {\n              const { jsonpathQueryLanguage: o } = await import(\"./vanilla-jsoneditor-CH8w8edm.js\");\n              f[l] = o;\n              break;\n            }\n          }\n      const e = Object.values(f);\n      if (e.length !== 0)\n        return e;\n    }, L = () => {\n      i.value && (i.value.removeEventListener(\"click\", b), i.value = null);\n    }, q = async () => {\n      if (typeof window > \"u\") return;\n      const { getElement: e, createElement: t } = await import(\"./full-width-button-handler-D8h6mHdo.js\"), { setFullWidthButtonStyle: l } = await import(\"./styles-handler-nPP1xL5s.js\");\n      await l();\n      const o = e(\".jse-full-width\"), O = (n == null ? void 0 : n.fullWidthButton) !== void 0 ? n == null ? void 0 : n.fullWidthButton : !0;\n      if (!(a.fullWidthButton !== void 0 ? a.fullWidthButton : O) || o) return;\n      i.value && L();\n      const x = e(\".jse-menu\"), D = Array.from(x.classList).find((H) => H.startsWith(\"svelte-\"));\n      i.value = t(\"button\"), i.value.classList.add(\"jse-full-width\"), i.value.classList.add(\"jse-button\"), i.value.classList.add(D), i.value.innerHTML += de, x.appendChild(i.value), i.value.addEventListener(\"click\", b);\n    }, b = () => {\n      var e, t;\n      g.value = !g.value, g.value ? (e = i.value) == null || e.classList.add(\"jse-full-width--active\") : (t = i.value) == null || t.classList.remove(\"jse-full-width--active\");\n    }, F = (e, t, l) => {\n      if (y.value) {\n        y.value = !1;\n        return;\n      }\n      m.value = !0, P(e, \"json\") && (u(\"update:json\", e.json), u(\"update:modelValue\", e.json)), P(e, \"text\") && (u(\"update:text\", e.text), u(\"update:jsonString\", e.text), u(\"update:modelValue\", e.text)), u(\"change\", e, t, l);\n    }, M = (e) => {\n      u(\"error\", e);\n    }, W = (e) => {\n      v.value = e, u(\"change-mode\", e), u(\"update:mode\", e);\n    }, I = (e) => {\n      u(\"change-query-language\", e);\n    }, A = () => {\n      u(\"focus\");\n    }, R = () => {\n      u(\"blur\");\n    }, J = (e) => {\n      u(\"update:selection\", e);\n    }, z = (e, t) => ($(() => {\n      q();\n    }), typeof a.onRenderMenu == \"function\" ? a.onRenderMenu(e, t) : e), j = async () => {\n      const e = { fullWidthButton: !0, ...n || {} }, t = await V();\n      return {\n        ...ie(e, a),\n        queryLanguages: t,\n        queryLanguageId: N.value,\n        onChange: F,\n        onError: M,\n        onChangeMode: W,\n        onChangeQueryLanguage: I,\n        onFocus: A,\n        onBlur: R,\n        onRenderMenu: z,\n        onSelect: J\n      };\n    }, k = c(!0), S = () => {\n      const e = (o = {}) => o === null || typeof o > \"u\" || typeof o == \"number\" || typeof o == \"bigint\" || typeof o == \"string\" || typeof o == \"boolean\" ? {\n        json: o\n      } : Array.isArray(o) ? {\n        json: [...o]\n      } : {\n        json: { ...o }\n      }, t = (o = \"\") => ({\n        text: o || \"\"\n      }), l = a.modelValue || a.value;\n      return l ? v.value === \"text\" && typeof l == \"string\" ? t(l) : e(l) : typeof a.json < \"u\" ? e(a.json) : typeof a.text < \"u\" ? t(a.text) : typeof a.jsonString < \"u\" ? t(a.jsonString) : t();\n    }, Q = async () => {\n      if (!(typeof window > \"u\")) {\n        if (!r.value) {\n          const e = await j(), { createJSONEditor: t } = await import(\"./vanilla-jsoneditor-CH8w8edm.js\");\n          k.value = !1, r.value = t({\n            target: d.value,\n            props: e\n          }), r.value.set(S());\n        }\n        r.value.focus();\n      }\n    }, C = async () => {\n      var t;\n      const e = await j();\n      (t = r.value) == null || t.updateProps(e);\n    }, U = () => {\n      var e;\n      if (m.value) {\n        m.value = !1;\n        return;\n      }\n      y.value = !0, (e = r.value) == null || e.update(S());\n    }, _ = () => {\n      r.value && (r.value.destroy(), r.value = null), L();\n    };\n    return p(\n      [\n        ...le.map((e) => () => a[e])\n      ],\n      C,\n      { deep: !0 }\n    ), p(\n      [() => a.modelValue, () => a.value, () => a.json, () => a.text, () => a.jsonString],\n      U,\n      {\n        deep: !0\n      }\n    ), p(\n      () => a.mode,\n      (e) => {\n        e !== v.value && (v.value = e, C());\n      }\n    ), p(\n      () => B.value,\n      async (e) => {\n        if (e) {\n          const { setDarkThemeStyle: t } = await import(\"./styles-handler-nPP1xL5s.js\");\n          await t();\n        }\n      },\n      { immediate: !0 }\n    ), Y(() => {\n      $(() => {\n        Q();\n      });\n    }), Z(() => {\n      _();\n    }), s({\n      $collapseAll() {\n        var e;\n        v.value === \"tree\" && ((e = r.value) == null || e.collapse([], !0));\n      },\n      $expandAll() {\n        var e;\n        v.value === \"tree\" && ((e = r.value) == null || e.expand([]));\n      },\n      $expand(e, t) {\n        var l, o;\n        typeof e == \"function\" ? ((l = r.value) == null || l.expand([], e), console.warn(\n          \"In new API you must pass the path before the callback!!! Backwards compatibility is deprecated and will be discontinued in the future\"\n        )) : (o = r.value) == null || o.expand(e, t);\n      },\n      $get() {\n        var e;\n        return (e = r.value) == null ? void 0 : e.get();\n      },\n      $set(e) {\n        var t;\n        (t = r.value) == null || t.set(e);\n      },\n      $update(e) {\n        var t;\n        (t = r.value) == null || t.update(e);\n      },\n      $updateProps(e) {\n        var t;\n        (t = r.value) == null || t.updateProps(e);\n      },\n      async $refresh() {\n        var e;\n        await ((e = r.value) == null ? void 0 : e.refresh());\n      },\n      $focus() {\n        var e;\n        (e = r.value) == null || e.focus();\n      },\n      async $destroy() {\n        var e;\n        await ((e = r.value) == null ? void 0 : e.destroy());\n      },\n      $patch(e) {\n        var t;\n        return (t = r.value) == null ? void 0 : t.patch(e);\n      },\n      $transform(e) {\n        var t;\n        (t = r.value) == null || t.transform(e);\n      },\n      async $scrollTo(e) {\n        var t;\n        await ((t = r.value) == null ? void 0 : t.scrollTo(e));\n      },\n      $findElement(e) {\n        var t;\n        return (t = r.value) == null ? void 0 : t.findElement(e);\n      },\n      $acceptAutoRepair() {\n        var e;\n        return (e = r.value) == null ? void 0 : e.acceptAutoRepair();\n      },\n      $validate() {\n        var e;\n        return (e = r.value) == null ? void 0 : e.validate();\n      }\n    }), {\n      max: g,\n      getHeight: T,\n      container: d,\n      darkThemeStyle: B,\n      fallbackSlot: k\n    };\n  }\n}), fe = (a, s) => {\n  const u = a.__vccOpts || a;\n  for (const [n, d] of s)\n    u[n] = d;\n  return u;\n};\nfunction ve(a, s, u, n, d, i) {\n  return te(), ee(\"div\", {\n    class: oe([\"vue-ts-json-editor\", { \"vue-ts-json-editor--max-box\": a.max, \"jse-theme-dark\": a.darkThemeStyle }]),\n    style: ne(a.getHeight),\n    ref: \"container\",\n    onKeydown: s[0] || (s[0] = ae(() => {\n    }, [\"stop\"]))\n  }, [\n    a.fallbackSlot ? re(a.$slots, \"default\", { key: 0 }) : ue(\"\", !0)\n  ], 38);\n}\nconst E = /* @__PURE__ */ fe(ce, [[\"render\", ve]]);\nE.install = () => {\n  console.error(`Default import not working!!! Use \"import {JsonEditorPlugin} from 'vue3-ts-jsoneditor';\"`);\n};\nconst pe = {\n  install(a, s = {}) {\n    a.config.globalProperties.$_vue3TsJsoneditor || (a.config.globalProperties.$_vue3TsJsoneditor = !0, a.component(s.componentName || \"JsonEditor\", E), a.provide(\"jsonEditorOptions\", s.options));\n  }\n};\nexport {\n  pe as JsonEditorPlugin,\n  E as default,\n  we as renderJSONSchemaEnum,\n  Be as renderValue\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,KAAK;AAAA,EACT;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAhBA,IAgBG,KAAK;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AApCA,IAoCG,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM;AACrB,QAAM,IAAI,CAAC;AACX,aAAW,KAAK,IAAI;AAClB,UAAM,IAAI,EAAE,CAAC,MAAM,SAAS,EAAE,CAAC,IAAI,EAAE,CAAC;AACtC,UAAM,WAAW,EAAE,CAAC,IAAI;AAAA,EAC1B;AACA,SAAO;AACT;AA3CA,IA2CG,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AA3CR,IAyDG,IAAI,CAAC,GAAG,GAAG,IAAI,UAAO;AACvB,QAAM,IAAI,KAAK,OAAO,EAAE,CAAC,IAAI;AAC7B,SAAO,CAAC,KAAK,KAAK,QAAQ,KAAK,kDAAkD,CAAC,GAAG;AACvF;AA5DA,IA4DG,KAAK,gBAAE;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,IAKL,YAAY,CAAC,QAAQ,OAAO,QAAQ,QAAQ,SAAS,QAAQ,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMjE,OAAO;AAAA,MACL,MAAM,CAAC,QAAQ,OAAO,QAAQ,QAAQ,SAAS,QAAQ,IAAI;AAAA,MAC3D,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM;AAAA,MACJ,MAAM,CAAC,QAAQ,OAAO,QAAQ,QAAQ,SAAS,IAAI;AAAA,MACnD,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,IAKN,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOZ,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,MAAM;AAAA,MACJ,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,aAAa;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,UAAU;AAAA,MACR,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,aAAa,CAAC,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAM5B,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOT,yBAAyB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMA,yBAAyB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,gBAAgB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUA,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOX,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAaR,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAYlB,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOZ,mBAAmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAMnB,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA,IAKjB,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAsBb,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAoCf,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA,IAKd,QAAQ,CAAC,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA,IAKvB,iBAAiB;AAAA,MACf,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA;AAAA;AAAA;AAAA;AAAA,IAKA,WAAW;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG;AAC/B,UAAM,IAAI,OAAE,qBAAqB,CAAC,CAAC,GAAG,IAAI,IAAE,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,KAAE,GAAG,IAAI,IAAE,MAAM,GAAG,IAAI,IAAE,IAAI,GAAG,IAAI,SAAE,MAAM;AACnI,YAAM,IAAI,EAAE,WAAW,KAAK,OAAO,SAAS,EAAE;AAC9C,aAAO,KAAK,CAAC,EAAE,QAAQ;AAAA,QACrB,QAAQ,IAAI;AAAA,MACd,IAAI,CAAC;AAAA,IACP,CAAC,GAAG,IAAI,SAAE,MAAM,EAAE,cAAc,KAAK,OAAO,SAAS,EAAE,UAAU,GAAG,IAAI,SAAE,MAAM,EAAE,sBAAsB,KAAK,OAAO,SAAS,EAAE,kBAAkB,GAAG,IAAI,SAAE,MAAM,EAAE,oBAAoB,KAAK,OAAO,SAAS,EAAE,gBAAgB,GAAG,IAAI,SAAE,CAAC,CAAC,GAAG,IAAI,YAAY;AACzP,UAAI;AACJ,UAAI,OAAO,SAAS,OAAO,OAAO,EAAE,QAAQ,OAAO,GAAG,IAAI,EAAE,UAAU,QAAQ,EAAE;AAC9E;AACF,iBAAW,KAAK,EAAE;AAChB,YAAI,CAAC,EAAE,CAAC;AACN,kBAAQ,GAAG;AAAA,YACT,KAAK,cAAc;AACjB,oBAAM,EAAE,yBAAyB,EAAE,IAAI,MAAM,OAAO,2CAAkC;AACtF,gBAAE,CAAC,IAAI;AACP;AAAA,YACF;AAAA,YACA,KAAK,UAAU;AACb,oBAAM,EAAE,qBAAqB,EAAE,IAAI,MAAM,OAAO,2CAAkC;AAClF,gBAAE,CAAC,IAAI;AACP;AAAA,YACF;AAAA,YACA,KAAK,YAAY;AACf,oBAAM,EAAE,uBAAuB,EAAE,IAAI,MAAM,OAAO,2CAAkC;AACpF,gBAAE,CAAC,IAAI;AACP;AAAA,YACF;AAAA,YAEA,KAAK,aAAa;AAChB,oBAAM,EAAE,mBAAmB,EAAE,IAAI,MAAM,OAAO,2CAAkC;AAChF,gBAAE,CAAC,IAAI;AACP;AAAA,YACF;AAAA,YACA,KAAK,YAAY;AACf,oBAAM,EAAE,uBAAuB,EAAE,IAAI,MAAM,OAAO,2CAAkC;AACpF,gBAAE,CAAC,IAAI;AACP;AAAA,YACF;AAAA,UACF;AACJ,YAAM,IAAI,OAAO,OAAO,CAAC;AACzB,UAAI,EAAE,WAAW;AACf,eAAO;AAAA,IACX,GAAG,IAAI,MAAM;AACX,QAAE,UAAU,EAAE,MAAM,oBAAoB,SAAS,CAAC,GAAG,EAAE,QAAQ;AAAA,IACjE,GAAG,IAAI,YAAY;AACjB,UAAI,OAAO,SAAS,IAAK;AACzB,YAAM,EAAE,YAAY,GAAG,eAAe,EAAE,IAAI,MAAM,OAAO,kDAAyC,GAAG,EAAE,yBAAyB,EAAE,IAAI,MAAM,OAAO,uCAA8B;AACjL,YAAM,EAAE;AACR,YAAM,IAAI,EAAE,iBAAiB,GAAG,KAAK,KAAK,OAAO,SAAS,EAAE,qBAAqB,SAAS,KAAK,OAAO,SAAS,EAAE,kBAAkB;AACnI,UAAI,EAAE,EAAE,oBAAoB,SAAS,EAAE,kBAAkB,MAAM,EAAG;AAClE,QAAE,SAAS,EAAE;AACb,YAAM,IAAI,EAAE,WAAW,GAAG,IAAI,MAAM,KAAK,EAAE,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,WAAW,SAAS,CAAC;AACzF,QAAE,QAAQ,EAAE,QAAQ,GAAG,EAAE,MAAM,UAAU,IAAI,gBAAgB,GAAG,EAAE,MAAM,UAAU,IAAI,YAAY,GAAG,EAAE,MAAM,UAAU,IAAI,CAAC,GAAG,EAAE,MAAM,aAAa,IAAI,EAAE,YAAY,EAAE,KAAK,GAAG,EAAE,MAAM,iBAAiB,SAAS,CAAC;AAAA,IACrN,GAAG,IAAI,MAAM;AACX,UAAI,GAAG;AACP,QAAE,QAAQ,CAAC,EAAE,OAAO,EAAE,SAAS,IAAI,EAAE,UAAU,QAAQ,EAAE,UAAU,IAAI,wBAAwB,KAAK,IAAI,EAAE,UAAU,QAAQ,EAAE,UAAU,OAAO,wBAAwB;AAAA,IACzK,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM;AAClB,UAAI,EAAE,OAAO;AACX,UAAE,QAAQ;AACV;AAAA,MACF;AACA,QAAE,QAAQ,MAAI,EAAE,GAAG,MAAM,MAAM,EAAE,eAAe,EAAE,IAAI,GAAG,EAAE,qBAAqB,EAAE,IAAI,IAAI,EAAE,GAAG,MAAM,MAAM,EAAE,eAAe,EAAE,IAAI,GAAG,EAAE,qBAAqB,EAAE,IAAI,GAAG,EAAE,qBAAqB,EAAE,IAAI,IAAI,EAAE,UAAU,GAAG,GAAG,CAAC;AAAA,IAC3N,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,SAAS,CAAC;AAAA,IACd,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,QAAQ,GAAG,EAAE,eAAe,CAAC,GAAG,EAAE,eAAe,CAAC;AAAA,IACtD,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,yBAAyB,CAAC;AAAA,IAC9B,GAAG,IAAI,MAAM;AACX,QAAE,OAAO;AAAA,IACX,GAAG,IAAI,MAAM;AACX,QAAE,MAAM;AAAA,IACV,GAAG,IAAI,CAAC,MAAM;AACZ,QAAE,oBAAoB,CAAC;AAAA,IACzB,GAAG,IAAI,CAAC,GAAG,OAAO,SAAE,MAAM;AACxB,QAAE;AAAA,IACJ,CAAC,GAAG,OAAO,EAAE,gBAAgB,aAAa,EAAE,aAAa,GAAG,CAAC,IAAI,IAAI,IAAI,YAAY;AACnF,YAAM,IAAI,EAAE,iBAAiB,MAAI,GAAG,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,EAAE;AAC3D,aAAO;AAAA,QACL,GAAG,GAAG,GAAG,CAAC;AAAA,QACV,gBAAgB;AAAA,QAChB,iBAAiB,EAAE;AAAA,QACnB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,cAAc;AAAA,QACd,uBAAuB;AAAA,QACvB,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,UAAU;AAAA,MACZ;AAAA,IACF,GAAG,IAAI,IAAE,IAAE,GAAG,IAAI,MAAM;AACtB,YAAM,IAAI,CAAC,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,IAAI,OAAO,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY,OAAO,KAAK,YAAY;AAAA,QACpJ,MAAM;AAAA,MACR,IAAI,MAAM,QAAQ,CAAC,IAAI;AAAA,QACrB,MAAM,CAAC,GAAG,CAAC;AAAA,MACb,IAAI;AAAA,QACF,MAAM,EAAE,GAAG,EAAE;AAAA,MACf,GAAG,IAAI,CAAC,IAAI,QAAQ;AAAA,QAClB,MAAM,KAAK;AAAA,MACb,IAAI,IAAI,EAAE,cAAc,EAAE;AAC1B,aAAO,IAAI,EAAE,UAAU,UAAU,OAAO,KAAK,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,OAAO,EAAE,OAAO,MAAM,EAAE,EAAE,IAAI,IAAI,OAAO,EAAE,OAAO,MAAM,EAAE,EAAE,IAAI,IAAI,OAAO,EAAE,aAAa,MAAM,EAAE,EAAE,UAAU,IAAI,EAAE;AAAA,IAC5L,GAAG,IAAI,YAAY;AACjB,UAAI,EAAE,OAAO,SAAS,MAAM;AAC1B,YAAI,CAAC,EAAE,OAAO;AACZ,gBAAM,IAAI,MAAM,EAAE,GAAG,EAAE,kBAAkB,EAAE,IAAI,MAAM,OAAO,2CAAkC;AAC9F,YAAE,QAAQ,OAAI,EAAE,QAAQ,EAAE;AAAA,YACxB,QAAQ,EAAE;AAAA,YACV,OAAO;AAAA,UACT,CAAC,GAAG,EAAE,MAAM,IAAI,EAAE,CAAC;AAAA,QACrB;AACA,UAAE,MAAM,MAAM;AAAA,MAChB;AAAA,IACF,GAAG,IAAI,YAAY;AACjB,UAAI;AACJ,YAAM,IAAI,MAAM,EAAE;AAClB,OAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,YAAY,CAAC;AAAA,IAC1C,GAAG,IAAI,MAAM;AACX,UAAI;AACJ,UAAI,EAAE,OAAO;AACX,UAAE,QAAQ;AACV;AAAA,MACF;AACA,QAAE,QAAQ,OAAK,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO,EAAE,CAAC;AAAA,IACrD,GAAG,IAAI,MAAM;AACX,QAAE,UAAU,EAAE,MAAM,QAAQ,GAAG,EAAE,QAAQ,OAAO,EAAE;AAAA,IACpD;AACA,WAAO;AAAA,MACL;AAAA,QACE,GAAG,GAAG,IAAI,CAAC,MAAM,MAAM,EAAE,CAAC,CAAC;AAAA,MAC7B;AAAA,MACA;AAAA,MACA,EAAE,MAAM,KAAG;AAAA,IACb,GAAG;AAAA,MACD,CAAC,MAAM,EAAE,YAAY,MAAM,EAAE,OAAO,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,EAAE,UAAU;AAAA,MAClF;AAAA,MACA;AAAA,QACE,MAAM;AAAA,MACR;AAAA,IACF,GAAG;AAAA,MACD,MAAM,EAAE;AAAA,MACR,CAAC,MAAM;AACL,cAAM,EAAE,UAAU,EAAE,QAAQ,GAAG,EAAE;AAAA,MACnC;AAAA,IACF,GAAG;AAAA,MACD,MAAM,EAAE;AAAA,MACR,OAAO,MAAM;AACX,YAAI,GAAG;AACL,gBAAM,EAAE,mBAAmB,EAAE,IAAI,MAAM,OAAO,uCAA8B;AAC5E,gBAAM,EAAE;AAAA,QACV;AAAA,MACF;AAAA,MACA,EAAE,WAAW,KAAG;AAAA,IAClB,GAAG,UAAE,MAAM;AACT,eAAE,MAAM;AACN,UAAE;AAAA,MACJ,CAAC;AAAA,IACH,CAAC,GAAG,gBAAE,MAAM;AACV,QAAE;AAAA,IACJ,CAAC,GAAG,EAAE;AAAA,MACJ,eAAe;AACb,YAAI;AACJ,UAAE,UAAU,YAAY,IAAI,EAAE,UAAU,QAAQ,EAAE,SAAS,CAAC,GAAG,IAAE;AAAA,MACnE;AAAA,MACA,aAAa;AACX,YAAI;AACJ,UAAE,UAAU,YAAY,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO,CAAC,CAAC;AAAA,MAC7D;AAAA,MACA,QAAQ,GAAG,GAAG;AACZ,YAAI,GAAG;AACP,eAAO,KAAK,eAAe,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ;AAAA,UAC1E;AAAA,QACF,MAAM,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO,GAAG,CAAC;AAAA,MAC7C;AAAA,MACA,OAAO;AACL,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,IAAI;AAAA,MAChD;AAAA,MACA,KAAK,GAAG;AACN,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,IAAI,CAAC;AAAA,MAClC;AAAA,MACA,QAAQ,GAAG;AACT,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,OAAO,CAAC;AAAA,MACrC;AAAA,MACA,aAAa,GAAG;AACd,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,YAAY,CAAC;AAAA,MAC1C;AAAA,MACA,MAAM,WAAW;AACf,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,QAAQ;AAAA,MACpD;AAAA,MACA,SAAS;AACP,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,MAAM;AAAA,MACnC;AAAA,MACA,MAAM,WAAW;AACf,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,QAAQ;AAAA,MACpD;AAAA,MACA,OAAO,GAAG;AACR,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,MAAM,CAAC;AAAA,MACnD;AAAA,MACA,WAAW,GAAG;AACZ,YAAI;AACJ,SAAC,IAAI,EAAE,UAAU,QAAQ,EAAE,UAAU,CAAC;AAAA,MACxC;AAAA,MACA,MAAM,UAAU,GAAG;AACjB,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,SAAS,CAAC;AAAA,MACtD;AAAA,MACA,aAAa,GAAG;AACd,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,YAAY,CAAC;AAAA,MACzD;AAAA,MACA,oBAAoB;AAClB,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,iBAAiB;AAAA,MAC7D;AAAA,MACA,YAAY;AACV,YAAI;AACJ,gBAAQ,IAAI,EAAE,UAAU,OAAO,SAAS,EAAE,SAAS;AAAA,MACrD;AAAA,IACF,CAAC,GAAG;AAAA,MACF,KAAK;AAAA,MACL,WAAW;AAAA,MACX,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc;AAAA,IAChB;AAAA,EACF;AACF,CAAC;AA5kBD,IA4kBI,KAAK,CAAC,GAAG,MAAM;AACjB,QAAM,IAAI,EAAE,aAAa;AACzB,aAAW,CAAC,GAAG,CAAC,KAAK;AACnB,MAAE,CAAC,IAAI;AACT,SAAO;AACT;AACA,SAAS,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5B,SAAO,UAAG,GAAG,mBAAG,OAAO;AAAA,IACrB,OAAO,eAAG,CAAC,sBAAsB,EAAE,+BAA+B,EAAE,KAAK,kBAAkB,EAAE,eAAe,CAAC,CAAC;AAAA,IAC9G,OAAO,eAAG,EAAE,SAAS;AAAA,IACrB,KAAK;AAAA,IACL,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,cAAG,MAAM;AAAA,IACpC,GAAG,CAAC,MAAM,CAAC;AAAA,EACb,GAAG;AAAA,IACD,EAAE,eAAe,WAAG,EAAE,QAAQ,WAAW,EAAE,KAAK,EAAE,CAAC,IAAI,mBAAG,IAAI,IAAE;AAAA,EAClE,GAAG,EAAE;AACP;AACA,IAAM,IAAoB,GAAG,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC;AACjD,EAAE,UAAU,MAAM;AAChB,UAAQ,MAAM,0FAA0F;AAC1G;AACA,IAAM,KAAK;AAAA,EACT,QAAQ,GAAG,IAAI,CAAC,GAAG;AACjB,MAAE,OAAO,iBAAiB,uBAAuB,EAAE,OAAO,iBAAiB,qBAAqB,MAAI,EAAE,UAAU,EAAE,iBAAiB,cAAc,CAAC,GAAG,EAAE,QAAQ,qBAAqB,EAAE,OAAO;AAAA,EAC/L;AACF;", "names": []}