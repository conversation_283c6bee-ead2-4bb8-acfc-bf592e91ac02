{"version": 3, "sources": ["../../validator/lib/isFQDN.js", "../../validator/lib/isIP.js", "../../validator/lib/isURL.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isFQDN;\nvar _assertString = _interopRequireDefault(require(\"./util/assertString\"));\nvar _merge = _interopRequireDefault(require(\"./util/merge\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nvar default_fqdn_options = {\n  require_tld: true,\n  allow_underscores: false,\n  allow_trailing_dot: false,\n  allow_numeric_tld: false,\n  allow_wildcard: false,\n  ignore_max_length: false\n};\nfunction isFQDN(str, options) {\n  (0, _assertString.default)(str);\n  options = (0, _merge.default)(options, default_fqdn_options);\n\n  /* Remove the optional trailing dot before checking validity */\n  if (options.allow_trailing_dot && str[str.length - 1] === '.') {\n    str = str.substring(0, str.length - 1);\n  }\n\n  /* Remove the optional wildcard before checking validity */\n  if (options.allow_wildcard === true && str.indexOf('*.') === 0) {\n    str = str.substring(2);\n  }\n  var parts = str.split('.');\n  var tld = parts[parts.length - 1];\n  if (options.require_tld) {\n    // disallow fqdns without tld\n    if (parts.length < 2) {\n      return false;\n    }\n    if (!options.allow_numeric_tld && !/^([a-z\\u00A1-\\u00A8\\u00AA-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFEF]{2,}|xn[a-z0-9-]{2,})$/i.test(tld)) {\n      return false;\n    }\n\n    // disallow spaces\n    if (/\\s/.test(tld)) {\n      return false;\n    }\n  }\n\n  // reject numeric TLDs\n  if (!options.allow_numeric_tld && /^\\d+$/.test(tld)) {\n    return false;\n  }\n  return parts.every(function (part) {\n    if (part.length > 63 && !options.ignore_max_length) {\n      return false;\n    }\n    if (!/^[a-z_\\u00a1-\\uffff0-9-]+$/i.test(part)) {\n      return false;\n    }\n\n    // disallow full-width chars\n    if (/[\\uff01-\\uff5e]/.test(part)) {\n      return false;\n    }\n\n    // disallow parts starting or ending with hyphen\n    if (/^-|-$/.test(part)) {\n      return false;\n    }\n    if (!options.allow_underscores && /_/.test(part)) {\n      return false;\n    }\n    return true;\n  });\n}\nmodule.exports = exports.default;\nmodule.exports.default = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isIP;\nvar _assertString = _interopRequireDefault(require(\"./util/assertString\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n/**\n11.3.  Examples\n\n   The following addresses\n\n             fe80::1234 (on the 1st link of the node)\n             ff02::5678 (on the 5th link of the node)\n             ff08::9abc (on the 10th organization of the node)\n\n   would be represented as follows:\n\n             fe80::1234%1\n             ff02::5678%5\n             ff08::9abc%10\n\n   (Here we assume a natural translation from a zone index to the\n   <zone_id> part, where the Nth zone of any scope is translated into\n   \"N\".)\n\n   If we use interface names as <zone_id>, those addresses could also be\n   represented as follows:\n\n            fe80::1234%ne0\n            ff02::5678%pvc1.3\n            ff08::9abc%interface10\n\n   where the interface \"ne0\" belongs to the 1st link, \"pvc1.3\" belongs\n   to the 5th link, and \"interface10\" belongs to the 10th organization.\n * * */\nvar IPv4SegmentFormat = '(?:[0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])';\nvar IPv4AddressFormat = \"(\".concat(IPv4SegmentFormat, \"[.]){3}\").concat(IPv4SegmentFormat);\nvar IPv4AddressRegExp = new RegExp(\"^\".concat(IPv4AddressFormat, \"$\"));\nvar IPv6SegmentFormat = '(?:[0-9a-fA-F]{1,4})';\nvar IPv6AddressRegExp = new RegExp('^(' + \"(?:\".concat(IPv6SegmentFormat, \":){7}(?:\").concat(IPv6SegmentFormat, \"|:)|\") + \"(?:\".concat(IPv6SegmentFormat, \":){6}(?:\").concat(IPv4AddressFormat, \"|:\").concat(IPv6SegmentFormat, \"|:)|\") + \"(?:\".concat(IPv6SegmentFormat, \":){5}(?::\").concat(IPv4AddressFormat, \"|(:\").concat(IPv6SegmentFormat, \"){1,2}|:)|\") + \"(?:\".concat(IPv6SegmentFormat, \":){4}(?:(:\").concat(IPv6SegmentFormat, \"){0,1}:\").concat(IPv4AddressFormat, \"|(:\").concat(IPv6SegmentFormat, \"){1,3}|:)|\") + \"(?:\".concat(IPv6SegmentFormat, \":){3}(?:(:\").concat(IPv6SegmentFormat, \"){0,2}:\").concat(IPv4AddressFormat, \"|(:\").concat(IPv6SegmentFormat, \"){1,4}|:)|\") + \"(?:\".concat(IPv6SegmentFormat, \":){2}(?:(:\").concat(IPv6SegmentFormat, \"){0,3}:\").concat(IPv4AddressFormat, \"|(:\").concat(IPv6SegmentFormat, \"){1,5}|:)|\") + \"(?:\".concat(IPv6SegmentFormat, \":){1}(?:(:\").concat(IPv6SegmentFormat, \"){0,4}:\").concat(IPv4AddressFormat, \"|(:\").concat(IPv6SegmentFormat, \"){1,6}|:)|\") + \"(?::((?::\".concat(IPv6SegmentFormat, \"){0,5}:\").concat(IPv4AddressFormat, \"|(?::\").concat(IPv6SegmentFormat, \"){1,7}|:))\") + ')(%[0-9a-zA-Z-.:]{1,})?$');\nfunction isIP(str) {\n  var version = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  (0, _assertString.default)(str);\n  version = String(version);\n  if (!version) {\n    return isIP(str, 4) || isIP(str, 6);\n  }\n  if (version === '4') {\n    return IPv4AddressRegExp.test(str);\n  }\n  if (version === '6') {\n    return IPv6AddressRegExp.test(str);\n  }\n  return false;\n}\nmodule.exports = exports.default;\nmodule.exports.default = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = isURL;\nvar _assertString = _interopRequireDefault(require(\"./util/assertString\"));\nvar _isFQDN = _interopRequireDefault(require(\"./isFQDN\"));\nvar _isIP = _interopRequireDefault(require(\"./isIP\"));\nvar _merge = _interopRequireDefault(require(\"./util/merge\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n/*\noptions for isURL method\n\nrequire_protocol - if set as true isURL will return false if protocol is not present in the URL\nrequire_valid_protocol - isURL will check if the URL's protocol is present in the protocols option\nprotocols - valid protocols can be modified with this option\nrequire_host - if set as false isURL will not check if host is present in the URL\nrequire_port - if set as true isURL will check if port is present in the URL\nallow_protocol_relative_urls - if set as true protocol relative URLs will be allowed\nvalidate_length - if set as false isURL will skip string length validation (IE maximum is 2083)\n\n*/\n\nvar default_url_options = {\n  protocols: ['http', 'https', 'ftp'],\n  require_tld: true,\n  require_protocol: false,\n  require_host: true,\n  require_port: false,\n  require_valid_protocol: true,\n  allow_underscores: false,\n  allow_trailing_dot: false,\n  allow_protocol_relative_urls: false,\n  allow_fragments: true,\n  allow_query_components: true,\n  validate_length: true\n};\nvar wrapped_ipv6 = /^\\[([^\\]]+)\\](?::([0-9]+))?$/;\nfunction isRegExp(obj) {\n  return Object.prototype.toString.call(obj) === '[object RegExp]';\n}\nfunction checkHost(host, matches) {\n  for (var i = 0; i < matches.length; i++) {\n    var match = matches[i];\n    if (host === match || isRegExp(match) && match.test(host)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction isURL(url, options) {\n  (0, _assertString.default)(url);\n  if (!url || /[\\s<>]/.test(url)) {\n    return false;\n  }\n  if (url.indexOf('mailto:') === 0) {\n    return false;\n  }\n  options = (0, _merge.default)(options, default_url_options);\n  if (options.validate_length && url.length >= 2083) {\n    return false;\n  }\n  if (!options.allow_fragments && url.includes('#')) {\n    return false;\n  }\n  if (!options.allow_query_components && (url.includes('?') || url.includes('&'))) {\n    return false;\n  }\n  var protocol, auth, host, hostname, port, port_str, split, ipv6;\n  split = url.split('#');\n  url = split.shift();\n  split = url.split('?');\n  url = split.shift();\n  split = url.split('://');\n  if (split.length > 1) {\n    protocol = split.shift().toLowerCase();\n    if (options.require_valid_protocol && options.protocols.indexOf(protocol) === -1) {\n      return false;\n    }\n  } else if (options.require_protocol) {\n    return false;\n  } else if (url.slice(0, 2) === '//') {\n    if (!options.allow_protocol_relative_urls) {\n      return false;\n    }\n    split[0] = url.slice(2);\n  }\n  url = split.join('://');\n  if (url === '') {\n    return false;\n  }\n  split = url.split('/');\n  url = split.shift();\n  if (url === '' && !options.require_host) {\n    return true;\n  }\n  split = url.split('@');\n  if (split.length > 1) {\n    if (options.disallow_auth) {\n      return false;\n    }\n    if (split[0] === '') {\n      return false;\n    }\n    auth = split.shift();\n    if (auth.indexOf(':') >= 0 && auth.split(':').length > 2) {\n      return false;\n    }\n    var _auth$split = auth.split(':'),\n      _auth$split2 = _slicedToArray(_auth$split, 2),\n      user = _auth$split2[0],\n      password = _auth$split2[1];\n    if (user === '' && password === '') {\n      return false;\n    }\n  }\n  hostname = split.join('@');\n  port_str = null;\n  ipv6 = null;\n  var ipv6_match = hostname.match(wrapped_ipv6);\n  if (ipv6_match) {\n    host = '';\n    ipv6 = ipv6_match[1];\n    port_str = ipv6_match[2] || null;\n  } else {\n    split = hostname.split(':');\n    host = split.shift();\n    if (split.length) {\n      port_str = split.join(':');\n    }\n  }\n  if (port_str !== null && port_str.length > 0) {\n    port = parseInt(port_str, 10);\n    if (!/^[0-9]+$/.test(port_str) || port <= 0 || port > 65535) {\n      return false;\n    }\n  } else if (options.require_port) {\n    return false;\n  }\n  if (options.host_whitelist) {\n    return checkHost(host, options.host_whitelist);\n  }\n  if (host === '' && !options.require_host) {\n    return true;\n  }\n  if (!(0, _isIP.default)(host) && !(0, _isFQDN.default)(host, options) && (!ipv6 || !(0, _isIP.default)(ipv6, 6))) {\n    return false;\n  }\n  host = host || ipv6;\n  if (options.host_blacklist && checkHost(host, options.host_blacklist)) {\n    return false;\n  }\n  return true;\n}\nmodule.exports = exports.default;\nmodule.exports.default = exports.default;"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB,uBAAuB,sBAA8B;AACzE,QAAI,SAAS,uBAAuB,eAAuB;AAC3D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,QAAI,uBAAuB;AAAA,MACzB,aAAa;AAAA,MACb,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,mBAAmB;AAAA,IACrB;AACA,aAAS,OAAO,KAAK,SAAS;AAC5B,OAAC,GAAG,cAAc,SAAS,GAAG;AAC9B,iBAAW,GAAG,OAAO,SAAS,SAAS,oBAAoB;AAG3D,UAAI,QAAQ,sBAAsB,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC7D,cAAM,IAAI,UAAU,GAAG,IAAI,SAAS,CAAC;AAAA,MACvC;AAGA,UAAI,QAAQ,mBAAmB,QAAQ,IAAI,QAAQ,IAAI,MAAM,GAAG;AAC9D,cAAM,IAAI,UAAU,CAAC;AAAA,MACvB;AACA,UAAI,QAAQ,IAAI,MAAM,GAAG;AACzB,UAAI,MAAM,MAAM,MAAM,SAAS,CAAC;AAChC,UAAI,QAAQ,aAAa;AAEvB,YAAI,MAAM,SAAS,GAAG;AACpB,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,QAAQ,qBAAqB,CAAC,qFAAqF,KAAK,GAAG,GAAG;AACjI,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,KAAK,GAAG,GAAG;AAClB,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,CAAC,QAAQ,qBAAqB,QAAQ,KAAK,GAAG,GAAG;AACnD,eAAO;AAAA,MACT;AACA,aAAO,MAAM,MAAM,SAAU,MAAM;AACjC,YAAI,KAAK,SAAS,MAAM,CAAC,QAAQ,mBAAmB;AAClD,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,8BAA8B,KAAK,IAAI,GAAG;AAC7C,iBAAO;AAAA,QACT;AAGA,YAAI,kBAAkB,KAAK,IAAI,GAAG;AAChC,iBAAO;AAAA,QACT;AAGA,YAAI,QAAQ,KAAK,IAAI,GAAG;AACtB,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,QAAQ,qBAAqB,IAAI,KAAK,IAAI,GAAG;AAChD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO,UAAU,QAAQ;AACzB,WAAO,QAAQ,UAAU,QAAQ;AAAA;AAAA;;;AC3EjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB,uBAAuB,sBAA8B;AACzE,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AA8B9F,QAAI,oBAAoB;AACxB,QAAI,oBAAoB,IAAI,OAAO,mBAAmB,SAAS,EAAE,OAAO,iBAAiB;AACzF,QAAI,oBAAoB,IAAI,OAAO,IAAI,OAAO,mBAAmB,GAAG,CAAC;AACrE,QAAI,oBAAoB;AACxB,QAAI,oBAAoB,IAAI,OAAO,OAAO,MAAM,OAAO,mBAAmB,UAAU,EAAE,OAAO,mBAAmB,MAAM,IAAI,MAAM,OAAO,mBAAmB,UAAU,EAAE,OAAO,mBAAmB,IAAI,EAAE,OAAO,mBAAmB,MAAM,IAAI,MAAM,OAAO,mBAAmB,WAAW,EAAE,OAAO,mBAAmB,KAAK,EAAE,OAAO,mBAAmB,YAAY,IAAI,MAAM,OAAO,mBAAmB,YAAY,EAAE,OAAO,mBAAmB,SAAS,EAAE,OAAO,mBAAmB,KAAK,EAAE,OAAO,mBAAmB,YAAY,IAAI,MAAM,OAAO,mBAAmB,YAAY,EAAE,OAAO,mBAAmB,SAAS,EAAE,OAAO,mBAAmB,KAAK,EAAE,OAAO,mBAAmB,YAAY,IAAI,MAAM,OAAO,mBAAmB,YAAY,EAAE,OAAO,mBAAmB,SAAS,EAAE,OAAO,mBAAmB,KAAK,EAAE,OAAO,mBAAmB,YAAY,IAAI,MAAM,OAAO,mBAAmB,YAAY,EAAE,OAAO,mBAAmB,SAAS,EAAE,OAAO,mBAAmB,KAAK,EAAE,OAAO,mBAAmB,YAAY,IAAI,YAAY,OAAO,mBAAmB,SAAS,EAAE,OAAO,mBAAmB,OAAO,EAAE,OAAO,mBAAmB,YAAY,IAAI,0BAA0B;AAClnC,aAAS,KAAK,KAAK;AACjB,UAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAClF,OAAC,GAAG,cAAc,SAAS,GAAG;AAC9B,gBAAU,OAAO,OAAO;AACxB,UAAI,CAAC,SAAS;AACZ,eAAO,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC;AAAA,MACpC;AACA,UAAI,YAAY,KAAK;AACnB,eAAO,kBAAkB,KAAK,GAAG;AAAA,MACnC;AACA,UAAI,YAAY,KAAK;AACnB,eAAO,kBAAkB,KAAK,GAAG;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU,QAAQ;AACzB,WAAO,QAAQ,UAAU,QAAQ;AAAA;AAAA;;;AC1DjC;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,gBAAgB,uBAAuB,sBAA8B;AACzE,QAAI,UAAU,uBAAuB,gBAAmB;AACxD,QAAI,QAAQ,uBAAuB,cAAiB;AACpD,QAAI,SAAS,uBAAuB,eAAuB;AAC3D,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,aAAS,eAAe,KAAK,GAAG;AAAE,aAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAAA,IAAG;AAC7J,aAAS,mBAAmB;AAAE,YAAM,IAAI,UAAU,2IAA2I;AAAA,IAAG;AAChM,aAAS,4BAA4B,GAAG,QAAQ;AAAE,UAAI,CAAC,EAAG;AAAQ,UAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAAG,UAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AAAG,UAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AAAM,UAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AAAG,UAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AAAA,IAAG;AAC/Z,aAAS,kBAAkB,KAAK,KAAK;AAAE,UAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAAQ,eAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAAG,aAAO;AAAA,IAAM;AAClL,aAAS,sBAAsB,GAAG,GAAG;AAAE,UAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAAG,UAAI,QAAQ,GAAG;AAAE,YAAI,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAI,IAAI;AAAI,YAAI;AAAE,cAAI,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AAAE,gBAAI,OAAO,CAAC,MAAM,EAAG;AAAQ,gBAAI;AAAA,UAAI,MAAO,QAAO,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,QAAE,SAASA,IAAG;AAAE,cAAI,MAAI,IAAIA;AAAA,QAAG,UAAE;AAAU,cAAI;AAAE,gBAAI,CAAC,KAAK,QAAQ,EAAE,WAAW,IAAI,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,UAAQ,UAAE;AAAU,gBAAI,EAAG,OAAM;AAAA,UAAG;AAAA,QAAE;AAAE,eAAO;AAAA,MAAG;AAAA,IAAE;AACnhB,aAAS,gBAAgB,KAAK;AAAE,UAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AAAA,IAAK;AAcpE,QAAI,sBAAsB;AAAA,MACxB,WAAW,CAAC,QAAQ,SAAS,KAAK;AAAA,MAClC,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,cAAc;AAAA,MACd,cAAc;AAAA,MACd,wBAAwB;AAAA,MACxB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,8BAA8B;AAAA,MAC9B,iBAAiB;AAAA,MACjB,wBAAwB;AAAA,MACxB,iBAAiB;AAAA,IACnB;AACA,QAAI,eAAe;AACnB,aAAS,SAAS,KAAK;AACrB,aAAO,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM;AAAA,IACjD;AACA,aAAS,UAAU,MAAM,SAAS;AAChC,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,YAAI,QAAQ,QAAQ,CAAC;AACrB,YAAI,SAAS,SAAS,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI,GAAG;AACzD,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,aAAS,MAAM,KAAK,SAAS;AAC3B,OAAC,GAAG,cAAc,SAAS,GAAG;AAC9B,UAAI,CAAC,OAAO,SAAS,KAAK,GAAG,GAAG;AAC9B,eAAO;AAAA,MACT;AACA,UAAI,IAAI,QAAQ,SAAS,MAAM,GAAG;AAChC,eAAO;AAAA,MACT;AACA,iBAAW,GAAG,OAAO,SAAS,SAAS,mBAAmB;AAC1D,UAAI,QAAQ,mBAAmB,IAAI,UAAU,MAAM;AACjD,eAAO;AAAA,MACT;AACA,UAAI,CAAC,QAAQ,mBAAmB,IAAI,SAAS,GAAG,GAAG;AACjD,eAAO;AAAA,MACT;AACA,UAAI,CAAC,QAAQ,2BAA2B,IAAI,SAAS,GAAG,KAAK,IAAI,SAAS,GAAG,IAAI;AAC/E,eAAO;AAAA,MACT;AACA,UAAI,UAAU,MAAM,MAAM,UAAU,MAAM,UAAU,OAAO;AAC3D,cAAQ,IAAI,MAAM,GAAG;AACrB,YAAM,MAAM,MAAM;AAClB,cAAQ,IAAI,MAAM,GAAG;AACrB,YAAM,MAAM,MAAM;AAClB,cAAQ,IAAI,MAAM,KAAK;AACvB,UAAI,MAAM,SAAS,GAAG;AACpB,mBAAW,MAAM,MAAM,EAAE,YAAY;AACrC,YAAI,QAAQ,0BAA0B,QAAQ,UAAU,QAAQ,QAAQ,MAAM,IAAI;AAChF,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,QAAQ,kBAAkB;AACnC,eAAO;AAAA,MACT,WAAW,IAAI,MAAM,GAAG,CAAC,MAAM,MAAM;AACnC,YAAI,CAAC,QAAQ,8BAA8B;AACzC,iBAAO;AAAA,QACT;AACA,cAAM,CAAC,IAAI,IAAI,MAAM,CAAC;AAAA,MACxB;AACA,YAAM,MAAM,KAAK,KAAK;AACtB,UAAI,QAAQ,IAAI;AACd,eAAO;AAAA,MACT;AACA,cAAQ,IAAI,MAAM,GAAG;AACrB,YAAM,MAAM,MAAM;AAClB,UAAI,QAAQ,MAAM,CAAC,QAAQ,cAAc;AACvC,eAAO;AAAA,MACT;AACA,cAAQ,IAAI,MAAM,GAAG;AACrB,UAAI,MAAM,SAAS,GAAG;AACpB,YAAI,QAAQ,eAAe;AACzB,iBAAO;AAAA,QACT;AACA,YAAI,MAAM,CAAC,MAAM,IAAI;AACnB,iBAAO;AAAA,QACT;AACA,eAAO,MAAM,MAAM;AACnB,YAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,KAAK,MAAM,GAAG,EAAE,SAAS,GAAG;AACxD,iBAAO;AAAA,QACT;AACA,YAAI,cAAc,KAAK,MAAM,GAAG,GAC9B,eAAe,eAAe,aAAa,CAAC,GAC5C,OAAO,aAAa,CAAC,GACrB,WAAW,aAAa,CAAC;AAC3B,YAAI,SAAS,MAAM,aAAa,IAAI;AAClC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,iBAAW,MAAM,KAAK,GAAG;AACzB,iBAAW;AACX,aAAO;AACP,UAAI,aAAa,SAAS,MAAM,YAAY;AAC5C,UAAI,YAAY;AACd,eAAO;AACP,eAAO,WAAW,CAAC;AACnB,mBAAW,WAAW,CAAC,KAAK;AAAA,MAC9B,OAAO;AACL,gBAAQ,SAAS,MAAM,GAAG;AAC1B,eAAO,MAAM,MAAM;AACnB,YAAI,MAAM,QAAQ;AAChB,qBAAW,MAAM,KAAK,GAAG;AAAA,QAC3B;AAAA,MACF;AACA,UAAI,aAAa,QAAQ,SAAS,SAAS,GAAG;AAC5C,eAAO,SAAS,UAAU,EAAE;AAC5B,YAAI,CAAC,WAAW,KAAK,QAAQ,KAAK,QAAQ,KAAK,OAAO,OAAO;AAC3D,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,QAAQ,cAAc;AAC/B,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,gBAAgB;AAC1B,eAAO,UAAU,MAAM,QAAQ,cAAc;AAAA,MAC/C;AACA,UAAI,SAAS,MAAM,CAAC,QAAQ,cAAc;AACxC,eAAO;AAAA,MACT;AACA,UAAI,EAAE,GAAG,MAAM,SAAS,IAAI,KAAK,EAAE,GAAG,QAAQ,SAAS,MAAM,OAAO,MAAM,CAAC,QAAQ,EAAE,GAAG,MAAM,SAAS,MAAM,CAAC,IAAI;AAChH,eAAO;AAAA,MACT;AACA,aAAO,QAAQ;AACf,UAAI,QAAQ,kBAAkB,UAAU,MAAM,QAAQ,cAAc,GAAG;AACrE,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU,QAAQ;AACzB,WAAO,QAAQ,UAAU,QAAQ;AAAA;AAAA;", "names": ["r"]}