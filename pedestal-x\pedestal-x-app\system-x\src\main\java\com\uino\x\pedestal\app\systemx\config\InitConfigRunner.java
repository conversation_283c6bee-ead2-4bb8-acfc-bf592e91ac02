package com.uino.x.pedestal.app.systemx.config;

import com.uino.x.common.core.util.AsyncUtils;
import com.uino.x.pedestal.tenant.api.service.TenantInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 项目初始化配置
 *
 * <AUTHOR>
 * @version 0.0.5
 * @date 2021/12/3 18:34
 */
//@Component
@RequiredArgsConstructor
public class InitConfigRunner implements ApplicationRunner {

    private final TenantInfoService tenantInfoService;

    @Override
    public void run(ApplicationArguments args) {

        // 租户数据初始化
        AsyncUtils.asyncExecutor(tenantInfoService::initTenantData);
    }
}
