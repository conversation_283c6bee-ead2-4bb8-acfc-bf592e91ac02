{"version": 3, "sources": ["../../@babel/runtime/helpers/typeof.js", "../../@babel/runtime/helpers/regeneratorRuntime.js", "../../@babel/runtime/regenerator/index.js", "../../@babel/runtime/helpers/esm/asyncToGenerator.js", "../../@babel/runtime/helpers/esm/arrayWithHoles.js", "../../@babel/runtime/helpers/esm/iterableToArrayLimit.js", "../../@babel/runtime/helpers/esm/arrayLikeToArray.js", "../../@babel/runtime/helpers/esm/unsupportedIterableToArray.js", "../../@babel/runtime/helpers/esm/nonIterableRest.js", "../../@babel/runtime/helpers/esm/slicedToArray.js", "../../@babel/runtime/helpers/esm/arrayWithoutHoles.js", "../../@babel/runtime/helpers/esm/iterableToArray.js", "../../@babel/runtime/helpers/esm/nonIterableSpread.js", "../../@babel/runtime/helpers/esm/toConsumableArray.js", "../../wujie/src/constant.ts", "../../wujie/src/utils.ts", "../../wujie/src/template.ts", "../../wujie/src/plugin.ts", "../../wujie/src/entry.ts", "../../@babel/runtime/helpers/esm/classCallCheck.js", "../../@babel/runtime/helpers/esm/createClass.js", "../../@babel/runtime/helpers/esm/assertThisInitialized.js", "../../@babel/runtime/helpers/esm/possibleConstructorReturn.js", "../../@babel/runtime/helpers/esm/getPrototypeOf.js", "../../@babel/runtime/helpers/esm/setPrototypeOf.js", "../../@babel/runtime/helpers/esm/inherits.js", "../../@babel/runtime/helpers/esm/isNativeFunction.js", "../../@babel/runtime/helpers/esm/isNativeReflectConstruct.js", "../../@babel/runtime/helpers/esm/construct.js", "../../@babel/runtime/helpers/esm/wrapNativeSuper.js", "../../wujie/src/common.ts", "../../wujie/src/effect.ts", "../../wujie/src/shadow.ts", "../../wujie/src/sync.ts", "../../wujie/src/iframe.ts", "../../wujie/src/proxy.ts", "../../wujie/src/event.ts", "../../wujie/src/sandbox.ts", "../../wujie/src/index.ts", "../../wujie-vue3/index.js"], "sourcesContent": ["function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports, _typeof(o);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _regeneratorRuntime() {\n  \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */\n  module.exports = _regeneratorRuntime = function _regeneratorRuntime() {\n    return e;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  var t,\n    e = {},\n    r = Object.prototype,\n    n = r.hasOwnProperty,\n    o = Object.defineProperty || function (t, e, r) {\n      t[e] = r.value;\n    },\n    i = \"function\" == typeof Symbol ? Symbol : {},\n    a = i.iterator || \"@@iterator\",\n    c = i.asyncIterator || \"@@asyncIterator\",\n    u = i.toStringTag || \"@@toStringTag\";\n  function define(t, e, r) {\n    return Object.defineProperty(t, e, {\n      value: r,\n      enumerable: !0,\n      configurable: !0,\n      writable: !0\n    }), t[e];\n  }\n  try {\n    define({}, \"\");\n  } catch (t) {\n    define = function define(t, e, r) {\n      return t[e] = r;\n    };\n  }\n  function wrap(t, e, r, n) {\n    var i = e && e.prototype instanceof Generator ? e : Generator,\n      a = Object.create(i.prototype),\n      c = new Context(n || []);\n    return o(a, \"_invoke\", {\n      value: makeInvokeMethod(t, r, c)\n    }), a;\n  }\n  function tryCatch(t, e, r) {\n    try {\n      return {\n        type: \"normal\",\n        arg: t.call(e, r)\n      };\n    } catch (t) {\n      return {\n        type: \"throw\",\n        arg: t\n      };\n    }\n  }\n  e.wrap = wrap;\n  var h = \"suspendedStart\",\n    l = \"suspendedYield\",\n    f = \"executing\",\n    s = \"completed\",\n    y = {};\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n  var p = {};\n  define(p, a, function () {\n    return this;\n  });\n  var d = Object.getPrototypeOf,\n    v = d && d(d(values([])));\n  v && v !== r && n.call(v, a) && (p = v);\n  var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p);\n  function defineIteratorMethods(t) {\n    [\"next\", \"throw\", \"return\"].forEach(function (e) {\n      define(t, e, function (t) {\n        return this._invoke(e, t);\n      });\n    });\n  }\n  function AsyncIterator(t, e) {\n    function invoke(r, o, i, a) {\n      var c = tryCatch(t[r], t, o);\n      if (\"throw\" !== c.type) {\n        var u = c.arg,\n          h = u.value;\n        return h && \"object\" == _typeof(h) && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) {\n          invoke(\"next\", t, i, a);\n        }, function (t) {\n          invoke(\"throw\", t, i, a);\n        }) : e.resolve(h).then(function (t) {\n          u.value = t, i(u);\n        }, function (t) {\n          return invoke(\"throw\", t, i, a);\n        });\n      }\n      a(c.arg);\n    }\n    var r;\n    o(this, \"_invoke\", {\n      value: function value(t, n) {\n        function callInvokeWithMethodAndArg() {\n          return new e(function (e, r) {\n            invoke(t, n, e, r);\n          });\n        }\n        return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n      }\n    });\n  }\n  function makeInvokeMethod(e, r, n) {\n    var o = h;\n    return function (i, a) {\n      if (o === f) throw Error(\"Generator is already running\");\n      if (o === s) {\n        if (\"throw\" === i) throw a;\n        return {\n          value: t,\n          done: !0\n        };\n      }\n      for (n.method = i, n.arg = a;;) {\n        var c = n.delegate;\n        if (c) {\n          var u = maybeInvokeDelegate(c, n);\n          if (u) {\n            if (u === y) continue;\n            return u;\n          }\n        }\n        if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) {\n          if (o === h) throw o = s, n.arg;\n          n.dispatchException(n.arg);\n        } else \"return\" === n.method && n.abrupt(\"return\", n.arg);\n        o = f;\n        var p = tryCatch(e, r, n);\n        if (\"normal\" === p.type) {\n          if (o = n.done ? s : l, p.arg === y) continue;\n          return {\n            value: p.arg,\n            done: n.done\n          };\n        }\n        \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg);\n      }\n    };\n  }\n  function maybeInvokeDelegate(e, r) {\n    var n = r.method,\n      o = e.iterator[n];\n    if (o === t) return r.delegate = null, \"throw\" === n && e.iterator[\"return\"] && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y;\n    var i = tryCatch(o, e.iterator, r.arg);\n    if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y;\n    var a = i.arg;\n    return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y);\n  }\n  function pushTryEntry(t) {\n    var e = {\n      tryLoc: t[0]\n    };\n    1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e);\n  }\n  function resetTryEntry(t) {\n    var e = t.completion || {};\n    e.type = \"normal\", delete e.arg, t.completion = e;\n  }\n  function Context(t) {\n    this.tryEntries = [{\n      tryLoc: \"root\"\n    }], t.forEach(pushTryEntry, this), this.reset(!0);\n  }\n  function values(e) {\n    if (e || \"\" === e) {\n      var r = e[a];\n      if (r) return r.call(e);\n      if (\"function\" == typeof e.next) return e;\n      if (!isNaN(e.length)) {\n        var o = -1,\n          i = function next() {\n            for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next;\n            return next.value = t, next.done = !0, next;\n          };\n        return i.next = i;\n      }\n    }\n    throw new TypeError(_typeof(e) + \" is not iterable\");\n  }\n  return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", {\n    value: GeneratorFunctionPrototype,\n    configurable: !0\n  }), o(GeneratorFunctionPrototype, \"constructor\", {\n    value: GeneratorFunction,\n    configurable: !0\n  }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) {\n    var e = \"function\" == typeof t && t.constructor;\n    return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name));\n  }, e.mark = function (t) {\n    return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t;\n  }, e.awrap = function (t) {\n    return {\n      __await: t\n    };\n  }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () {\n    return this;\n  }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) {\n    void 0 === i && (i = Promise);\n    var a = new AsyncIterator(wrap(t, r, n, o), i);\n    return e.isGeneratorFunction(r) ? a : a.next().then(function (t) {\n      return t.done ? t.value : a.next();\n    });\n  }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () {\n    return this;\n  }), define(g, \"toString\", function () {\n    return \"[object Generator]\";\n  }), e.keys = function (t) {\n    var e = Object(t),\n      r = [];\n    for (var n in e) r.push(n);\n    return r.reverse(), function next() {\n      for (; r.length;) {\n        var t = r.pop();\n        if (t in e) return next.value = t, next.done = !1, next;\n      }\n      return next.done = !0, next;\n    };\n  }, e.values = values, Context.prototype = {\n    constructor: Context,\n    reset: function reset(e) {\n      if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t);\n    },\n    stop: function stop() {\n      this.done = !0;\n      var t = this.tryEntries[0].completion;\n      if (\"throw\" === t.type) throw t.arg;\n      return this.rval;\n    },\n    dispatchException: function dispatchException(e) {\n      if (this.done) throw e;\n      var r = this;\n      function handle(n, o) {\n        return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o;\n      }\n      for (var o = this.tryEntries.length - 1; o >= 0; --o) {\n        var i = this.tryEntries[o],\n          a = i.completion;\n        if (\"root\" === i.tryLoc) return handle(\"end\");\n        if (i.tryLoc <= this.prev) {\n          var c = n.call(i, \"catchLoc\"),\n            u = n.call(i, \"finallyLoc\");\n          if (c && u) {\n            if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n            if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n          } else if (c) {\n            if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n          } else {\n            if (!u) throw Error(\"try statement without catch or finally\");\n            if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n          }\n        }\n      }\n    },\n    abrupt: function abrupt(t, e) {\n      for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n        var o = this.tryEntries[r];\n        if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) {\n          var i = o;\n          break;\n        }\n      }\n      i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);\n      var a = i ? i.completion : {};\n      return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a);\n    },\n    complete: function complete(t, e) {\n      if (\"throw\" === t.type) throw t.arg;\n      return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y;\n    },\n    finish: function finish(t) {\n      for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n        var r = this.tryEntries[e];\n        if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y;\n      }\n    },\n    \"catch\": function _catch(t) {\n      for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n        var r = this.tryEntries[e];\n        if (r.tryLoc === t) {\n          var n = r.completion;\n          if (\"throw\" === n.type) {\n            var o = n.arg;\n            resetTryEntry(r);\n          }\n          return o;\n        }\n      }\n      throw Error(\"illegal catch attempt\");\n    },\n    delegateYield: function delegateYield(e, r, n) {\n      return this.delegate = {\n        iterator: values(e),\n        resultName: r,\n        nextLoc: n\n      }, \"next\" === this.method && (this.arg = t), y;\n    }\n  }, e;\n}\nmodule.exports = _regeneratorRuntime, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "// TODO(Babel 8): Remove this file.\n\nvar runtime = require(\"../helpers/regeneratorRuntime\")();\nmodule.exports = runtime;\n\n// Copied from https://github.com/facebook/regenerator/blob/main/packages/runtime/runtime.js#L736=\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n", "function asyncGeneratorStep(n, t, e, r, o, a, c) {\n  try {\n    var i = n[a](c),\n      u = i.value;\n  } catch (n) {\n    return void e(n);\n  }\n  i.done ? t(u) : Promise.resolve(u).then(r, o);\n}\nfunction _asyncToGenerator(n) {\n  return function () {\n    var t = this,\n      e = arguments;\n    return new Promise(function (r, o) {\n      var a = n.apply(t, e);\n      function _next(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n);\n      }\n      function _throw(n) {\n        asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n);\n      }\n      _next(void 0);\n    });\n  };\n}\nexport { _asyncToGenerator as default };", "function _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nexport { _arrayWithHoles as default };", "function _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nexport { _iterableToArrayLimit as default };", "function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };", "function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableRest as default };", "import arrayWithHoles from \"./arrayWithHoles.js\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableRest from \"./nonIterableRest.js\";\nfunction _slicedToArray(r, e) {\n  return arrayWithHoles(r) || iterableToArrayLimit(r, e) || unsupportedIterableToArray(r, e) || nonIterableRest();\n}\nexport { _slicedToArray as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };", "function _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nexport { _iterableToArray as default };", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };", "import arrayWithoutHoles from \"./arrayWithoutHoles.js\";\nimport iterableToArray from \"./iterableToArray.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableSpread from \"./nonIterableSpread.js\";\nfunction _toConsumableArray(r) {\n  return arrayWithoutHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableSpread();\n}\nexport { _toConsumableArray as default };", "// 私有元素属性\nexport const WUJIE_APP_ID = \"data-wujie-id\";\nexport const WUJIE_SCRIPT_ID = \"data-wujie-script-id\";\nexport const WUJIE_DATA_FLAG = \"data-wujie-Flag\";\nexport const CONTAINER_POSITION_DATA_FLAG = \"data-container-position-flag\";\nexport const CONTAINER_OVERFLOW_DATA_FLAG = \"data-container-overflow-flag\";\nexport const LOADING_DATA_FLAG = \"data-loading-flag\";\nexport const WUJIE_DATA_ATTACH_CSS_FLAG = \"data-wujie-attach-css-flag\";\n\n// 需要使用的某些固定变量\nexport const WUJIE_IFRAME_CLASS = \"wujie_iframe\";\nexport const WUJIE_ALL_EVENT = \"_wujie_all_event\";\nexport const WUJIE_SHADE_STYLE =\n  \"position: fixed; z-index: 2147483647; visibility: hidden; inset: 0px; backface-visibility: hidden;\";\nexport const WUJIE_LOADING_STYLE =\n  \"position: absolute; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; z-index:1;\";\n\nexport const WUJIE_LOADING_SVG = `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24px\" height=\"30px\" viewBox=\"0 0 24 30\">\n<rect x=\"0\" y=\"13\" width=\"4\" height=\"5\" fill=\"#909090\">\n  <animate attributeName=\"height\" attributeType=\"XML\" values=\"5;21;5\" begin=\"0s\" dur=\"0.6s\" repeatCount=\"indefinite\"></animate>\n  <animate attributeName=\"y\" attributeType=\"XML\" values=\"13; 5; 13\" begin=\"0s\" dur=\"0.6s\" repeatCount=\"indefinite\"></animate>\n</rect>\n<rect x=\"10\" y=\"13\" width=\"4\" height=\"5\" fill=\"#909090\">\n  <animate attributeName=\"height\" attributeType=\"XML\" values=\"5;21;5\" begin=\"0.15s\" dur=\"0.6s\" repeatCount=\"indefinite\"></animate>\n  <animate attributeName=\"y\" attributeType=\"XML\" values=\"13; 5; 13\" begin=\"0.15s\" dur=\"0.6s\" repeatCount=\"indefinite\"></animate>\n</rect>\n<rect x=\"20\" y=\"13\" width=\"4\" height=\"5\" fill=\"#909090\">\n  <animate attributeName=\"height\" attributeType=\"XML\" values=\"5;21;5\" begin=\"0.3s\" dur=\"0.6s\" repeatCount=\"indefinite\"></animate>\n  <animate attributeName=\"y\" attributeType=\"XML\" values=\"13; 5; 13\" begin=\"0.3s\" dur=\"0.6s\" repeatCount=\"indefinite\"></animate>\n</rect>\n</svg>`;\n\n// 提醒类\nexport const WUJIE_TIPS_NO_URL = \"url参数为空\";\nexport const WUJIE_TIPS_RELOAD_DISABLED = \"子应用调用reload无法生效\";\nexport const WUJIE_TIPS_STOP_APP = \"此报错可以忽略，iframe主动中断主应用代码在子应用运行\";\nexport const WUJIE_TIPS_STOP_APP_DETAIL = WUJIE_TIPS_STOP_APP + \"，详见：https://github.com/Tencent/wujie/issues/54\";\nexport const WUJIE_TIPS_NO_SUBJECT = \"事件订阅数量为空\";\nexport const WUJIE_TIPS_NO_FETCH = \"window上不存在fetch属性，需要自行polyfill\";\nexport const WUJIE_TIPS_NOT_SUPPORTED = \"当前浏览器不支持无界，子应用将采用iframe方式渲染\";\nexport const WUJIE_TIPS_SCRIPT_ERROR_REQUESTED = \"脚本请求出现错误\";\nexport const WUJIE_TIPS_CSS_ERROR_REQUESTED = \"样式请求出现错误\";\nexport const WUJIE_TIPS_HTML_ERROR_REQUESTED = \"html请求出现错误\";\nexport const WUJIE_TIPS_REPEAT_RENDER = \"无界组件短时间重复渲染了两次，可能存在性能问题请检查代码\";\nexport const WUJIE_TIPS_NO_SCRIPT = \"目标Script尚未准备好或已经被移除\";\nexport const WUJIE_TIPS_GET_ELEMENT_BY_ID =\n  \"不支持document.getElementById()传入特殊字符，请参考document.querySelector文档\";\n", "import {\n  WUJIE_SCRIPT_ID,\n  WUJIE_TIPS_NO_URL,\n  WU<PERSON>IE_APP_ID,\n  WUJIE_TIPS_STOP_APP,\n  WUJIE_TIPS_STOP_APP_DETAIL,\n} from \"./constant\";\nimport { plugin, cacheOptions } from \"./index\";\n\nexport function toArray<T>(array: T | T[]): T[] {\n  return Array.isArray(array) ? array : [array];\n}\n\nexport function isFunction(value: any): boolean {\n  return typeof value === \"function\";\n}\n\nexport function isHijackingTag(tagName?: string) {\n  return (\n    tagName?.toUpperCase() === \"LINK\" ||\n    tagName?.toUpperCase() === \"STYLE\" ||\n    tagName?.toUpperCase() === \"SCRIPT\" ||\n    tagName?.toUpperCase() === \"IFRAME\"\n  );\n}\n\nexport const wujieSupport = window.Proxy && window.CustomElementRegistry;\n\n/**\n * in safari\n * typeof document.all === 'undefined' // true\n * typeof document.all === 'function' // true\n * We need to discriminate safari for better performance\n */\nconst naughtySafari = typeof document.all === \"function\" && typeof document.all === \"undefined\";\nconst callableFnCacheMap = new WeakMap<CallableFunction, boolean>();\nexport const isCallable = (fn: any) => {\n  if (callableFnCacheMap.has(fn)) {\n    return true;\n  }\n\n  const callable = naughtySafari ? typeof fn === \"function\" && typeof fn !== \"undefined\" : typeof fn === \"function\";\n  if (callable) {\n    callableFnCacheMap.set(fn, callable);\n  }\n  return callable;\n};\n\nconst boundedMap = new WeakMap<CallableFunction, boolean>();\nexport function isBoundedFunction(fn: CallableFunction) {\n  if (boundedMap.has(fn)) {\n    return boundedMap.get(fn);\n  }\n  const bounded = fn.name.indexOf(\"bound \") === 0 && !fn.hasOwnProperty(\"prototype\");\n  boundedMap.set(fn, bounded);\n  return bounded;\n}\n\nconst fnRegexCheckCacheMap = new WeakMap<any | FunctionConstructor, boolean>();\nexport function isConstructable(fn: () => any | FunctionConstructor) {\n  const hasPrototypeMethods =\n    fn.prototype && fn.prototype.constructor === fn && Object.getOwnPropertyNames(fn.prototype).length > 1;\n\n  if (hasPrototypeMethods) return true;\n\n  if (fnRegexCheckCacheMap.has(fn)) {\n    return fnRegexCheckCacheMap.get(fn);\n  }\n\n  let constructable = hasPrototypeMethods;\n  if (!constructable) {\n    const fnString = fn.toString();\n    const constructableFunctionRegex = /^function\\b\\s[A-Z].*/;\n    const classRegex = /^class\\b/;\n    constructable = constructableFunctionRegex.test(fnString) || classRegex.test(fnString);\n  }\n\n  fnRegexCheckCacheMap.set(fn, constructable);\n  return constructable;\n}\n\n// 修复多个子应用启动，拿到的全局对象都是第一个子应用全局对象的bug：https://github.com/Tencent/wujie/issues/770\nconst setFnCacheMap = new WeakMap<\n  Window | Document | ShadowRoot | Location,\n  WeakMap<CallableFunction, CallableFunction>\n>();\n\nexport function checkProxyFunction(target: Window | Document | ShadowRoot | Location, value: any) {\n  if (isCallable(value) && !isBoundedFunction(value) && !isConstructable(value)) {\n    if (!setFnCacheMap.has(target)) {\n      setFnCacheMap.set(target, new WeakMap());\n      setFnCacheMap.get(target).set(value, value);\n    } else if (!setFnCacheMap.get(target).has(value)) {\n      setFnCacheMap.get(target).set(value, value);\n    }\n  }\n}\n\nexport function getTargetValue(target: any, p: any): any {\n  const value = target[p];\n  if (setFnCacheMap.has(target) && setFnCacheMap.get(target).has(value)) {\n    return setFnCacheMap.get(target).get(value);\n  }\n  if (isCallable(value) && !isBoundedFunction(value) && !isConstructable(value)) {\n    const boundValue = Function.prototype.bind.call(value, target);\n    if (setFnCacheMap.has(target)) {\n      setFnCacheMap.get(target).set(value, boundValue);\n    } else {\n      setFnCacheMap.set(target, new WeakMap());\n      setFnCacheMap.get(target).set(value, boundValue);\n    }\n    for (const key in value) {\n      boundValue[key] = value[key];\n    }\n    if (value.hasOwnProperty(\"prototype\") && !boundValue.hasOwnProperty(\"prototype\")) {\n      // https://github.com/kuitos/kuitos.github.io/issues/47\n      Object.defineProperty(boundValue, \"prototype\", { value: value.prototype, enumerable: false, writable: true });\n    }\n    return boundValue;\n  }\n  return value;\n}\n\nexport function getDegradeIframe(id: string): HTMLIFrameElement {\n  return window.document.querySelector(`iframe[${WUJIE_APP_ID}=\"${id}\"]`);\n}\n\nexport function setAttrsToElement(element: HTMLElement, attrs: { [key: string]: any }) {\n  Object.keys(attrs).forEach((name) => {\n    element.setAttribute(name, attrs[name]);\n  });\n}\n\nexport function appRouteParse(url: string): {\n  urlElement: HTMLAnchorElement;\n  appHostPath: string;\n  appRoutePath: string;\n} {\n  if (!url) {\n    error(WUJIE_TIPS_NO_URL);\n    throw new Error();\n  }\n  const urlElement = anchorElementGenerator(url);\n  const appHostPath = urlElement.protocol + \"//\" + urlElement.host;\n  let appRoutePath = urlElement.pathname + urlElement.search + urlElement.hash;\n  if (!appRoutePath.startsWith(\"/\")) appRoutePath = \"/\" + appRoutePath; // hack ie\n  return { urlElement, appHostPath, appRoutePath };\n}\n\nexport function anchorElementGenerator(url: string): HTMLAnchorElement {\n  const element = window.document.createElement(\"a\");\n  element.href = url;\n  element.href = element.href; // hack ie\n  return element;\n}\n\nexport function getAnchorElementQueryMap(anchorElement: HTMLAnchorElement): { [key: string]: string } {\n  const queryString = anchorElement.search || \"\";\n  return [...new URLSearchParams(queryString).entries()].reduce((p, c) => {\n    p[c[0]] = c[1];\n    return p;\n  }, {} as Record<string, string>);\n}\n\n/**\n * 当前url的查询参数中是否有给定的id\n */\nexport function isMatchSyncQueryById(id: string): boolean {\n  const queryMap = getAnchorElementQueryMap(anchorElementGenerator(window.location.href));\n  return Object.keys(queryMap).includes(id);\n}\n\n/**\n * 劫持元素原型对相对地址的赋值转绝对地址\n * @param iframeWindow\n */\nexport function fixElementCtrSrcOrHref(\n  iframeWindow: Window,\n  elementCtr:\n    | typeof HTMLImageElement\n    | typeof HTMLAnchorElement\n    | typeof HTMLSourceElement\n    | typeof HTMLLinkElement\n    | typeof HTMLScriptElement\n    | typeof HTMLMediaElement,\n  attr\n): void {\n  // patch setAttribute\n  const rawElementSetAttribute = iframeWindow.Element.prototype.setAttribute;\n  elementCtr.prototype.setAttribute = function (name: string, value: string): void {\n    let targetValue = value;\n    if (name === attr) targetValue = getAbsolutePath(value, this.baseURI || \"\", true);\n    rawElementSetAttribute.call(this, name, targetValue);\n  };\n  // patch href get and set\n  const rawAnchorElementHrefDescriptor = Object.getOwnPropertyDescriptor(elementCtr.prototype, attr);\n  const { enumerable, configurable, get, set } = rawAnchorElementHrefDescriptor;\n  Object.defineProperty(elementCtr.prototype, attr, {\n    enumerable,\n    configurable,\n    get: function () {\n      return get.call(this);\n    },\n    set: function (href) {\n      set.call(this, getAbsolutePath(href, this.baseURI, true));\n    },\n  });\n  // TODO: innerHTML的处理\n}\n\nexport function getCurUrl(proxyLocation: Object): string {\n  const location = proxyLocation as Location;\n  return location.protocol + \"//\" + location.host + location.pathname;\n}\n\nexport function getAbsolutePath(url: string, base: string, hash?: boolean): string {\n  try {\n    // 为空值无需处理\n    if (url) {\n      // 需要处理hash的场景\n      if (hash && url.startsWith(\"#\")) return url;\n      return new URL(url, base).href;\n    } else return url;\n  } catch {\n    return url;\n  }\n}\n/**\n * 获取需要同步的url\n */\nexport function getSyncUrl(id: string, prefix: { [key: string]: string }): string {\n  let winUrlElement = anchorElementGenerator(window.location.href);\n  const queryMap = getAnchorElementQueryMap(winUrlElement);\n  winUrlElement = null;\n  const syncUrl = window.decodeURIComponent(queryMap[id] || \"\");\n  const validShortPath = syncUrl.match(/^{([^}]*)}/)?.[1];\n  if (prefix && validShortPath) {\n    return syncUrl.replace(`{${validShortPath}}`, prefix[validShortPath]);\n  }\n  return syncUrl;\n}\n// @ts-ignore\nexport const requestIdleCallback = window.requestIdleCallback || ((cb: Function) => setTimeout(cb, 1));\n\nexport function getContainer(container: string | HTMLElement): HTMLElement {\n  return typeof container === \"string\" ? (document.querySelector(container) as HTMLElement) : container;\n}\n\nexport function warn(msg: string, data?: any): void {\n  console?.warn(`[wujie warn]: ${msg}`, data);\n}\n\nexport function error(msg: string, data?: any): void {\n  console?.error(`[wujie error]: ${msg}`, data);\n}\n\nexport function getInlineCode(match) {\n  const start = match.indexOf(\">\") + 1;\n  const end = match.lastIndexOf(\"<\");\n  return match.substring(start, end);\n}\n\nexport function defaultGetPublicPath(entry) {\n  if (typeof entry === \"object\") {\n    return \"/\";\n  }\n  try {\n    const { origin, pathname } = new URL(entry, location.href);\n    const paths = pathname.split(\"/\");\n    // 移除最后一个元素\n    paths.pop();\n    return `${origin}${paths.join(\"/\")}/`;\n  } catch (e) {\n    console.warn(e);\n    return \"\";\n  }\n}\n\n/** [f1, f2, f3, f4] => f4(f3(f2(f1))) 函数柯里化 */\nexport function compose(fnList: Array<Function>): (...args: Array<string>) => string {\n  return function (code: string, ...args: Array<any>) {\n    return fnList.reduce((newCode, fn) => (isFunction(fn) ? fn(newCode, ...args) : newCode), code || \"\");\n  };\n}\n\n// 微任务\nexport function nextTick(cb: () => any): void {\n  Promise.resolve().then(cb);\n}\n\n//执行钩子函数\nexport function execHooks(plugins: Array<plugin>, hookName: string, ...args: Array<any>): void {\n  try {\n    if (plugins && plugins.length > 0) {\n      plugins\n        .map((plugin) => plugin[hookName])\n        .filter((hook) => isFunction(hook))\n        .forEach((hook) => hook(...args));\n    }\n  } catch (e) {\n    error(e);\n  }\n}\n\nexport function isScriptElement(element: HTMLElement): boolean {\n  return element.tagName?.toUpperCase() === \"SCRIPT\";\n}\n\nlet count = 1;\nexport function setTagToScript(element: HTMLScriptElement, tag?: string): void {\n  if (isScriptElement(element)) {\n    const scriptTag = tag || String(count++);\n    element.setAttribute(WUJIE_SCRIPT_ID, scriptTag);\n  }\n}\n\nexport function getTagFromScript(element: HTMLScriptElement): string | null {\n  if (isScriptElement(element)) {\n    return element.getAttribute(WUJIE_SCRIPT_ID);\n  }\n  return null;\n}\n\n// 合并缓存\nexport function mergeOptions(options: cacheOptions, cacheOptions: cacheOptions) {\n  return {\n    name: options.name,\n    el: options.el || cacheOptions?.el,\n    url: options.url || cacheOptions?.url,\n    html: options.html || cacheOptions?.html,\n    exec: options.exec !== undefined ? options.exec : cacheOptions?.exec,\n    replace: options.replace || cacheOptions?.replace,\n    fetch: options.fetch || cacheOptions?.fetch,\n    props: options.props || cacheOptions?.props,\n    sync: options.sync !== undefined ? options.sync : cacheOptions?.sync,\n    prefix: options.prefix || cacheOptions?.prefix,\n    loading: options.loading || cacheOptions?.loading,\n    // 默认 {}\n    attrs: options.attrs !== undefined ? options.attrs : cacheOptions?.attrs || {},\n    degradeAttrs: options.degradeAttrs !== undefined ? options.degradeAttrs : cacheOptions?.degradeAttrs || {},\n    // 默认 true\n    fiber: options.fiber !== undefined ? options.fiber : cacheOptions?.fiber !== undefined ? cacheOptions?.fiber : true,\n    alive: options.alive !== undefined ? options.alive : cacheOptions?.alive,\n    degrade: options.degrade !== undefined ? options.degrade : cacheOptions?.degrade,\n    plugins: options.plugins || cacheOptions?.plugins,\n    lifecycles: {\n      beforeLoad: options.beforeLoad || cacheOptions?.beforeLoad,\n      beforeMount: options.beforeMount || cacheOptions?.beforeMount,\n      afterMount: options.afterMount || cacheOptions?.afterMount,\n      beforeUnmount: options.beforeUnmount || cacheOptions?.beforeUnmount,\n      afterUnmount: options.afterUnmount || cacheOptions?.afterUnmount,\n      activated: options.activated || cacheOptions?.activated,\n      deactivated: options.deactivated || cacheOptions?.deactivated,\n      loadError: options.loadError || cacheOptions?.loadError,\n    },\n  };\n}\n\n/**\n * 事件触发器\n */\nexport function eventTrigger(el: HTMLElement | Window | Document, eventName: string, detail?: any) {\n  let event;\n  if (typeof window.CustomEvent === \"function\") {\n    event = new CustomEvent(eventName, { detail });\n  } else {\n    event = document.createEvent(\"CustomEvent\");\n    event.initCustomEvent(eventName, true, false, detail);\n  }\n  el.dispatchEvent(event);\n}\n\nexport function stopMainAppRun() {\n  warn(WUJIE_TIPS_STOP_APP_DETAIL);\n  throw new Error(WUJIE_TIPS_STOP_APP);\n}\n", "import { getInlineCode } from \"./utils\";\n\nconst ALL_SCRIPT_REGEX = /(<script[\\s\\S]*?>)[\\s\\S]*?<\\/script>/gi;\nconst SCRIPT_TAG_REGEX = /<(script)\\s+((?!type=('|\")text\\/ng-template\\3).)*?>.*?<\\/\\1>/is;\nconst SCRIPT_SRC_REGEX = /.*\\ssrc=('|\")?([^>'\"\\s]+)/;\nconst SCRIPT_TYPE_REGEX = /.*\\stype=('|\")?([^>'\"\\s]+)/;\nconst SCRIPT_ENTRY_REGEX = /.*\\sentry\\s*.*/;\nconst SCRIPT_ASYNC_REGEX = /.*\\sasync\\s*.*/;\nconst DEFER_ASYNC_REGEX = /.*\\sdefer\\s*.*/;\nconst SCRIPT_NO_MODULE_REGEX = /.*\\snomodule\\s*.*/;\nconst SCRIPT_MODULE_REGEX = /.*\\stype=('|\")?module('|\")?\\s*.*/;\nconst LINK_TAG_REGEX = /<(link)\\s+.*?>/gis;\nconst LINK_PRELOAD_OR_PREFETCH_REGEX = /\\srel=('|\")?(preload|prefetch|modulepreload)\\1/;\nconst LINK_HREF_REGEX = /.*\\shref=('|\")?([^>'\"\\s]+)/;\nconst LINK_AS_FONT = /.*\\sas=('|\")?font\\1.*/;\nconst STYLE_TAG_REGEX = /<style[^>]*>[\\s\\S]*?<\\/style>/gi;\nconst STYLE_TYPE_REGEX = /\\s+rel=('|\")?stylesheet\\1.*/;\nconst STYLE_HREF_REGEX = /.*\\shref=('|\")?([^>'\"\\s]+)/;\nconst HTML_COMMENT_REGEX = /<!--([\\s\\S]*?)-->/g;\nconst LINK_IGNORE_REGEX = /<link(\\s+|\\s+.+\\s+)ignore(\\s*|\\s+.*|=.*)>/is;\nconst STYLE_IGNORE_REGEX = /<style(\\s+|\\s+.+\\s+)ignore(\\s*|\\s+.*|=.*)>/is;\nconst SCRIPT_IGNORE_REGEX = /<script(\\s+|\\s+.+\\s+)ignore(\\s*|\\s+.*|=.*)>/is;\nconst CROSS_ORIGIN_REGEX = /.*\\scrossorigin=?('|\")?(use-credentials|anonymous)?('|\")?/i;\n\nexport type ScriptAttributes = {\n  [key: string]: string | boolean; // 所有属性都可以是字符串或布尔值类型\n};\n/** 脚本对象 */\nexport interface ScriptBaseObject {\n  /** 脚本地址，内联为空 */\n  src?: string;\n  /** 脚本是否为async执行 */\n  async?: boolean;\n  /** 脚本是否为defer执行 */\n  defer?: boolean;\n  /** 脚本是否为module模块 */\n  module?: boolean;\n  /** 脚本是否设置crossorigin */\n  crossorigin?: boolean;\n  /** 脚本crossorigin的类型 */\n  crossoriginType?: \"anonymous\" | \"use-credentials\" | \"\";\n  /** 脚本正则匹配属性 */\n  attrs?: ScriptAttributes;\n}\nexport type ScriptObject = ScriptBaseObject & {\n  /** 内联script的代码 */\n  content?: string;\n  /** 忽略，子应用自行请求 */\n  ignore?: boolean;\n  /** 子应用加载完毕事件 */\n  onload?: Function;\n};\n\n/** 样式对象 */\nexport interface StyleObject {\n  /** 样式地址， 内联为空 */\n  src?: string;\n  /** 样式代码 */\n  content?: string;\n  /** 忽略，子应用自行请求 */\n  ignore?: boolean;\n}\n\nexport interface TemplateResult {\n  template: string;\n  scripts: ScriptObject[];\n  styles: StyleObject[];\n  entry: string | ScriptObject;\n}\n\nfunction hasProtocol(url) {\n  return url.startsWith(\"//\") || url.startsWith(\"http://\") || url.startsWith(\"https://\");\n}\n\nfunction getEntirePath(path, baseURI) {\n  return new URL(path, baseURI).toString();\n}\n\nfunction isValidJavaScriptType(type) {\n  const handleTypes = [\n    \"text/javascript\",\n    \"module\",\n    \"application/javascript\",\n    \"text/ecmascript\",\n    \"application/ecmascript\",\n  ];\n  return !type || handleTypes.indexOf(type) !== -1;\n}\n\n/**\n * 解析标签的属性\n * @param scriptOuterHTML script 标签的 outerHTML\n * @returns 返回一个对象，包含 script 标签的所有属性\n */\nexport function parseTagAttributes(TagOuterHTML) {\n  const pattern = /<[-\\w]+\\s+([^>]*)>/i;\n  const matches = pattern.exec(TagOuterHTML);\n\n  if (!matches) {\n    return {};\n  }\n\n  const attributesString = matches[1];\n  const attributesPattern = /([^\\s=]+)\\s*=\\s*(['\"])(.*?)\\2/g;\n  const attributesObject = {};\n\n  let attributeMatches;\n  while ((attributeMatches = attributesPattern.exec(attributesString)) !== null) {\n    const attributeName = attributeMatches[1];\n    const attributeValue = attributeMatches[3];\n    attributesObject[attributeName] = attributeValue;\n  }\n\n  return attributesObject;\n}\n\nfunction isModuleScriptSupported() {\n  const s = window.document.createElement(\"script\");\n  return \"noModule\" in s;\n}\n\nexport const genLinkReplaceSymbol = (linkHref, preloadOrPrefetch = false) =>\n  `<!-- ${preloadOrPrefetch ? \"prefetch/preload/modulepreload\" : \"\"} link ${linkHref} replaced by wujie -->`;\nexport const getInlineStyleReplaceSymbol = (index) => `<!-- inline-style-${index} replaced by wujie -->`;\nexport const genScriptReplaceSymbol = (scriptSrc, type = \"\") =>\n  `<!-- ${type} script ${scriptSrc} replaced by wujie -->`;\nexport const inlineScriptReplaceSymbol = \"<!-- inline scripts replaced by wujie -->\";\nexport const genIgnoreAssetReplaceSymbol = (url) => `<!-- ignore asset ${url || \"file\"} replaced by wujie -->`;\nexport const genModuleScriptReplaceSymbol = (scriptSrc, moduleSupport) =>\n  `<!-- ${moduleSupport ? \"nomodule\" : \"module\"} script ${scriptSrc} ignored by wujie -->`;\n\n/**\n * parse the script link from the template\n * 1. collect stylesheets\n * 2. use global eval to evaluate the inline scripts\n *    see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Function#Difference_between_Function_constructor_and_function_declaration\n *    see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/eval#Do_not_ever_use_eval!\n * @param tpl\n * @param baseURI\n * @stripStyles whether to strip the css links\n * @returns {{template: void | string | *, scripts: *[], entry: *}}\n */\nexport default function processTpl(tpl: String, baseURI: String, postProcessTemplate?: Function): TemplateResult {\n  const scripts: ScriptObject[] = [];\n  const styles: StyleObject[] = [];\n  let entry = null;\n  const moduleSupport = isModuleScriptSupported();\n  const template = tpl\n\n    /*\n     remove html comment first\n     */\n    .replace(HTML_COMMENT_REGEX, \"\")\n\n    .replace(LINK_TAG_REGEX, (match) => {\n      /*\n       change the css link\n       */\n      const styleType = !!match.match(STYLE_TYPE_REGEX);\n      if (styleType) {\n        const styleHref = match.match(STYLE_HREF_REGEX);\n        const styleIgnore = match.match(LINK_IGNORE_REGEX);\n\n        if (styleHref) {\n          const href = styleHref && styleHref[2];\n          let newHref = href;\n\n          if (href && !hasProtocol(href)) {\n            newHref = getEntirePath(href, baseURI);\n          }\n          if (styleIgnore) {\n            return genIgnoreAssetReplaceSymbol(newHref);\n          }\n\n          styles.push({ src: newHref });\n          return genLinkReplaceSymbol(newHref);\n        }\n      }\n\n      const preloadOrPrefetchType =\n        match.match(LINK_PRELOAD_OR_PREFETCH_REGEX) && match.match(LINK_HREF_REGEX) && !match.match(LINK_AS_FONT);\n      if (preloadOrPrefetchType) {\n        const [, , linkHref] = match.match(LINK_HREF_REGEX);\n        return genLinkReplaceSymbol(linkHref, true);\n      }\n\n      return match;\n    })\n    .replace(STYLE_TAG_REGEX, (match) => {\n      if (STYLE_IGNORE_REGEX.test(match)) {\n        return genIgnoreAssetReplaceSymbol(\"style file\");\n      } else {\n        const code = getInlineCode(match);\n        styles.push({ src: \"\", content: code });\n        return getInlineStyleReplaceSymbol(styles.length - 1);\n      }\n    })\n    .replace(ALL_SCRIPT_REGEX, (match, scriptTag) => {\n      const scriptIgnore = scriptTag.match(SCRIPT_IGNORE_REGEX);\n      const isModuleScript = !!scriptTag.match(SCRIPT_MODULE_REGEX);\n      const isCrossOriginScript = scriptTag.match(CROSS_ORIGIN_REGEX);\n      const crossOriginType = isCrossOriginScript?.[2] || \"\";\n      const moduleScriptIgnore =\n        (moduleSupport && !!scriptTag.match(SCRIPT_NO_MODULE_REGEX)) || (!moduleSupport && isModuleScript);\n      // in order to keep the exec order of all javascripts\n\n      const matchedScriptTypeMatch = scriptTag.match(SCRIPT_TYPE_REGEX);\n      const matchedScriptType = matchedScriptTypeMatch && matchedScriptTypeMatch[2];\n      if (!isValidJavaScriptType(matchedScriptType)) {\n        return match;\n      }\n\n      // if it is a external script\n      if (SCRIPT_TAG_REGEX.test(match) && scriptTag.match(SCRIPT_SRC_REGEX)) {\n        /*\n         collect scripts and replace the ref\n         */\n\n        const matchedScriptEntry = scriptTag.match(SCRIPT_ENTRY_REGEX);\n        const matchedScriptSrcMatch = scriptTag.match(SCRIPT_SRC_REGEX);\n        let matchedScriptSrc = matchedScriptSrcMatch && matchedScriptSrcMatch[2];\n\n        if (entry && matchedScriptEntry) {\n          throw new SyntaxError(\"You should not set multiply entry script!\");\n        } else {\n          // append the domain while the script not have an protocol prefix\n          if (matchedScriptSrc && !hasProtocol(matchedScriptSrc)) {\n            matchedScriptSrc = getEntirePath(matchedScriptSrc, baseURI);\n          }\n\n          entry = entry || (matchedScriptEntry && matchedScriptSrc);\n        }\n\n        if (scriptIgnore) {\n          return genIgnoreAssetReplaceSymbol(matchedScriptSrc || \"js file\");\n        }\n\n        if (moduleScriptIgnore) {\n          return genModuleScriptReplaceSymbol(matchedScriptSrc || \"js file\", moduleSupport);\n        }\n\n        if (matchedScriptSrc) {\n          const isAsyncScript = !!scriptTag.match(SCRIPT_ASYNC_REGEX);\n          const isDeferScript = !!scriptTag.match(DEFER_ASYNC_REGEX);\n          scripts.push(\n            isAsyncScript || isDeferScript\n              ? {\n                  async: isAsyncScript,\n                  defer: isDeferScript,\n                  src: matchedScriptSrc,\n                  module: isModuleScript,\n                  crossorigin: !!isCrossOriginScript,\n                  crossoriginType: crossOriginType,\n                  attrs: parseTagAttributes(match),\n                }\n              : {\n                  src: matchedScriptSrc,\n                  module: isModuleScript,\n                  crossorigin: !!isCrossOriginScript,\n                  crossoriginType: crossOriginType,\n                  attrs: parseTagAttributes(match),\n                }\n          );\n          return genScriptReplaceSymbol(\n            matchedScriptSrc,\n            (isAsyncScript && \"async\") || (isDeferScript && \"defer\") || \"\"\n          );\n        }\n\n        return match;\n      } else {\n        if (scriptIgnore) {\n          return genIgnoreAssetReplaceSymbol(\"js file\");\n        }\n\n        if (moduleScriptIgnore) {\n          return genModuleScriptReplaceSymbol(\"js file\", moduleSupport);\n        }\n\n        // if it is an inline script\n        const code = getInlineCode(match);\n\n        // remove script blocks when all of these lines are comments.\n        const isPureCommentBlock = code.split(/[\\r\\n]+/).every((line) => !line.trim() || line.trim().startsWith(\"//\"));\n\n        if (!isPureCommentBlock && code) {\n          scripts.push({\n            src: \"\",\n            content: code,\n            module: isModuleScript,\n            crossorigin: !!isCrossOriginScript,\n            crossoriginType: crossOriginType,\n            attrs: parseTagAttributes(match),\n          });\n        }\n\n        return inlineScriptReplaceSymbol;\n      }\n    });\n\n  let tplResult = {\n    template,\n    scripts,\n    styles,\n    // set the last script as entry if have not set\n    entry: entry || scripts[scripts.length - 1],\n  };\n  if (typeof postProcessTemplate === \"function\") {\n    tplResult = postProcessTemplate(tplResult);\n  }\n\n  return tplResult;\n}\n", "import { plugin, ScriptObjectLoader } from \"./index\";\nimport { StyleObject } from \"./template\";\nimport { compose, getAbsolutePath } from \"./utils\";\n\ninterface loaderOption {\n  plugins: Array<plugin>;\n  replace: (code: string) => string;\n}\n\n/**\n * 获取柯里化 cssLoader\n */\nexport function getCssLoader({ plugins, replace }: loaderOption) {\n  return (code: string, src: string = \"\", base: string): string =>\n    compose(plugins.map((plugin) => plugin.cssLoader))(replace ? replace(code) : code, src, base);\n}\n\n/**\n * 获取柯里化 jsLoader\n */\nexport function getJsLoader({ plugins, replace }: loaderOption) {\n  return (code: string, src: string = \"\", base: string): string =>\n    compose(plugins.map((plugin) => plugin.jsLoader))(replace ? replace(code) : code, src, base);\n}\n\n/**\n * 获取预置插件\n */\ntype presetLoadersType = \"cssBeforeLoaders\" | \"cssAfterLoaders\" | \"jsBeforeLoaders\" | \"jsAfterLoaders\";\nexport function getPresetLoaders(loaderType: presetLoadersType, plugins: Array<plugin>): plugin[presetLoadersType] {\n  const loaders: (StyleObject | ScriptObjectLoader)[][] = plugins\n    .map((plugin) => plugin[loaderType])\n    .filter((loaders) => loaders?.length);\n  const res = loaders.reduce((preLoaders, curLoaders) => preLoaders.concat(curLoaders), []);\n  return loaderType === \"cssBeforeLoaders\" ? res.reverse() : res;\n}\n\n/**\n * 获取影响插件\n */\ntype effectLoadersType = \"jsExcludes\" | \"cssExcludes\" | \"jsIgnores\" | \"cssIgnores\";\nexport function getEffectLoaders(loaderType: effectLoadersType, plugins: Array<plugin>): plugin[effectLoadersType] {\n  return plugins\n    .map((plugin) => plugin[loaderType])\n    .filter((loaders) => loaders?.length)\n    .reduce((preLoaders, curLoaders) => preLoaders.concat(curLoaders), []);\n}\n\n// 判断 url 是否符合loader的规则\nexport function isMatchUrl(url: string, effectLoaders: plugin[effectLoadersType]): boolean {\n  return effectLoaders.some((loader) => (typeof loader === \"string\" ? url === loader : loader.test(url)));\n}\n\n/**\n * 转换子应用css内的相对地址成绝对地址\n */\nfunction cssRelativePathResolve(code: string, src: string, base: string) {\n  const baseUrl = src ? getAbsolutePath(src, base) : base;\n  /**\n   * https://developer.mozilla.org/en-US/docs/Web/CSS/url\n   *\n   * 旧: const urlReg = /(url\\((?!['\"]?(?:data):)['\"]?)([^'\")]*)(['\"]?\\))/g;\n   *\n   * 这里修改一下正则匹配，先匹配url(xxx)内的xxx，需要兼容一下嵌套括号，\n   * 再判断是否为base64，不再预先忽略data:前缀，防止base64的svg内仍有url被匹配\n   *\n   * eg:\n   * background: url(data:image/svg+xml;charset=utf-8,<svg><path fill=url(#a)></path></svg>)\n   * 以上样式会匹配出#a并进行修改，svg填充的路径就出问题了。\n   *\n   *  */\n  const urlReg = /url\\((['\"]?)((?:[^()]+|\\((?:[^()]+|\\([^()]*\\))*\\))*)(\\1)\\)/g;\n\n  return code.replace(urlReg, (_m, pre, url, post) => {\n    const base64Regx = /^data:/;\n    const isBase64 = base64Regx.test(url);\n\n    /** 如果匹配到data:前缀，则认为是base64文件，直接不进行路径替换吧 */\n    if (isBase64) {\n      return _m;\n    }\n\n    return `url(${pre}${getAbsolutePath(url, baseUrl)}${post})`;\n  });\n}\n\nconst defaultPlugin = {\n  cssLoader: cssRelativePathResolve,\n  // fix https://github.com/Tencent/wujie/issues/455\n  cssBeforeLoaders: [{ content: \"html {view-transition-name: none;}\" }],\n};\n\nexport function getPlugins(plugins: Array<plugin>): Array<plugin> {\n  return Array.isArray(plugins) ? [defaultPlugin, ...plugins] : [defaultPlugin];\n}\n\nexport default defaultPlugin;\n", "import processTpl, {\n  genLinkReplaceSymbol,\n  getInlineStyleReplaceSymbol,\n  ScriptObject,\n  ScriptBaseObject,\n  StyleObject,\n} from \"./template\";\nimport { defaultGetPublicPath, getInlineCode, requestIdleCallback, error, compose, getCurUrl } from \"./utils\";\nimport {\n  WUJIE_TIPS_NO_FETCH,\n  WUJIE_TIPS_SCRIPT_ERROR_REQUESTED,\n  WUJIE_TIPS_CSS_ERROR_REQUESTED,\n  WUJIE_TIPS_HTML_ERROR_REQUESTED,\n} from \"./constant\";\nimport { getEffectLoaders, isMatchUrl } from \"./plugin\";\nimport Wujie from \"./sandbox\";\nimport { plugin, loadErrorHandler } from \"./index\";\n\nexport type ScriptResultList = (ScriptBaseObject & { contentPromise: Promise<string> })[];\nexport type StyleResultList = { src: string; contentPromise: Promise<string>; ignore?: boolean }[];\n\ninterface htmlParseResult {\n  template: string;\n\n  assetPublicPath: string;\n\n  getExternalScripts(): ScriptResultList;\n\n  getExternalStyleSheets(): StyleResultList;\n}\n\ntype ImportEntryOpts = {\n  fetch?: typeof window.fetch;\n  fiber?: boolean;\n  plugins?: Array<plugin>;\n  loadError?: loadErrorHandler;\n};\n\nconst styleCache = {};\nconst scriptCache = {};\nconst embedHTMLCache = {};\n\nif (!window.fetch) {\n  error(WUJIE_TIPS_NO_FETCH);\n  throw new Error();\n}\nconst defaultFetch = window.fetch.bind(window);\n\nfunction defaultGetTemplate(tpl) {\n  return tpl;\n}\n\n/**\n * 处理css-loader\n */\nexport async function processCssLoader(\n  sandbox: Wujie,\n  template: string,\n  getExternalStyleSheets: () => StyleResultList\n): Promise<string> {\n  const curUrl = getCurUrl(sandbox.proxyLocation);\n  /** css-loader */\n  const composeCssLoader = compose(sandbox.plugins.map((plugin) => plugin.cssLoader));\n  const processedCssList: StyleResultList = getExternalStyleSheets().map(({ src, ignore, contentPromise }) => ({\n    src,\n    ignore,\n    contentPromise: contentPromise.then((content) => composeCssLoader(content, src, curUrl)),\n  }));\n  const embedHTML = await getEmbedHTML(template, processedCssList);\n  return sandbox.replace ? sandbox.replace(embedHTML) : embedHTML;\n}\n\n/**\n * convert external css link to inline style for performance optimization\n * @return embedHTML\n */\nasync function getEmbedHTML(template, styleResultList: StyleResultList): Promise<string> {\n  let embedHTML = template;\n\n  return Promise.all(\n    styleResultList.map((styleResult, index) =>\n      styleResult.contentPromise.then((content) => {\n        if (styleResult.src) {\n          embedHTML = embedHTML.replace(\n            genLinkReplaceSymbol(styleResult.src),\n            styleResult.ignore\n              ? `<link href=\"${styleResult.src}\" rel=\"stylesheet\" type=\"text/css\">`\n              : `<style>/* ${styleResult.src} */${content}</style>`\n          );\n        } else if (content) {\n          embedHTML = embedHTML.replace(\n            getInlineStyleReplaceSymbol(index),\n            `<style>/* inline-style-${index} */${content}</style>`\n          );\n        }\n      })\n    )\n  ).then(() => embedHTML);\n}\n\nconst isInlineCode = (code) => code.startsWith(\"<\");\n\nconst fetchAssets = (\n  src: string,\n  cache: Object,\n  fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response>,\n  cssFlag?: boolean,\n  loadError?: loadErrorHandler\n) =>\n  cache[src] ||\n  (cache[src] = fetch(src)\n    .then((response) => {\n      // usually browser treats 4xx and 5xx response of script loading as an error and will fire a script error event\n      // https://stackoverflow.com/questions/5625420/what-http-headers-responses-trigger-the-onerror-handler-on-a-script-tag/5625603\n      if (response.status >= 400) {\n        cache[src] = null;\n        if (cssFlag) {\n          error(WUJIE_TIPS_CSS_ERROR_REQUESTED, { src, response });\n          loadError?.(src, new Error(WUJIE_TIPS_CSS_ERROR_REQUESTED));\n          return \"\";\n        } else {\n          error(WUJIE_TIPS_SCRIPT_ERROR_REQUESTED, { src, response });\n          loadError?.(src, new Error(WUJIE_TIPS_SCRIPT_ERROR_REQUESTED));\n          throw new Error(WUJIE_TIPS_SCRIPT_ERROR_REQUESTED);\n        }\n      }\n      return response.text();\n    })\n    .catch((e) => {\n      cache[src] = null;\n      if (cssFlag) {\n        error(WUJIE_TIPS_CSS_ERROR_REQUESTED, src);\n        loadError?.(src, e);\n        return \"\";\n      } else {\n        error(WUJIE_TIPS_SCRIPT_ERROR_REQUESTED, src);\n        loadError?.(src, e);\n        return \"\";\n      }\n    }));\n\n// for prefetch\nexport function getExternalStyleSheets(\n  styles: StyleObject[],\n  fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response> = defaultFetch,\n  loadError: loadErrorHandler\n): StyleResultList {\n  return styles.map(({ src, content, ignore }) => {\n    // 内联\n    if (content) {\n      return { src: \"\", contentPromise: Promise.resolve(content) };\n    } else if (isInlineCode(src)) {\n      // if it is inline style\n      return { src: \"\", contentPromise: Promise.resolve(getInlineCode(src)) };\n    } else {\n      // external styles\n      return {\n        src,\n        ignore,\n        contentPromise: ignore ? Promise.resolve(\"\") : fetchAssets(src, styleCache, fetch, true, loadError),\n      };\n    }\n  });\n}\n\n// for prefetch\nexport function getExternalScripts(\n  scripts: ScriptObject[],\n  fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response> = defaultFetch,\n  loadError: loadErrorHandler,\n  fiber: boolean\n): ScriptResultList {\n  // module should be requested in iframe\n  return scripts.map((script) => {\n    const { src, async, defer, module, ignore } = script;\n    let contentPromise = null;\n    // async\n    if ((async || defer) && src && !module) {\n      contentPromise = new Promise((resolve, reject) =>\n        fiber\n          ? requestIdleCallback(() => fetchAssets(src, scriptCache, fetch, false, loadError).then(resolve, reject))\n          : fetchAssets(src, scriptCache, fetch, false, loadError).then(resolve, reject)\n      );\n      // module || ignore\n    } else if ((module && src) || ignore) {\n      contentPromise = Promise.resolve(\"\");\n      // inline\n    } else if (!src) {\n      contentPromise = Promise.resolve(script.content);\n      // outline\n    } else {\n      contentPromise = fetchAssets(src, scriptCache, fetch, false, loadError);\n    }\n    // refer https://html.spec.whatwg.org/multipage/scripting.html#attr-script-defer\n    if (module && !async) script.defer = true;\n    return { ...script, contentPromise };\n  });\n}\n\nexport default function importHTML(params: {\n  url: string;\n  html?: string;\n  opts: ImportEntryOpts;\n}): Promise<htmlParseResult> {\n  const { url, opts, html } = params;\n  const fetch = opts.fetch ?? defaultFetch;\n  const fiber = opts.fiber ?? true;\n  const { plugins, loadError } = opts;\n  const htmlLoader = plugins ? compose(plugins.map((plugin) => plugin.htmlLoader)) : defaultGetTemplate;\n  const jsExcludes = getEffectLoaders(\"jsExcludes\", plugins);\n  const cssExcludes = getEffectLoaders(\"cssExcludes\", plugins);\n  const jsIgnores = getEffectLoaders(\"jsIgnores\", plugins);\n  const cssIgnores = getEffectLoaders(\"cssIgnores\", plugins);\n  const getPublicPath = defaultGetPublicPath;\n\n  const getHtmlParseResult = (url, html, htmlLoader) =>\n    (html\n      ? Promise.resolve(html)\n      : fetch(url)\n          .then((response) => {\n            if (response.status >= 400) {\n              error(WUJIE_TIPS_HTML_ERROR_REQUESTED, { url, response });\n              loadError?.(url, new Error(WUJIE_TIPS_HTML_ERROR_REQUESTED));\n              return \"\";\n            }\n            return response.text();\n          })\n          .catch((e) => {\n            embedHTMLCache[url] = null;\n            loadError?.(url, e);\n            return Promise.reject(e);\n          })\n    ).then((html) => {\n      const assetPublicPath = getPublicPath(url);\n      const { template, scripts, styles } = processTpl(htmlLoader(html), assetPublicPath);\n      return {\n        template: template,\n        assetPublicPath,\n        getExternalScripts: () =>\n          getExternalScripts(\n            scripts\n              .filter((script) => !script.src || !isMatchUrl(script.src, jsExcludes))\n              .map((script) => ({ ...script, ignore: script.src && isMatchUrl(script.src, jsIgnores) })),\n            fetch,\n            loadError,\n            fiber\n          ),\n        getExternalStyleSheets: () =>\n          getExternalStyleSheets(\n            styles\n              .filter((style) => !style.src || !isMatchUrl(style.src, cssExcludes))\n              .map((style) => ({ ...style, ignore: style.src && isMatchUrl(style.src, cssIgnores) })),\n            fetch,\n            loadError\n          ),\n      };\n    });\n\n  if (opts?.plugins.some((plugin) => plugin.htmlLoader)) {\n    return getHtmlParseResult(url, html, htmlLoader);\n    // 没有html-loader可以做缓存\n  } else {\n    return embedHTMLCache[url] || (embedHTMLCache[url] = getHtmlParseResult(url, html, htmlLoader));\n  }\n}\n", "function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nexport { _createClass as default };", "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };", "function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };", "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };", "function _isNativeFunction(t) {\n  try {\n    return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n  } catch (n) {\n    return \"function\" == typeof t;\n  }\n}\nexport { _isNativeFunction as default };", "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "import isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nexport { _construct as default };", "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nimport isNativeFunction from \"./isNativeFunction.js\";\nimport construct from \"./construct.js\";\nfunction _wrapNativeSuper(t) {\n  var r = \"function\" == typeof Map ? new Map() : void 0;\n  return _wrapNativeSuper = function _wrapNativeSuper(t) {\n    if (null === t || !isNativeFunction(t)) return t;\n    if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n    if (void 0 !== r) {\n      if (r.has(t)) return r.get(t);\n      r.set(t, Wrapper);\n    }\n    function Wrapper() {\n      return construct(t, arguments, getPrototypeOf(this).constructor);\n    }\n    return Wrapper.prototype = Object.create(t.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: !1,\n        writable: !0,\n        configurable: !0\n      }\n    }), setPrototypeOf(Wrapper, t);\n  }, _wrapNativeSuper(t);\n}\nexport { _wrapNativeSuper as default };", "import <PERSON>ji<PERSON> from \"./sandbox\";\nimport { cacheOptions } from \"./index\";\nexport interface SandboxCache {\n  wujie?: Wujie;\n  options?: cacheOptions;\n}\n\nexport type appAddEventListenerOptions = AddEventListenerOptions & { targetWindow?: Window };\n\n// 全部无界实例和配置存储map\nexport const idToSandboxCacheMap = window.__POWERED_BY_WUJIE__\n  ? window.__WUJIE.inject.idToSandboxMap\n  : new Map<String, SandboxCache>();\n\nexport function getWujieById(id: String): Wujie | null {\n  return idToSandboxCacheMap.get(id)?.wujie || null;\n}\n\nexport function getOptionsById(id: String): cacheOptions | null {\n  return idToSandboxCacheMap.get(id)?.options || null;\n}\n\nexport function addSandboxCacheWithWujie(id: string, sandbox: Wujie): void {\n  const wujieCache = idToSandboxCacheMap.get(id);\n  if (wujieCache) idToSandboxCacheMap.set(id, { ...wujieCache, wujie: sandbox });\n  else idToSandboxCacheMap.set(id, { wujie: sandbox });\n}\n\nexport function deleteWujieById(id: string) {\n  const wujieCache = idToSandboxCacheMap.get(id);\n  if (wujieCache?.options) idToSandboxCacheMap.set(id, { options: wujieCache.options });\n  idToSandboxCacheMap.delete(id);\n}\n\nexport function addSandboxCacheWithOptions(id: string, options: cacheOptions): void {\n  const wujieCache = idToSandboxCacheMap.get(id);\n  if (wujieCache) idToSandboxCacheMap.set(id, { ...wujieCache, options });\n  else idToSandboxCacheMap.set(id, { options });\n}\n\n// 分类document上需要处理的属性，不同类型会进入不同的处理逻辑\nexport const documentProxyProperties = {\n  // 降级场景下需要本地特殊处理的属性\n  modifyLocalProperties: [\n    \"createElement\",\n    \"createTextNode\",\n    \"documentURI\",\n    \"URL\",\n    \"getElementsByTagName\",\n    \"getElementById\",\n  ],\n\n  // 子应用需要手动修正的属性方法\n  modifyProperties: [\n    \"createElement\",\n    \"createTextNode\",\n    \"documentURI\",\n    \"URL\",\n    \"getElementsByTagName\",\n    \"getElementsByClassName\",\n    \"getElementsByName\",\n    \"getElementById\",\n    \"querySelector\",\n    \"querySelectorAll\",\n    \"documentElement\",\n    \"scrollingElement\",\n    \"forms\",\n    \"images\",\n    \"links\",\n  ],\n\n  // 需要从shadowRoot中获取的属性\n  shadowProperties: [\n    \"activeElement\",\n    \"childElementCount\",\n    \"children\",\n    \"firstElementChild\",\n    \"firstChild\",\n    \"fullscreenElement\",\n    \"lastElementChild\",\n    \"pictureInPictureElement\",\n    \"pointerLockElement\",\n    \"styleSheets\",\n  ],\n\n  // 需要从shadowRoot中获取的方法\n  shadowMethods: [\n    \"append\",\n    \"contains\",\n    \"getSelection\",\n    \"elementFromPoint\",\n    \"elementsFromPoint\",\n    \"getAnimations\",\n    \"replaceChildren\",\n  ],\n\n  // 需要从主应用document中获取的属性\n  documentProperties: [\n    \"characterSet\",\n    \"compatMode\",\n    \"contentType\",\n    \"designMode\",\n    \"dir\",\n    \"doctype\",\n    \"embeds\",\n    \"fullscreenEnabled\",\n    \"hidden\",\n    \"implementation\",\n    \"lastModified\",\n    \"pictureInPictureEnabled\",\n    \"plugins\",\n    \"readyState\",\n    \"referrer\",\n    \"visibilityState\",\n    \"fonts\",\n  ],\n\n  // 需要从主应用document中获取的方法\n  documentMethods: [\n    \"execCommand\",\n    \"caretPositionFromPoint\",\n    \"createRange\",\n    \"exitFullscreen\",\n    \"exitPictureInPicture\",\n    \"getElementsByTagNameNS\",\n    \"hasFocus\",\n    \"prepend\",\n  ],\n\n  // 需要从主应用document中获取的事件\n  documentEvents: [\n    \"onpointerlockchange\",\n    \"onpointerlockerror\",\n    \"onbeforecopy\",\n    \"onbeforecut\",\n    \"onbeforepaste\",\n    \"onfreeze\",\n    \"onresume\",\n    \"onsearch\",\n    \"onfullscreenchange\",\n    \"onfullscreenerror\",\n    \"onsecuritypolicyviolation\",\n    \"onvisibilitychange\",\n  ],\n\n  // 无需修改原型的属性\n  ownerProperties: [\"head\", \"body\"],\n};\n\n// 需要挂载到子应用iframe document上的事件\nexport const appDocumentAddEventListenerEvents = [\"DOMContentLoaded\", \"readystatechange\"];\nexport const appDocumentOnEvents = [\"onreadystatechange\"];\n// 需要挂载到主应用document上的事件\nexport const mainDocumentAddEventListenerEvents = [\n  \"fullscreenchange\",\n  \"fullscreenerror\",\n  \"selectionchange\",\n  \"visibilitychange\",\n  \"wheel\",\n  \"keydown\",\n  \"keypress\",\n  \"keyup\",\n];\n\n// 需要同时挂载到主应用document和shadow上的事件（互斥）\nexport const mainAndAppAddEventListenerEvents = [\"gotpointercapture\", \"lostpointercapture\"];\n\n// 子应用window监听需要挂载到iframe沙箱上的事件\nexport const appWindowAddEventListenerEvents = [\n  \"hashchange\",\n  \"popstate\",\n  \"DOMContentLoaded\",\n  \"load\",\n  \"beforeunload\",\n  \"unload\",\n  \"message\",\n  \"error\",\n  \"unhandledrejection\",\n];\n\n// 子应用window.onXXX需要挂载到iframe沙箱上的事件\nexport const appWindowOnEvent = [\"onload\", \"onbeforeunload\", \"onunload\", \"onerror\", \"onunhandledrejection\"];\n\n// 相对路径问题元素的tag和attr的map\nexport const relativeElementTagAttrMap = {\n  IMG: \"src\",\n  A: \"href\",\n  SOURCE: \"src\",\n};\n\n// 需要单独处理的window属性\nexport const windowProxyProperties = [\"getComputedStyle\", \"visualViewport\", \"matchMedia\", \"DOMParser\"];\n\n// window白名单\nexport const windowRegWhiteList = [\n  /animationFrame$/i,\n  /resizeObserver$|mutationObserver$|intersectionObserver$/i,\n  /height$|width$|left$/i,\n  /^screen/i,\n  /CSSStyleSheet$/i,\n  /X$|Y$/,\n];\n\n// 保存原型方法\n// 子应用的Document.prototype已经被改写了\nexport const rawElementAppendChild = HTMLElement.prototype.appendChild;\nexport const rawElementRemoveChild = HTMLElement.prototype.removeChild;\nexport const rawElementContains = HTMLElement.prototype.contains;\nexport const rawHeadInsertBefore = HTMLHeadElement.prototype.insertBefore;\nexport const rawBodyInsertBefore = HTMLBodyElement.prototype.insertBefore;\nexport const rawAddEventListener = Node.prototype.addEventListener;\nexport const rawRemoveEventListener = Node.prototype.removeEventListener;\nexport const rawWindowAddEventListener = window.addEventListener;\nexport const rawWindowRemoveEventListener = window.removeEventListener;\nexport const rawAppendChild = Node.prototype.appendChild;\nexport const rawDocumentQuerySelector = window.__POWERED_BY_WUJIE__\n  ? window.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__\n  : Document.prototype.querySelector;\n", "import { getExternalStyleSheets, getExternalScripts } from \"./entry\";\nimport {\n  getWujieById,\n  rawAppendChild,\n  rawElementContains,\n  rawElementRemoveChild,\n  rawHeadInsertBefore,\n  rawBodyInsertBefore,\n  rawDocumentQuerySelector,\n  rawAddEventListener,\n  rawRemoveEventListener,\n} from \"./common\";\nimport {\n  isFunction,\n  isHijackingTag,\n  warn,\n  nextTick,\n  getCurUrl,\n  execHooks,\n  isScriptElement,\n  setTagToScript,\n  getTagFromScript,\n  setAttrsToElement,\n} from \"./utils\";\nimport { insertScriptToIframe, patchElementEffect } from \"./iframe\";\nimport Wujie from \"./sandbox\";\nimport { getPatchStyleElements } from \"./shadow\";\nimport { getCssLoader, getEffectLoaders, isMatchUrl } from \"./plugin\";\nimport { WUJIE_SCRIPT_ID, WUJIE_DATA_FLAG, WUJIE_TIPS_REPEAT_RENDER, WUJIE_TIPS_NO_SCRIPT } from \"./constant\";\nimport { ScriptObject, parseTagAttributes } from \"./template\";\n\nfunction patchCustomEvent(\n  e: CustomEvent,\n  elementGetter: () => HTMLScriptElement | HTMLLinkElement | null\n): CustomEvent {\n  Object.defineProperties(e, {\n    srcElement: {\n      get: elementGetter,\n    },\n    target: {\n      get: elementGetter,\n    },\n  });\n\n  return e;\n}\n\n/**\n * 手动触发事件回调\n */\nfunction manualInvokeElementEvent(element: HTMLLinkElement | HTMLScriptElement, event: string): void {\n  const customEvent = new CustomEvent(event);\n  const patchedEvent = patchCustomEvent(customEvent, () => element);\n  if (isFunction(element[`on${event}`])) {\n    element[`on${event}`](patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\n\n/**\n * 样式元素的css变量处理，每个stylesheetElement单独节流\n */\nfunction handleStylesheetElementPatch(stylesheetElement: HTMLStyleElement & { _patcher?: any }, sandbox: Wujie) {\n  if (!stylesheetElement.innerHTML || sandbox.degrade) return;\n  const patcher = () => {\n    const [hostStyleSheetElement, fontStyleSheetElement] = getPatchStyleElements([stylesheetElement.sheet]);\n    if (hostStyleSheetElement) {\n      sandbox.shadowRoot.head.appendChild(hostStyleSheetElement);\n    }\n    if (fontStyleSheetElement) {\n      sandbox.shadowRoot.host.appendChild(fontStyleSheetElement);\n    }\n    stylesheetElement._patcher = undefined;\n  };\n  if (stylesheetElement._patcher) {\n    clearTimeout(stylesheetElement._patcher);\n  }\n  stylesheetElement._patcher = setTimeout(patcher, 50);\n}\n\n/**\n * 劫持处理样式元素的属性\n */\nfunction patchStylesheetElement(\n  stylesheetElement: HTMLStyleElement & { _hasPatchStyle?: boolean },\n  cssLoader: (code: string, url: string, base: string) => string,\n  sandbox: Wujie,\n  curUrl: string\n) {\n  if (stylesheetElement._hasPatchStyle) return;\n  const innerHTMLDesc = Object.getOwnPropertyDescriptor(Element.prototype, \"innerHTML\");\n  const innerTextDesc = Object.getOwnPropertyDescriptor(HTMLElement.prototype, \"innerText\");\n  const textContentDesc = Object.getOwnPropertyDescriptor(Node.prototype, \"textContent\");\n  const RawInsertRule = stylesheetElement.sheet?.insertRule;\n  // 这个地方将cssRule加到innerHTML中去，防止子应用切换之后丢失\n  function patchSheetInsertRule() {\n    if (!RawInsertRule) return;\n    stylesheetElement.sheet.insertRule = (rule: string, index?: number): number => {\n      innerHTMLDesc ? (stylesheetElement.innerHTML += rule) : (stylesheetElement.innerText += rule);\n      return RawInsertRule.call(stylesheetElement.sheet, rule, index);\n    };\n  }\n  patchSheetInsertRule();\n\n  if (innerHTMLDesc) {\n    Object.defineProperties(stylesheetElement, {\n      innerHTML: {\n        get: function () {\n          return innerHTMLDesc.get.call(stylesheetElement);\n        },\n        set: function (code: string) {\n          innerHTMLDesc.set.call(stylesheetElement, cssLoader(code, \"\", curUrl));\n          nextTick(() => handleStylesheetElementPatch(this, sandbox));\n        },\n      },\n    });\n  }\n\n  Object.defineProperties(stylesheetElement, {\n    innerText: {\n      get: function () {\n        return innerTextDesc.get.call(stylesheetElement);\n      },\n      set: function (code: string) {\n        innerTextDesc.set.call(stylesheetElement, cssLoader(code, \"\", curUrl));\n        nextTick(() => handleStylesheetElementPatch(this, sandbox));\n      },\n    },\n    textContent: {\n      get: function () {\n        return textContentDesc.get.call(stylesheetElement);\n      },\n      set: function (code: string) {\n        textContentDesc.set.call(stylesheetElement, cssLoader(code, \"\", curUrl));\n        nextTick(() => handleStylesheetElementPatch(this, sandbox));\n      },\n    },\n    appendChild: {\n      value: function (node: Node): Node {\n        nextTick(() => handleStylesheetElementPatch(this, sandbox));\n        if (node.nodeType === Node.TEXT_NODE) {\n          const res = rawAppendChild.call(\n            stylesheetElement,\n            stylesheetElement.ownerDocument.createTextNode(cssLoader(node.textContent, \"\", curUrl))\n          );\n          // 当appendChild之后，样式元素的sheet对象发生改变，要重新patch\n          patchSheetInsertRule();\n          return res;\n        } else return rawAppendChild(node);\n      },\n    },\n    _hasPatchStyle: { get: () => true },\n  });\n}\n\nlet dynamicScriptExecStack = Promise.resolve();\nfunction rewriteAppendOrInsertChild(opts: {\n  rawDOMAppendOrInsertBefore: <T extends Node>(newChild: T, refChild?: Node | null) => T;\n  wujieId: string;\n}) {\n  return function appendChildOrInsertBefore<T extends Node>(\n    this: HTMLHeadElement | HTMLBodyElement,\n    newChild: T,\n    refChild?: Node | null\n  ) {\n    let element = newChild as any;\n    const { rawDOMAppendOrInsertBefore, wujieId } = opts;\n    const sandbox = getWujieById(wujieId);\n\n    const { styleSheetElements, replace, fetch, plugins, iframe, lifecycles, proxyLocation, fiber } = sandbox;\n\n    if (!isHijackingTag(element.tagName) || !wujieId) {\n      const res = rawDOMAppendOrInsertBefore.call(this, element, refChild) as T;\n      patchElementEffect(element, iframe.contentWindow);\n      execHooks(plugins, \"appendOrInsertElementHook\", element, iframe.contentWindow);\n      return res;\n    }\n\n    const iframeDocument = iframe.contentDocument;\n    const curUrl = getCurUrl(proxyLocation);\n\n    // TODO 过滤可以开放\n    if (element.tagName) {\n      switch (element.tagName?.toUpperCase()) {\n        case \"LINK\": {\n          const { href, rel, type } = element as HTMLLinkElement;\n          const styleFlag = rel === \"stylesheet\" || type === \"text/css\" || href.endsWith(\".css\");\n          // 非 stylesheet 不做处理\n          if (!styleFlag) {\n            const res = rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            execHooks(plugins, \"appendOrInsertElementHook\", element, iframe.contentWindow);\n            return res;\n          }\n          // 排除css\n          if (href && !isMatchUrl(href, getEffectLoaders(\"cssExcludes\", plugins))) {\n            getExternalStyleSheets(\n              [{ src: href, ignore: isMatchUrl(href, getEffectLoaders(\"cssIgnores\", plugins)) }],\n              fetch,\n              lifecycles.loadError\n            ).forEach(({ src, ignore, contentPromise }) =>\n              contentPromise.then(\n                (content) => {\n                  // 处理 ignore 样式\n                  const rawAttrs = parseTagAttributes(element.outerHTML);\n                  if (ignore && src) {\n                    // const stylesheetElement = iframeDocument.createElement(\"link\");\n                    // const attrs = {\n                    //   ...rawAttrs,\n                    //   type: \"text/css\",\n                    //   rel: \"stylesheet\",\n                    //   href: src,\n                    // };\n                    // setAttrsToElement(stylesheetElement, attrs);\n                    // rawDOMAppendOrInsertBefore.call(this, stylesheetElement, refChild);\n                    // manualInvokeElementEvent(element, \"load\");\n                    // 忽略的元素应该直接把对应元素插入，而不是用新的 link 标签进行替代插入，保证 element 的上下文正常\n                    rawDOMAppendOrInsertBefore.call(this, element, refChild);\n                  } else {\n                    // 记录js插入样式，子应用重新激活时恢复\n                    const stylesheetElement = iframeDocument.createElement(\"style\");\n                    // 处理css-loader插件\n                    const cssLoader = getCssLoader({ plugins, replace });\n                    stylesheetElement.innerHTML = cssLoader(content, src, curUrl);\n                    styleSheetElements.push(stylesheetElement);\n                    setAttrsToElement(stylesheetElement, rawAttrs);\n                    rawDOMAppendOrInsertBefore.call(this, stylesheetElement, refChild);\n                    // 处理样式补丁\n                    handleStylesheetElementPatch(stylesheetElement, sandbox);\n                    manualInvokeElementEvent(element, \"load\");\n                  }\n                  element = null;\n                },\n                () => {\n                  manualInvokeElementEvent(element, \"error\");\n                  element = null;\n                }\n              )\n            );\n          }\n\n          const comment = iframeDocument.createComment(`dynamic link ${href} replaced by wujie`);\n          return rawDOMAppendOrInsertBefore.call(this, comment, refChild);\n        }\n        case \"STYLE\": {\n          const stylesheetElement: HTMLStyleElement = newChild as any;\n          styleSheetElements.push(stylesheetElement);\n          const content = stylesheetElement.innerHTML;\n          const cssLoader = getCssLoader({ plugins, replace });\n          content && (stylesheetElement.innerHTML = cssLoader(content, \"\", curUrl));\n          const res = rawDOMAppendOrInsertBefore.call(this, element, refChild);\n          // 处理样式补丁\n          patchStylesheetElement(stylesheetElement, cssLoader, sandbox, curUrl);\n          handleStylesheetElementPatch(stylesheetElement, sandbox);\n          execHooks(plugins, \"appendOrInsertElementHook\", element, iframe.contentWindow);\n          return res;\n        }\n        case \"SCRIPT\": {\n          setTagToScript(element);\n          const { src, text, type, crossOrigin } = element as HTMLScriptElement;\n          // 排除js\n          if (src && !isMatchUrl(src, getEffectLoaders(\"jsExcludes\", plugins))) {\n            const execScript = (scriptResult: ScriptObject) => {\n              // 假如子应用被连续渲染两次，两次渲染会导致处理流程的交叉污染\n              if (sandbox.iframe === null) return warn(WUJIE_TIPS_REPEAT_RENDER);\n              const onload = () => {\n                manualInvokeElementEvent(element, \"load\");\n                element = null;\n              };\n              insertScriptToIframe({ ...scriptResult, onload }, sandbox.iframe.contentWindow, element);\n            };\n            const scriptOptions = {\n              src,\n              module: type === \"module\",\n              crossorigin: crossOrigin !== null,\n              crossoriginType: crossOrigin || \"\",\n              ignore: isMatchUrl(src, getEffectLoaders(\"jsIgnores\", plugins)),\n              attrs: parseTagAttributes(element.outerHTML),\n            } as ScriptObject;\n            getExternalScripts([scriptOptions], fetch, lifecycles.loadError, fiber).forEach((scriptResult) => {\n              dynamicScriptExecStack = dynamicScriptExecStack.then(() =>\n                scriptResult.contentPromise.then(\n                  (content) => {\n                    if (sandbox.execQueue === null) return warn(WUJIE_TIPS_REPEAT_RENDER);\n                    const execQueueLength = sandbox.execQueue?.length;\n                    sandbox.execQueue.push(() =>\n                      fiber\n                        ? sandbox.requestIdleCallback(() => {\n                            execScript({ ...scriptResult, content });\n                          })\n                        : execScript({ ...scriptResult, content })\n                    );\n                    // 同步脚本如果都执行完了，需要手动触发执行\n                    if (!execQueueLength) sandbox.execQueue.shift()();\n                  },\n                  () => {\n                    manualInvokeElementEvent(element, \"error\");\n                    element = null;\n                  }\n                )\n              );\n            });\n          } else {\n            const execQueueLength = sandbox.execQueue?.length;\n            sandbox.execQueue.push(() =>\n              fiber\n                ? sandbox.requestIdleCallback(() => {\n                    insertScriptToIframe(\n                      { src: null, content: text, attrs: parseTagAttributes(element.outerHTML) },\n                      sandbox.iframe.contentWindow,\n                      element\n                    );\n                  })\n                : insertScriptToIframe(\n                    { src: null, content: text, attrs: parseTagAttributes(element.outerHTML) },\n                    sandbox.iframe.contentWindow,\n                    element\n                  )\n            );\n            if (!execQueueLength) sandbox.execQueue.shift()();\n          }\n          // inline script never trigger the onload and onerror event\n          const comment = iframeDocument.createComment(`dynamic script ${src} replaced by wujie`);\n          return rawDOMAppendOrInsertBefore.call(this, comment, refChild);\n        }\n        // 修正子应用内部iframe的window.parent指向\n        case \"IFRAME\": {\n          // 嵌套的子应用的js-iframe需要插入子应用的js-iframe内部\n          if (element.getAttribute(WUJIE_DATA_FLAG) === \"\") {\n            return rawAppendChild.call(rawDocumentQuerySelector.call(this.ownerDocument, \"html\"), element);\n          }\n          const res = rawDOMAppendOrInsertBefore.call(this, element, refChild);\n          execHooks(plugins, \"appendOrInsertElementHook\", element, iframe.contentWindow);\n          return res;\n        }\n        default:\n      }\n    }\n  };\n}\n\nfunction findScriptElementFromIframe(rawElement: HTMLScriptElement, wujieId: string) {\n  const wujieTag = getTagFromScript(rawElement);\n  const sandbox = getWujieById(wujieId);\n  const { iframe } = sandbox;\n  const targetScript = iframe.contentWindow.__WUJIE_RAW_DOCUMENT_HEAD__.querySelector(\n    `script[${WUJIE_SCRIPT_ID}='${wujieTag}']`\n  );\n  if (targetScript === null) {\n    warn(WUJIE_TIPS_NO_SCRIPT, `<script ${WUJIE_SCRIPT_ID}='${wujieTag}'/>`);\n  }\n  return { targetScript, iframe };\n}\n\nfunction rewriteContains(opts: { rawElementContains: (other: Node | null) => boolean; wujieId: string }) {\n  return function contains(other: Node | null) {\n    const element = other as HTMLElement;\n    const { rawElementContains, wujieId } = opts;\n    if (element && isScriptElement(element)) {\n      const { targetScript } = findScriptElementFromIframe(element as HTMLScriptElement, wujieId);\n      return targetScript !== null;\n    }\n    return rawElementContains(element);\n  };\n}\n\nfunction rewriteRemoveChild(opts: { rawElementRemoveChild: <T extends Node>(child: T) => T; wujieId: string }) {\n  return function removeChild(child: Node) {\n    const element = child as HTMLElement;\n    const { rawElementRemoveChild, wujieId } = opts;\n    if (element && isScriptElement(element)) {\n      const { targetScript, iframe } = findScriptElementFromIframe(element as HTMLScriptElement, wujieId);\n      if (targetScript !== null) {\n        return iframe.contentWindow.__WUJIE_RAW_DOCUMENT_HEAD__.removeChild(targetScript);\n      }\n      return null;\n    }\n    return rawElementRemoveChild(element);\n  };\n}\n\n/**\n * 记录head和body的事件，等重新渲染复用head和body时需要清空事件\n */\nfunction patchEventListener(element: HTMLHeadElement | HTMLBodyElement) {\n  const listenerMap = new Map<string, EventListenerOrEventListenerObject[]>();\n  element._cacheListeners = listenerMap;\n\n  element.addEventListener = (\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ) => {\n    const listeners = listenerMap.get(type) || [];\n    listenerMap.set(type, [...listeners, listener]);\n    return rawAddEventListener.call(element, type, listener, options);\n  };\n\n  element.removeEventListener = (\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ) => {\n    const typeListeners = listenerMap.get(type);\n    const index = typeListeners?.indexOf(listener);\n    if (typeListeners?.length && index !== -1) {\n      typeListeners.splice(index, 1);\n    }\n    return rawRemoveEventListener.call(element, type, listener, options);\n  };\n}\n\n/**\n * 清空head和body的绑定的事件\n */\nexport function removeEventListener(element: HTMLHeadElement | HTMLBodyElement) {\n  const listenerMap = element._cacheListeners;\n  [...listenerMap.entries()].forEach(([type, listeners]) => {\n    listeners.forEach((listener) => rawRemoveEventListener.call(element, type, listener));\n  });\n}\n\n/**\n * patch head and body in render\n * intercept appendChild and insertBefore\n */\nexport function patchRenderEffect(render: ShadowRoot | Document, id: string, degrade: boolean): void {\n  // 降级场景dom渲染在iframe中，iframe移动后事件自动销毁，不需要记录\n  if (!degrade) {\n    patchEventListener(render.head);\n    patchEventListener(render.body as HTMLBodyElement);\n  }\n\n  render.head.appendChild = rewriteAppendOrInsertChild({\n    rawDOMAppendOrInsertBefore: rawAppendChild,\n    wujieId: id,\n  }) as typeof rawAppendChild;\n  render.head.insertBefore = rewriteAppendOrInsertChild({\n    rawDOMAppendOrInsertBefore: rawHeadInsertBefore as any,\n    wujieId: id,\n  }) as typeof rawHeadInsertBefore;\n  render.head.removeChild = rewriteRemoveChild({\n    rawElementRemoveChild: rawElementRemoveChild.bind(render.head),\n    wujieId: id,\n  }) as typeof rawElementRemoveChild;\n  render.head.contains = rewriteContains({\n    rawElementContains: rawElementContains.bind(render.head),\n    wujieId: id,\n  }) as typeof rawElementContains;\n  render.contains = rewriteContains({\n    rawElementContains: rawElementContains.bind(render),\n    wujieId: id,\n  }) as typeof rawElementContains;\n  render.body.appendChild = rewriteAppendOrInsertChild({\n    rawDOMAppendOrInsertBefore: rawAppendChild,\n    wujieId: id,\n  }) as typeof rawAppendChild;\n  render.body.insertBefore = rewriteAppendOrInsertChild({\n    rawDOMAppendOrInsertBefore: rawBodyInsertBefore as any,\n    wujieId: id,\n  }) as typeof rawBodyInsertBefore;\n}\n", "import {\n  WUJ<PERSON>_APP_ID,\n  WU<PERSON>IE_IFRAME_CLASS,\n  WUJIE_SHADE_STYLE,\n  CONTAINER_POSITION_DATA_FLAG,\n  CONTAINER_OVERFLOW_DATA_FLAG,\n  LOADING_DATA_FLAG,\n  WUJIE_LOADING_STYLE,\n  WUJIE_LOADING_SVG,\n} from \"./constant\";\nimport {\n  getWujieById,\n  rawAppendChild,\n  rawElementAppendChild,\n  rawElementRemoveChild,\n  relativeElementTagAttrMap,\n} from \"./common\";\nimport { getExternalStyleSheets } from \"./entry\";\nimport Wujie from \"./sandbox\";\nimport { patchElementEffect } from \"./iframe\";\nimport { patchRenderEffect } from \"./effect\";\nimport { getCssLoader, getPresetLoaders } from \"./plugin\";\nimport { getAbsolutePath, getContainer, getCurUrl, setAttrsToElement } from \"./utils\";\n\nconst cssSelectorMap = {\n  \":root\": \":host\",\n};\n\ndeclare global {\n  interface ShadowRoot {\n    head: HTMLHeadElement;\n    body: HTMLBodyElement;\n  }\n}\n\n/**\n * 定义 wujie webComponent，将shadow包裹并获得dom装载和卸载的生命周期\n */\nexport function defineWujieWebComponent() {\n  const customElements = window.customElements;\n  if (customElements && !customElements?.get(\"wujie-app\")) {\n    class WujieApp extends HTMLElement {\n      connectedCallback(): void {\n        if (this.shadowRoot) return;\n        const shadowRoot = this.attachShadow({ mode: \"open\" });\n        const sandbox = getWujieById(this.getAttribute(WUJIE_APP_ID));\n        patchElementEffect(shadowRoot, sandbox.iframe.contentWindow);\n        sandbox.shadowRoot = shadowRoot;\n      }\n\n      disconnectedCallback(): void {\n        const sandbox = getWujieById(this.getAttribute(WUJIE_APP_ID));\n        sandbox?.unmount();\n      }\n    }\n    customElements?.define(\"wujie-app\", WujieApp);\n  }\n}\n\nexport function createWujieWebComponent(id: string): HTMLElement {\n  const contentElement = window.document.createElement(\"wujie-app\");\n  contentElement.setAttribute(WUJIE_APP_ID, id);\n  contentElement.classList.add(WUJIE_IFRAME_CLASS);\n  return contentElement;\n}\n\n/**\n * 将准备好的内容插入容器\n */\nexport function renderElementToContainer(\n  element: Element | ChildNode,\n  selectorOrElement: string | HTMLElement\n): HTMLElement {\n  const container = getContainer(selectorOrElement);\n  if (container && !container.contains(element)) {\n    // 有 loading 无需清理，已经清理过了\n    if (!container.querySelector(`div[${LOADING_DATA_FLAG}]`)) {\n      // 清除内容\n      clearChild(container);\n    }\n    // 插入元素\n    if (element) {\n      rawElementAppendChild.call(container, element);\n    }\n  }\n  return container;\n}\n\n/**\n * 将降级的iframe挂在到容器上并进行初始化\n */\nexport function initRenderIframeAndContainer(\n  id: string,\n  parent: string | HTMLElement,\n  degradeAttrs: { [key: string]: any } = {}\n): { iframe: HTMLIFrameElement; container: HTMLElement } {\n  const iframe = createIframeContainer(id, degradeAttrs);\n  const container = renderElementToContainer(iframe, parent);\n  const contentDocument = iframe.contentWindow.document;\n  contentDocument.open();\n  contentDocument.write(\"<!DOCTYPE html><html><head></head><body></body></html>\");\n  contentDocument.close();\n  return { iframe, container };\n}\n\n/**\n * 处理css-before-loader 以及 css-after-loader\n */\nasync function processCssLoaderForTemplate(sandbox: Wujie, html: HTMLHtmlElement): Promise<HTMLHtmlElement> {\n  const document = sandbox.iframe.contentDocument;\n  const { plugins, replace, proxyLocation } = sandbox;\n  const cssLoader = getCssLoader({ plugins, replace });\n  const cssBeforeLoaders = getPresetLoaders(\"cssBeforeLoaders\", plugins);\n  const cssAfterLoaders = getPresetLoaders(\"cssAfterLoaders\", plugins);\n  const curUrl = getCurUrl(proxyLocation);\n\n  return await Promise.all([\n    Promise.all(\n      getExternalStyleSheets(cssBeforeLoaders, sandbox.fetch, sandbox.lifecycles.loadError).map(\n        ({ src, contentPromise }) => contentPromise.then((content) => ({ src, content }))\n      )\n    ).then((contentList) => {\n      contentList.forEach(({ src, content }) => {\n        if (!content) return;\n        const styleElement = document.createElement(\"style\");\n        styleElement.setAttribute(\"type\", \"text/css\");\n        styleElement.appendChild(document.createTextNode(content ? cssLoader(content, src, curUrl) : content));\n        const head = html.querySelector(\"head\");\n        const body = html.querySelector(\"body\");\n        html.insertBefore(styleElement, head || body || html.firstChild);\n      });\n    }),\n    Promise.all(\n      getExternalStyleSheets(cssAfterLoaders, sandbox.fetch, sandbox.lifecycles.loadError).map(\n        ({ src, contentPromise }) => contentPromise.then((content) => ({ src, content }))\n      )\n    ).then((contentList) => {\n      contentList.forEach(({ src, content }) => {\n        if (!content) return;\n        const styleElement = document.createElement(\"style\");\n        styleElement.setAttribute(\"type\", \"text/css\");\n        styleElement.appendChild(document.createTextNode(content ? cssLoader(content, src, curUrl) : content));\n        html.appendChild(styleElement);\n      });\n    }),\n  ]).then(\n    () => html,\n    () => html\n  );\n}\n\n// 替换html的head和body\nfunction replaceHeadAndBody(html: HTMLHtmlElement, head: HTMLHeadElement, body: HTMLBodyElement): HTMLHtmlElement {\n  const headElement = html.querySelector(\"head\");\n  const bodyElement = html.querySelector(\"body\");\n  if (headElement) {\n    while (headElement.firstChild) {\n      rawAppendChild.call(head, headElement.firstChild.cloneNode(true));\n      headElement.removeChild(headElement.firstChild);\n    }\n    headElement.parentNode.replaceChild(head, headElement);\n  }\n  if (bodyElement) {\n    while (bodyElement.firstChild) {\n      rawAppendChild.call(body, bodyElement.firstChild.cloneNode(true));\n      bodyElement.removeChild(bodyElement.firstChild);\n    }\n    bodyElement.parentNode.replaceChild(body, bodyElement);\n  }\n  return html;\n}\n\n/**\n * 将template渲染成html元素\n */\nfunction renderTemplateToHtml(iframeWindow: Window, template: string): HTMLHtmlElement {\n  const sandbox = iframeWindow.__WUJIE;\n  const { head, body, alive, execFlag } = sandbox;\n  const document = iframeWindow.document;\n  const parser = new DOMParser();\n  const parsedDocument = parser.parseFromString(template, \"text/html\");\n\n  // 无论 template 是否包含html，documentElement 必然是 HTMLHtmlElement\n  const parsedHtml = parsedDocument.documentElement as HTMLHtmlElement;\n  const sourceAttributes = parsedHtml.attributes;\n  let html = document.createElement(\"html\");\n  html.innerHTML = template;\n  for (let i = 0; i < sourceAttributes.length; i++) {\n    html.setAttribute(sourceAttributes[i].name, sourceAttributes[i].value);\n  }\n  // 组件多次渲染，head和body必须一直使用同一个来应对被缓存的场景\n  if (!alive && execFlag) {\n    html = replaceHeadAndBody(html, head, body);\n  } else {\n    sandbox.head = html.querySelector(\"head\");\n    sandbox.body = html.querySelector(\"body\");\n  }\n  const ElementIterator = document.createTreeWalker(html, NodeFilter.SHOW_ELEMENT, null, false);\n  let nextElement = ElementIterator.currentNode as HTMLElement;\n  while (nextElement) {\n    patchElementEffect(nextElement, iframeWindow);\n    const relativeAttr = relativeElementTagAttrMap[nextElement.tagName];\n    const url = nextElement[relativeAttr];\n    if (relativeAttr) nextElement.setAttribute(relativeAttr, getAbsolutePath(url, nextElement.baseURI || \"\"));\n    nextElement = ElementIterator.nextNode() as HTMLElement;\n  }\n  if (!html.querySelector(\"head\")) {\n    const head = document.createElement(\"head\");\n    html.appendChild(head);\n  }\n  if (!html.querySelector(\"body\")) {\n    const body = document.createElement(\"body\");\n    html.appendChild(body);\n  }\n  return html;\n}\n\n/**\n * 将template渲染到shadowRoot\n */\nexport async function renderTemplateToShadowRoot(\n  shadowRoot: ShadowRoot,\n  iframeWindow: Window,\n  template: string\n): Promise<void> {\n  const html = renderTemplateToHtml(iframeWindow, template);\n  // 处理 css-before-loader 和 css-after-loader\n  const processedHtml = await processCssLoaderForTemplate(iframeWindow.__WUJIE, html);\n  // change ownerDocument\n  shadowRoot.appendChild(processedHtml);\n  const shade = document.createElement(\"div\");\n  shade.setAttribute(\"style\", WUJIE_SHADE_STYLE);\n  processedHtml.insertBefore(shade, processedHtml.firstChild);\n  shadowRoot.head = shadowRoot.querySelector(\"head\");\n  shadowRoot.body = shadowRoot.querySelector(\"body\");\n\n  // 修复 html parentNode\n  Object.defineProperty(shadowRoot.firstChild, \"parentNode\", {\n    enumerable: true,\n    configurable: true,\n    get: () => iframeWindow.document,\n  });\n\n  patchRenderEffect(shadowRoot, iframeWindow.__WUJIE.id, false);\n}\n\nexport function createIframeContainer(id: string, degradeAttrs: { [key: string]: any } = {}): HTMLIFrameElement {\n  const iframe = document.createElement(\"iframe\");\n  const defaultStyle = \"height:100%;width:100%\";\n  setAttrsToElement(iframe, {\n    ...degradeAttrs,\n    style: [defaultStyle, degradeAttrs.style].join(\";\"),\n    [WUJIE_APP_ID]: id,\n  });\n  return iframe;\n}\n\n/**\n * 将template渲染到iframe\n */\nexport async function renderTemplateToIframe(\n  renderDocument: Document,\n  iframeWindow: Window,\n  template: string\n): Promise<void> {\n  // 插入template\n  const html = renderTemplateToHtml(iframeWindow, template);\n  // 处理 css-before-loader 和 css-after-loader\n  const processedHtml = await processCssLoaderForTemplate(iframeWindow.__WUJIE, html);\n  renderDocument.replaceChild(processedHtml, renderDocument.documentElement);\n\n  // 修复 html parentNode\n  Object.defineProperty(renderDocument.documentElement, \"parentNode\", {\n    enumerable: true,\n    configurable: true,\n    get: () => iframeWindow.document,\n  });\n\n  patchRenderEffect(renderDocument, iframeWindow.__WUJIE.id, true);\n}\n\n/**\n * 清除Element所有节点\n */\nexport function clearChild(root: ShadowRoot | Node): void {\n  // 清除内容\n  while (root?.firstChild) {\n    rawElementRemoveChild.call(root, root.firstChild);\n  }\n}\n\n/**\n * 给容器添加loading\n */\nexport function addLoading(el: string | HTMLElement, loading: HTMLElement): void {\n  const container = getContainer(el);\n  clearChild(container);\n  // 给容器设置一些样式，防止 loading 抖动\n  let containerStyles = null;\n  try {\n    containerStyles = window.getComputedStyle(container);\n  } catch {\n    return;\n  }\n  if (containerStyles.position === \"static\") {\n    container.setAttribute(CONTAINER_POSITION_DATA_FLAG, containerStyles.position);\n    container.setAttribute(\n      CONTAINER_OVERFLOW_DATA_FLAG,\n      containerStyles.overflow === \"visible\" ? \"\" : containerStyles.overflow\n    );\n    container.style.setProperty(\"position\", \"relative\");\n    container.style.setProperty(\"overflow\", \"hidden\");\n  } else if ([\"relative\", \"sticky\"].includes(containerStyles.position)) {\n    container.setAttribute(\n      CONTAINER_OVERFLOW_DATA_FLAG,\n      containerStyles.overflow === \"visible\" ? \"\" : containerStyles.overflow\n    );\n    container.style.setProperty(\"overflow\", \"hidden\");\n  }\n  const loadingContainer = document.createElement(\"div\");\n  loadingContainer.setAttribute(LOADING_DATA_FLAG, \"\");\n  loadingContainer.setAttribute(\"style\", WUJIE_LOADING_STYLE);\n  if (loading) loadingContainer.appendChild(loading);\n  else loadingContainer.innerHTML = WUJIE_LOADING_SVG;\n  container.appendChild(loadingContainer);\n}\n/**\n * 移除loading\n */\nexport function removeLoading(el: HTMLElement): void {\n  // 去除容器设置的样式\n  const positionFlag = el.getAttribute(CONTAINER_POSITION_DATA_FLAG);\n  const overflowFlag = el.getAttribute(CONTAINER_OVERFLOW_DATA_FLAG);\n  if (positionFlag) el.style.removeProperty(\"position\");\n  if (overflowFlag !== null) {\n    overflowFlag ? el.style.setProperty(\"overflow\", overflowFlag) : el.style.removeProperty(\"overflow\");\n  }\n  el.removeAttribute(CONTAINER_POSITION_DATA_FLAG);\n  el.removeAttribute(CONTAINER_OVERFLOW_DATA_FLAG);\n  const loadingContainer = el.querySelector(`div[${LOADING_DATA_FLAG}]`);\n  loadingContainer && el.removeChild(loadingContainer);\n}\n/**\n * 获取修复好的样式元素\n * 主要是针对对root样式和font-face样式\n */\nexport function getPatchStyleElements(rootStyleSheets: Array<CSSStyleSheet>): Array<HTMLStyleElement | null> {\n  const rootCssRules = [];\n  const fontCssRules = [];\n  const rootStyleReg = /:root/g;\n\n  // 找出root的cssRules\n  for (let i = 0; i < rootStyleSheets.length; i++) {\n    const cssRules = rootStyleSheets[i]?.cssRules ?? [];\n    for (let j = 0; j < cssRules.length; j++) {\n      const cssRuleText = cssRules[j].cssText;\n      // 如果是root的cssRule\n      if (rootStyleReg.test(cssRuleText)) {\n        rootCssRules.push(cssRuleText.replace(rootStyleReg, (match) => cssSelectorMap[match]));\n      }\n      // 如果是font-face的cssRule\n      if (cssRules[j].type === CSSRule.FONT_FACE_RULE) {\n        fontCssRules.push(cssRuleText);\n      }\n    }\n  }\n\n  let rootStyleSheetElement = null;\n  let fontStyleSheetElement = null;\n\n  // 复制到host上\n  if (rootCssRules.length) {\n    rootStyleSheetElement = window.document.createElement(\"style\");\n    rootStyleSheetElement.innerHTML = rootCssRules.join(\"\");\n  }\n\n  if (fontCssRules.length) {\n    fontStyleSheetElement = window.document.createElement(\"style\");\n    fontStyleSheetElement.innerHTML = fontCssRules.join(\"\");\n  }\n\n  return [rootStyleSheetElement, fontStyleSheetElement];\n}\n", "import { anchorElementGenerator, getAnchorElementQueryMap, getSyncUrl, appRouteParse, getDegradeIframe } from \"./utils\";\nimport { renderIframeReplaceApp, patchEventTimeStamp } from \"./iframe\";\nimport { renderElementToContainer, initRenderIframeAndContainer } from \"./shadow\";\nimport { getWujieById, rawDocumentQuerySelector } from \"./common\";\n\n/**\n * 同步子应用路由到主应用路由\n */\nexport function syncUrlToWindow(iframeWindow: Window): void {\n  const { sync, id, prefix } = iframeWindow.__WUJIE;\n  let winUrlElement = anchorElementGenerator(window.location.href);\n  const queryMap = getAnchorElementQueryMap(winUrlElement);\n  // 非同步且url上没有当前id的查询参数，否则就要同步参数或者清理参数\n  if (!sync && !queryMap[id]) return (winUrlElement = null);\n  const curUrl = iframeWindow.location.pathname + iframeWindow.location.search + iframeWindow.location.hash;\n  let validShortPath = \"\";\n  // 处理短路径\n  if (prefix) {\n    Object.keys(prefix).forEach((shortPath) => {\n      const longPath = prefix[shortPath];\n      // 找出最长匹配路径\n      if (curUrl.startsWith(longPath) && (!validShortPath || longPath.length > prefix[validShortPath].length)) {\n        validShortPath = shortPath;\n      }\n    });\n  }\n  // 同步\n  if (sync) {\n    queryMap[id] = window.encodeURIComponent(\n      validShortPath ? curUrl.replace(prefix[validShortPath], `{${validShortPath}}`) : curUrl\n    );\n    // 清理\n  } else {\n    delete queryMap[id];\n  }\n  const newQuery =\n    \"?\" +\n    Object.keys(queryMap)\n      .map((key) => key + \"=\" + queryMap[key])\n      .join(\"&\");\n  winUrlElement.search = newQuery;\n  if (winUrlElement.href !== window.location.href) {\n    window.history.replaceState(null, \"\", winUrlElement.href);\n  }\n  winUrlElement = null;\n}\n\n/**\n * 同步主应用路由到子应用\n */\nexport function syncUrlToIframe(iframeWindow: Window): void {\n  // 获取当前路由路径\n  const { pathname, search, hash } = iframeWindow.location;\n  const { id, url, sync, execFlag, prefix, inject } = iframeWindow.__WUJIE;\n\n  // 只在浏览器刷新或者第一次渲染时同步\n  const idUrl = sync && !execFlag ? getSyncUrl(id, prefix) : url;\n  // 排除href跳转情况\n  const syncUrl = (/^http/.test(idUrl) ? null : idUrl) || url;\n  const { appRoutePath } = appRouteParse(syncUrl);\n\n  const preAppRoutePath = pathname + search + hash;\n  if (preAppRoutePath !== appRoutePath) {\n    iframeWindow.history.replaceState(null, \"\", inject.mainHostPath + appRoutePath);\n  }\n}\n\n/**\n * 清理非激活态的子应用同步参数\n * 主应用采用hash模式时，切换子应用后已销毁的子应用同步参数还存在需要手动清理\n */\nexport function clearInactiveAppUrl(): void {\n  let winUrlElement = anchorElementGenerator(window.location.href);\n  const queryMap = getAnchorElementQueryMap(winUrlElement);\n  Object.keys(queryMap).forEach((id) => {\n    const sandbox = getWujieById(id);\n    if (!sandbox) return;\n    // 子应用执行过并且已经失活才需要清除\n    if (sandbox.execFlag && sandbox.sync && !sandbox.hrefFlag && !sandbox.activeFlag) {\n      delete queryMap[id];\n    }\n  });\n  const newQuery =\n    \"?\" +\n    Object.keys(queryMap)\n      .map((key) => key + \"=\" + queryMap[key])\n      .join(\"&\");\n  winUrlElement.search = newQuery;\n  if (winUrlElement.href !== window.location.href) {\n    window.history.replaceState(null, \"\", winUrlElement.href);\n  }\n  winUrlElement = null;\n}\n\n/**\n * 推送指定url到主应用路由\n */\nexport function pushUrlToWindow(id: string, url: string): void {\n  let winUrlElement = anchorElementGenerator(window.location.href);\n  const queryMap = getAnchorElementQueryMap(winUrlElement);\n  queryMap[id] = window.encodeURIComponent(url);\n  const newQuery =\n    \"?\" +\n    Object.keys(queryMap)\n      .map((key) => key + \"=\" + queryMap[key])\n      .join(\"&\");\n  winUrlElement.search = newQuery;\n  window.history.pushState(null, \"\", winUrlElement.href);\n  winUrlElement = null;\n}\n\n/**\n * 应用跳转(window.location.href)情况路由处理\n */\nexport function processAppForHrefJump(): void {\n  window.addEventListener(\"popstate\", () => {\n    let winUrlElement = anchorElementGenerator(window.location.href);\n    const queryMap = getAnchorElementQueryMap(winUrlElement);\n    winUrlElement = null;\n    Object.keys(queryMap)\n      .map((id) => getWujieById(id))\n      .filter((sandbox) => sandbox)\n      .forEach((sandbox) => {\n        const url = queryMap[sandbox.id];\n        const iframeBody = rawDocumentQuerySelector.call(sandbox.iframe.contentDocument, \"body\");\n        // 前进href\n        if (/http/.test(url)) {\n          if (sandbox.degrade) {\n            renderElementToContainer(sandbox.document.documentElement, iframeBody);\n            renderIframeReplaceApp(\n              window.decodeURIComponent(url),\n              getDegradeIframe(sandbox.id).parentElement,\n              sandbox.degradeAttrs\n            );\n          } else\n            renderIframeReplaceApp(\n              window.decodeURIComponent(url),\n              sandbox.shadowRoot.host.parentElement,\n              sandbox.degradeAttrs\n            );\n          sandbox.hrefFlag = true;\n          // href后退\n        } else if (sandbox.hrefFlag) {\n          if (sandbox.degrade) {\n            // 走全套流程，但是事件恢复不需要\n            const { iframe } = initRenderIframeAndContainer(sandbox.id, sandbox.el, sandbox.degradeAttrs);\n            patchEventTimeStamp(iframe.contentWindow, sandbox.iframe.contentWindow);\n            iframe.contentWindow.onunload = () => {\n              sandbox.unmount();\n            };\n            iframe.contentDocument.appendChild(iframeBody.firstElementChild);\n            sandbox.document = iframe.contentDocument;\n          } else renderElementToContainer(sandbox.shadowRoot.host, sandbox.el);\n          sandbox.hrefFlag = false;\n        }\n      });\n  });\n}\n", "import <PERSON><PERSON><PERSON> from \"./sandbox\";\nimport { ScriptObject } from \"./template\";\nimport { renderElementToContainer } from \"./shadow\";\nimport { syncUrlToWindow } from \"./sync\";\nimport {\n  fixElementCtrSrcOrHref,\n  isConstructable,\n  anchorElementGenerator,\n  isMatchSyncQueryById,\n  isFunction,\n  warn,\n  error,\n  execHooks,\n  getCurUrl,\n  getAbsolutePath,\n  setAttrsToElement,\n  setTagToScript,\n  getTagFromScript,\n} from \"./utils\";\nimport {\n  documentProxyProperties,\n  rawAddEventListener,\n  rawRemoveEventListener,\n  rawDocumentQuerySelector,\n  mainDocumentAddEventListenerEvents,\n  mainAndAppAddEventListenerEvents,\n  appDocumentAddEventListenerEvents,\n  appDocumentOnEvents,\n  appWindowAddEventListenerEvents,\n  appWindowOnEvent,\n  windowProxyProperties,\n  windowRegWhiteList,\n  rawWindowAddEventListener,\n  rawWindowRemoveEventListener,\n} from \"./common\";\nimport type { appAddEventListenerOptions } from \"./common\";\nimport { getJsLoader } from \"./plugin\";\nimport { WUJIE_TIPS_SCRIPT_ERROR_REQUESTED, WUJIE_DATA_FLAG } from \"./constant\";\nimport { ScriptObjectLoader } from \"./index\";\n\ndeclare global {\n  interface Window {\n    // 是否存在无界\n    __POWERED_BY_WUJIE__?: boolean;\n    // 子应用公共加载路径\n    __WUJIE_PUBLIC_PATH__: string;\n    // 原生的querySelector\n    __WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__: typeof Document.prototype.querySelector;\n\n    // iframe内原生的createElement\n    __WUJIE_RAW_DOCUMENT_CREATE_ELEMENT__: typeof Document.prototype.createElement;\n\n    // iframe内原生的createTextNode\n    __WUJIE_RAW_DOCUMENT_CREATE_TEXT_NODE__: typeof Document.prototype.createTextNode;\n\n    // iframe内原生的head\n    __WUJIE_RAW_DOCUMENT_HEAD__: typeof Document.prototype.head;\n\n    // 原生的querySelector\n    __WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__: typeof Document.prototype.querySelectorAll;\n    // 原生的window对象\n    __WUJIE_RAW_WINDOW__: Window;\n    // 子应用沙盒实例\n    __WUJIE: WuJie;\n    // 记录注册在主应用中的事件\n    __WUJIE_EVENTLISTENER__: Set<{ listener: EventListenerOrEventListenerObject; type: string; options: any }>;\n    // 子应用mount函数\n    __WUJIE_MOUNT: () => void;\n    // 子应用unmount函数\n    __WUJIE_UNMOUNT: () => void | Promise<void>;\n    // document type\n    Document: typeof Document;\n    // img type\n    HTMLImageElement: typeof HTMLImageElement;\n    // node type\n    Node: typeof Node;\n    // element type\n    Element: typeof Element;\n    // htmlElement typeof\n    HTMLElement: typeof HTMLElement;\n    // anchor type\n    HTMLAnchorElement: typeof HTMLAnchorElement;\n    // source type\n    HTMLSourceElement: typeof HTMLSourceElement;\n    // link type\n    HTMLLinkElement: typeof HTMLLinkElement;\n    // script type\n    HTMLScriptElement: typeof HTMLScriptElement;\n    // media type\n    HTMLMediaElement: typeof HTMLMediaElement;\n    EventTarget: typeof EventTarget;\n    Event: typeof Event;\n    ShadowRoot: typeof ShadowRoot;\n    // 注入对象\n    $wujie: { [key: string]: any };\n  }\n  interface HTMLHeadElement {\n    _cacheListeners: Map<string, EventListenerOrEventListenerObject[]>;\n  }\n  interface HTMLBodyElement {\n    _cacheListeners: Map<string, EventListenerOrEventListenerObject[]>;\n  }\n  interface Document {\n    createTreeWalker(\n      root: Node,\n      whatToShow?: number,\n      filter?: NodeFilter | null,\n      entityReferenceExpansion?: boolean\n    ): TreeWalker;\n  }\n}\n\n/**\n * 修改window对象的事件监听，只有路由事件采用iframe的事件\n */\nfunction patchIframeEvents(iframeWindow: Window) {\n  iframeWindow.__WUJIE_EVENTLISTENER__ = iframeWindow.__WUJIE_EVENTLISTENER__ || new Set();\n  iframeWindow.addEventListener = function addEventListener<K extends keyof WindowEventMap>(\n    type: K,\n    listener: (this: Window, ev: WindowEventMap[K]) => any,\n    options?: boolean | appAddEventListenerOptions\n  ) {\n    // 运行插件钩子函数\n    execHooks(iframeWindow.__WUJIE.plugins, \"windowAddEventListenerHook\", iframeWindow, type, listener, options);\n    // 相同参数多次调用 addEventListener 不会导致重复注册，所以用set。\n    iframeWindow.__WUJIE_EVENTLISTENER__.add({ type, listener, options });\n    if (appWindowAddEventListenerEvents.includes(type) || (typeof options === \"object\" && options.targetWindow)) {\n      const targetWindow = typeof options === \"object\" && options.targetWindow ? options?.targetWindow : iframeWindow;\n      return rawWindowAddEventListener.call(targetWindow, type, listener, options);\n    }\n    // 在子应用嵌套场景使用window.window获取真实window\n    rawWindowAddEventListener.call(window.__WUJIE_RAW_WINDOW__ || window, type, listener, options);\n  };\n\n  iframeWindow.removeEventListener = function removeEventListener<K extends keyof WindowEventMap>(\n    type: K,\n    listener: (this: Window, ev: WindowEventMap[K]) => any,\n    options?: boolean | appAddEventListenerOptions\n  ) {\n    // 运行插件钩子函数\n    execHooks(iframeWindow.__WUJIE.plugins, \"windowRemoveEventListenerHook\", iframeWindow, type, listener, options);\n    iframeWindow.__WUJIE_EVENTLISTENER__.forEach((o) => {\n      // 这里严格一点，确保子应用销毁的时候都能销毁\n      if (o.listener === listener && o.type === type && options == o.options) {\n        iframeWindow.__WUJIE_EVENTLISTENER__.delete(o);\n      }\n    });\n    if (appWindowAddEventListenerEvents.includes(type) || (typeof options === \"object\" && options.targetWindow)) {\n      const targetWindow = typeof options === \"object\" && options.targetWindow ? options?.targetWindow : iframeWindow;\n      return rawWindowRemoveEventListener.call(targetWindow, type, listener, options);\n    }\n    rawWindowRemoveEventListener.call(window.__WUJIE_RAW_WINDOW__ || window, type, listener, options);\n  };\n}\n\nfunction patchIframeVariable(iframeWindow: Window, wujie: WuJie, appHostPath: string): void {\n  iframeWindow.__WUJIE = wujie;\n  iframeWindow.__WUJIE_PUBLIC_PATH__ = appHostPath + \"/\";\n  iframeWindow.$wujie = wujie.provide;\n  iframeWindow.__WUJIE_RAW_WINDOW__ = iframeWindow;\n}\n\n/**\n * 对iframe的history的pushState和replaceState进行修改\n * 将从location劫持后的数据修改回来，防止跨域错误\n * 同步路由到主应用\n * @param iframeWindow\n * @param appHostPath 子应用的 host path\n * @param mainHostPath 主应用的 host path\n */\nfunction patchIframeHistory(iframeWindow: Window, appHostPath: string, mainHostPath: string): void {\n  const history = iframeWindow.history;\n  const rawHistoryPushState = history.pushState;\n  const rawHistoryReplaceState = history.replaceState;\n  history.pushState = function (data: any, title: string, url?: string): void {\n    const baseUrl =\n      mainHostPath + iframeWindow.location.pathname + iframeWindow.location.search + iframeWindow.location.hash;\n    const mainUrl = getAbsolutePath(url?.replace(appHostPath, \"\"), baseUrl);\n    const ignoreFlag = url === undefined;\n\n    rawHistoryPushState.call(history, data, title, ignoreFlag ? undefined : mainUrl);\n    if (ignoreFlag) return;\n    updateBase(iframeWindow, appHostPath, mainHostPath);\n    syncUrlToWindow(iframeWindow);\n  };\n  history.replaceState = function (data: any, title: string, url?: string): void {\n    const baseUrl =\n      mainHostPath + iframeWindow.location.pathname + iframeWindow.location.search + iframeWindow.location.hash;\n    const mainUrl = getAbsolutePath(url?.replace(appHostPath, \"\"), baseUrl);\n    const ignoreFlag = url === undefined;\n\n    rawHistoryReplaceState.call(history, data, title, ignoreFlag ? undefined : mainUrl);\n    if (ignoreFlag) return;\n    updateBase(iframeWindow, appHostPath, mainHostPath);\n    syncUrlToWindow(iframeWindow);\n  };\n}\n\n/**\n * 动态的修改iframe的base地址\n * @param iframeWindow\n * @param appHostPath\n * @param mainHostPath\n */\nfunction updateBase(iframeWindow: Window, appHostPath: string, mainHostPath: string) {\n  const baseUrl = new URL(iframeWindow.location.href?.replace(mainHostPath, \"\"), appHostPath);\n  const baseElement = rawDocumentQuerySelector.call(iframeWindow.document, \"base\");\n  if (baseElement) baseElement.setAttribute(\"href\", appHostPath + baseUrl.pathname);\n}\n\n/**\n * patch iframe window effect\n * @param iframeWindow\n */\n// TODO 继续改进\nfunction patchWindowEffect(iframeWindow: Window): void {\n  // 属性处理函数\n  function processWindowProperty(key: string): boolean {\n    const value = iframeWindow[key];\n    try {\n      if (typeof value === \"function\" && !isConstructable(value)) {\n        iframeWindow[key] = window[key].bind(window);\n      } else {\n        iframeWindow[key] = window[key];\n      }\n      return true;\n    } catch (e) {\n      warn(e.message);\n      return false;\n    }\n  }\n  Object.getOwnPropertyNames(iframeWindow).forEach((key) => {\n    // 特殊处理\n    if (key === \"getSelection\") {\n      Object.defineProperty(iframeWindow, key, {\n        get: () => iframeWindow.document[key],\n      });\n      return;\n    }\n    // 单独属性\n    if (windowProxyProperties.includes(key)) {\n      processWindowProperty(key);\n      return;\n    }\n    // 正则匹配，可以一次处理多个\n    windowRegWhiteList.some((reg) => {\n      if (reg.test(key) && key in iframeWindow.parent) {\n        return processWindowProperty(key);\n      }\n      return false;\n    });\n  });\n  // onEvent set\n  const windowOnEvents = Object.getOwnPropertyNames(window)\n    .filter((p) => /^on/.test(p))\n    .filter((e) => !appWindowOnEvent.includes(e));\n\n  // 走主应用window\n  windowOnEvents.forEach((e) => {\n    const descriptor = Object.getOwnPropertyDescriptor(iframeWindow, e) || {\n      enumerable: true,\n      writable: true,\n    };\n    try {\n      Object.defineProperty(iframeWindow, e, {\n        enumerable: descriptor.enumerable,\n        configurable: true,\n        get: () => window[e],\n        set:\n          descriptor.writable || descriptor.set\n            ? (handler) => {\n                window[e] = typeof handler === \"function\" ? handler.bind(iframeWindow) : handler;\n              }\n            : undefined,\n      });\n    } catch (e) {\n      warn(e.message);\n    }\n  });\n  // 运行插件钩子函数\n  execHooks(iframeWindow.__WUJIE.plugins, \"windowPropertyOverride\", iframeWindow);\n}\n\n/**\n * 记录节点的监听事件\n */\nfunction recordEventListeners(iframeWindow: Window) {\n  const sandbox = iframeWindow.__WUJIE;\n  iframeWindow.Node.prototype.addEventListener = function (\n    type: string,\n    handler: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    // 添加事件缓存\n    const elementListenerList = sandbox.elementEventCacheMap.get(this);\n    if (elementListenerList) {\n      if (!elementListenerList.find((listener) => listener.type === type && listener.handler === handler)) {\n        elementListenerList.push({ type, handler, options });\n      }\n    } else sandbox.elementEventCacheMap.set(this, [{ type, handler, options }]);\n    return rawAddEventListener.call(this, type, handler, options);\n  };\n\n  iframeWindow.Node.prototype.removeEventListener = function (\n    type: string,\n    handler: EventListenerOrEventListenerObject,\n    options?: boolean | EventListenerOptions\n  ): void {\n    // 清除缓存\n    const elementListenerList = sandbox.elementEventCacheMap.get(this);\n    if (elementListenerList) {\n      const index = elementListenerList?.findIndex((ele) => ele.type === type && ele.handler === handler);\n      elementListenerList.splice(index, 1);\n    }\n    if (!elementListenerList?.length) {\n      sandbox.elementEventCacheMap.delete(this);\n    }\n    return rawRemoveEventListener.call(this, type, handler, options);\n  };\n}\n\n/**\n * 恢复节点的监听事件\n */\nexport function recoverEventListeners(rootElement: Element | ChildNode, iframeWindow: Window) {\n  const sandbox = iframeWindow.__WUJIE;\n  const elementEventCacheMap: WeakMap<\n    Node,\n    Array<{ type: string; handler: EventListenerOrEventListenerObject; options: any }>\n  > = new WeakMap();\n  const ElementIterator = document.createTreeWalker(rootElement, NodeFilter.SHOW_ELEMENT, null, false);\n  let nextElement = ElementIterator.currentNode;\n  while (nextElement) {\n    const elementListenerList = sandbox.elementEventCacheMap.get(nextElement);\n    if (elementListenerList?.length) {\n      elementEventCacheMap.set(nextElement, elementListenerList);\n      elementListenerList.forEach((listener) => {\n        nextElement.addEventListener(listener.type, listener.handler, listener.options);\n      });\n    }\n    nextElement = ElementIterator.nextNode() as HTMLElement;\n  }\n  sandbox.elementEventCacheMap = elementEventCacheMap;\n}\n\n/**\n * 恢复根节点的监听事件\n */\nexport function recoverDocumentListeners(\n  oldRootElement: Element | ChildNode,\n  newRootElement: Element | ChildNode,\n  iframeWindow: Window\n) {\n  const sandbox = iframeWindow.__WUJIE;\n  const elementEventCacheMap: WeakMap<\n    Node,\n    Array<{ type: string; handler: EventListenerOrEventListenerObject; options: any }>\n  > = new WeakMap();\n  const elementListenerList = sandbox.elementEventCacheMap.get(oldRootElement);\n  if (elementListenerList?.length) {\n    elementEventCacheMap.set(newRootElement, elementListenerList);\n    elementListenerList.forEach((listener) => {\n      newRootElement.addEventListener(listener.type, listener.handler, listener.options);\n    });\n  }\n  sandbox.elementEventCacheMap = elementEventCacheMap;\n}\n\n/**\n * 修复vue绑定事件e.timeStamp < attachedTimestamp 的情况\n */\nexport function patchEventTimeStamp(targetWindow: Window, iframeWindow: Window) {\n  Object.defineProperty(targetWindow.Event.prototype, \"timeStamp\", {\n    get: () => {\n      return iframeWindow.document.createEvent(\"Event\").timeStamp;\n    },\n  });\n}\n\n/**\n * patch document effect\n * @param iframeWindow\n */\n// TODO 继续改进\nfunction patchDocumentEffect(iframeWindow: Window): void {\n  const sandbox = iframeWindow.__WUJIE;\n\n  /**\n   * 处理 addEventListener和removeEventListener\n   * 由于这个劫持导致 handler 的this发生改变，所以需要handler.bind(document)\n   * 但是这样会导致removeEventListener无法正常工作，因为handler => handler.bind(document)\n   * 这个地方保存callback = handler.bind(document) 方便removeEventListener\n   */\n  const handlerCallbackMap: WeakMap<EventListenerOrEventListenerObject, EventListenerOrEventListenerObject> =\n    new WeakMap();\n  const handlerTypeMap: WeakMap<EventListenerOrEventListenerObject, Array<string>> = new WeakMap();\n  iframeWindow.Document.prototype.addEventListener = function (\n    type: string,\n    handler: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    if (!handler) return;\n    let callback = handlerCallbackMap.get(handler);\n    const typeList = handlerTypeMap.get(handler);\n    // 设置 handlerCallbackMap\n    if (!callback) {\n      callback = typeof handler === \"function\" ? handler.bind(this) : handler;\n      handlerCallbackMap.set(handler, callback);\n    }\n    // 设置 handlerTypeMap\n    if (typeList) {\n      if (!typeList.includes(type)) typeList.push(type);\n    } else {\n      handlerTypeMap.set(handler, [type]);\n    }\n\n    // 运行插件钩子函数\n    execHooks(iframeWindow.__WUJIE.plugins, \"documentAddEventListenerHook\", iframeWindow, type, callback, options);\n    if (appDocumentAddEventListenerEvents.includes(type)) {\n      return rawAddEventListener.call(this, type, callback, options);\n    }\n    // 降级统一走 sandbox.document\n    if (sandbox.degrade) return sandbox.document.addEventListener(type, callback, options);\n    if (mainDocumentAddEventListenerEvents.includes(type))\n      return window.document.addEventListener(type, callback, options);\n    if (mainAndAppAddEventListenerEvents.includes(type)) {\n      window.document.addEventListener(type, callback, options);\n      sandbox.shadowRoot.addEventListener(type, callback, options);\n      return;\n    }\n    sandbox.shadowRoot.addEventListener(type, callback, options);\n  };\n  iframeWindow.Document.prototype.removeEventListener = function (\n    type: string,\n    handler: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions\n  ): void {\n    const callback: EventListenerOrEventListenerObject = handlerCallbackMap.get(handler);\n    const typeList = handlerTypeMap.get(handler);\n    if (callback) {\n      if (typeList?.includes(type)) {\n        typeList.splice(typeList.indexOf(type), 1);\n        if (!typeList.length) {\n          handlerCallbackMap.delete(handler);\n          handlerTypeMap.delete(handler);\n        }\n      }\n\n      // 运行插件钩子函数\n      execHooks(iframeWindow.__WUJIE.plugins, \"documentRemoveEventListenerHook\", iframeWindow, type, callback, options);\n      if (appDocumentAddEventListenerEvents.includes(type)) {\n        return rawRemoveEventListener.call(this, type, callback, options);\n      }\n      if (sandbox.degrade) return sandbox.document.removeEventListener(type, callback, options);\n      if (mainDocumentAddEventListenerEvents.includes(type)) {\n        return window.document.removeEventListener(type, callback, options);\n      }\n      if (mainAndAppAddEventListenerEvents.includes(type)) {\n        window.document.removeEventListener(type, callback, options);\n        sandbox.shadowRoot.removeEventListener(type, callback, options);\n        return;\n      }\n      sandbox.shadowRoot.removeEventListener(type, callback, options);\n    }\n  };\n  // 处理onEvent\n  const elementOnEvents = Object.keys(iframeWindow.HTMLElement.prototype).filter((ele) => /^on/.test(ele));\n  const documentOnEvent = Object.keys(iframeWindow.Document.prototype)\n    .filter((ele) => /^on/.test(ele))\n    .filter((ele) => !appDocumentOnEvents.includes(ele));\n  elementOnEvents\n    .filter((e) => documentOnEvent.includes(e))\n    .forEach((e) => {\n      const descriptor = Object.getOwnPropertyDescriptor(iframeWindow.Document.prototype, e) || {\n        enumerable: true,\n        writable: true,\n      };\n      try {\n        Object.defineProperty(iframeWindow.Document.prototype, e, {\n          enumerable: descriptor.enumerable,\n          configurable: true,\n          get: () => (sandbox.degrade ? sandbox.document[e] : sandbox.shadowRoot.firstElementChild[e]),\n          set:\n            descriptor.writable || descriptor.set\n              ? (handler) => {\n                  const val = typeof handler === \"function\" ? handler.bind(iframeWindow.document) : handler;\n                  sandbox.degrade ? (sandbox.document[e] = val) : (sandbox.shadowRoot.firstElementChild[e] = val);\n                }\n              : undefined,\n        });\n      } catch (e) {\n        warn(e.message);\n      }\n    });\n  // 处理属性get\n  const {\n    ownerProperties,\n    modifyProperties,\n    shadowProperties,\n    shadowMethods,\n    documentProperties,\n    documentMethods,\n    documentEvents,\n  } = documentProxyProperties;\n  modifyProperties.concat(shadowProperties, shadowMethods, documentProperties, documentMethods).forEach((propKey) => {\n    const descriptor = Object.getOwnPropertyDescriptor(iframeWindow.Document.prototype, propKey) || {\n      enumerable: true,\n      writable: true,\n    };\n    try {\n      Object.defineProperty(iframeWindow.Document.prototype, propKey, {\n        enumerable: descriptor.enumerable,\n        configurable: true,\n        get: () => sandbox.proxyDocument[propKey],\n        set: undefined,\n      });\n    } catch (e) {\n      warn(e.message);\n    }\n  });\n  // 处理document专属事件\n  // TODO 内存泄露\n  documentEvents.forEach((propKey) => {\n    const descriptor = Object.getOwnPropertyDescriptor(iframeWindow.Document.prototype, propKey) || {\n      enumerable: true,\n      writable: true,\n    };\n    //get里获取属性值，set里直接对iframeWindow.document[propKey]赋值，下一个handler绑在iframeWindow.document[propKey]之前需要对之前的handler解绑\n    try {\n      Object.defineProperty(iframeWindow.Document.prototype, propKey, {\n        enumerable: descriptor.enumerable,\n        configurable: true,\n        get: () => (sandbox.degrade ? sandbox : window).document[propKey],\n        // 在设置新的handler之前先移除之前的回调\n        set:\n          descriptor.writable || descriptor.set\n            ? (handler) => {\n                // (sandbox.degrade ? sandbox : window).document[propKey] =\n                //   typeof handler === \"function\" ? handler.bind(iframeWindow.document) : handler;\n                (sandbox.degrade ? sandbox : window).document.removeEventListener(\n                  propKey,\n                  handlerCallbackMap.get(handler)\n                );\n                // 绑定新回调函数\n                (sandbox.degrade ? sandbox : window).document.addEventListener(\n                  propKey,\n                  typeof handler === \"function\" ? handler.bind(iframeWindow.document) : handler\n                );\n                // 更新回调函数的映射\n                handlerCallbackMap.set(handler, handler.bind(iframeWindow.document));\n              }\n            : undefined,\n      });\n    } catch (e) {\n      warn(e.message);\n    }\n  });\n  // process owner property\n  ownerProperties.forEach((propKey) => {\n    Object.defineProperty(iframeWindow.document, propKey, {\n      enumerable: true,\n      configurable: true,\n      get: () => sandbox.proxyDocument[propKey],\n      set: undefined,\n    });\n  });\n  // 运行插件钩子函数\n  execHooks(iframeWindow.__WUJIE.plugins, \"documentPropertyOverride\", iframeWindow);\n}\n\n/**\n * patch Node effect\n * 1、处理 getRootNode\n * 2、处理 appendChild、insertBefore，当插入的节点为 svg 时，createElement 的 patch 会被去除，需要重新 patch\n * @param iframeWindow\n */\nfunction patchNodeEffect(iframeWindow: Window): void {\n  const rawGetRootNode = iframeWindow.Node.prototype.getRootNode;\n  const rawAppendChild = iframeWindow.Node.prototype.appendChild;\n  const rawInsertRule = iframeWindow.Node.prototype.insertBefore;\n  const rawRemoveChild = iframeWindow.Node.prototype.removeChild;\n  iframeWindow.Node.prototype.getRootNode = function (options?: GetRootNodeOptions): Node {\n    const rootNode = rawGetRootNode.call(this, options);\n    if (rootNode === iframeWindow.__WUJIE.shadowRoot) return iframeWindow.document;\n    else return rootNode;\n  };\n  iframeWindow.Node.prototype.appendChild = function <T extends Node>(node: T): T {\n    const res = rawAppendChild.call(this, node);\n    patchElementEffect(node, iframeWindow);\n    return res;\n  };\n  iframeWindow.Node.prototype.insertBefore = function <T extends Node>(node: T, child: Node | null): T {\n    const res = rawInsertRule.call(this, node, child);\n    patchElementEffect(node, iframeWindow);\n    return res;\n  };\n  iframeWindow.Node.prototype.removeChild = function <T extends Node>(node: T): T {\n    let res;\n    try {\n      res = rawRemoveChild.call(this, node);\n    } catch (e) {\n      console.warn(\n        `Failed to removeChild: ${node.nodeName.toLowerCase()} is not a child of ${this.nodeName.toLowerCase()}, try again with parentNode attribute. `\n      );\n      if (node.isConnected && isFunction(node.parentNode?.removeChild)) {\n        node.parentNode.removeChild(node);\n      }\n    }\n    patchElementEffect(node, iframeWindow);\n    return res;\n  };\n}\n\n/**\n * 修复资源元素的相对路径问题\n * @param iframeWindow\n */\nfunction patchRelativeUrlEffect(iframeWindow: Window): void {\n  fixElementCtrSrcOrHref(iframeWindow, iframeWindow.HTMLImageElement, \"src\");\n  fixElementCtrSrcOrHref(iframeWindow, iframeWindow.HTMLAnchorElement, \"href\");\n  fixElementCtrSrcOrHref(iframeWindow, iframeWindow.HTMLSourceElement, \"src\");\n  fixElementCtrSrcOrHref(iframeWindow, iframeWindow.HTMLLinkElement, \"href\");\n  fixElementCtrSrcOrHref(iframeWindow, iframeWindow.HTMLScriptElement, \"src\");\n  fixElementCtrSrcOrHref(iframeWindow, iframeWindow.HTMLMediaElement, \"src\");\n}\n\n/**\n * 初始化base标签\n */\nexport function initBase(iframeWindow: Window, url: string): void {\n  const iframeDocument = iframeWindow.document;\n  const baseElement = iframeDocument.createElement(\"base\");\n  const iframeUrlElement = anchorElementGenerator(iframeWindow.location.href);\n  const appUrlElement = anchorElementGenerator(url);\n  baseElement.setAttribute(\"href\", appUrlElement.protocol + \"//\" + appUrlElement.host + iframeUrlElement.pathname);\n  iframeDocument.head.appendChild(baseElement);\n}\n\n/**\n * 初始化iframe的dom结构\n * @param iframeWindow\n * @param wujie\n * @param mainHostPath\n * @param appHostPath\n */\nfunction initIframeDom(iframeWindow: Window, wujie: WuJie, mainHostPath: string, appHostPath: string): void {\n  const iframeDocument = iframeWindow.document;\n  const newDoc = window.document.implementation.createHTMLDocument(\"\");\n  const newDocumentElement = iframeDocument.importNode(newDoc.documentElement, true);\n  iframeDocument.documentElement\n    ? iframeDocument.replaceChild(newDocumentElement, iframeDocument.documentElement)\n    : iframeDocument.appendChild(newDocumentElement);\n  iframeWindow.__WUJIE_RAW_DOCUMENT_HEAD__ = iframeDocument.head;\n  iframeWindow.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__ = iframeWindow.Document.prototype.querySelector;\n  iframeWindow.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__ = iframeWindow.Document.prototype.querySelectorAll;\n  iframeWindow.__WUJIE_RAW_DOCUMENT_CREATE_ELEMENT__ = iframeWindow.Document.prototype.createElement;\n  iframeWindow.__WUJIE_RAW_DOCUMENT_CREATE_TEXT_NODE__ = iframeWindow.Document.prototype.createTextNode;\n  initBase(iframeWindow, wujie.url);\n  patchIframeHistory(iframeWindow, appHostPath, mainHostPath);\n  patchIframeEvents(iframeWindow);\n  if (wujie.degrade) recordEventListeners(iframeWindow);\n  syncIframeUrlToWindow(iframeWindow);\n\n  patchWindowEffect(iframeWindow);\n  patchDocumentEffect(iframeWindow);\n  patchNodeEffect(iframeWindow);\n  patchRelativeUrlEffect(iframeWindow);\n}\n\n/**\n * 防止运行主应用的js代码，给子应用带来很多副作用\n */\n// TODO 更加准确抓取停止时机\nfunction stopIframeLoading(iframe: HTMLIFrameElement, useObjectURL: { mainHostPath: string } | false) {\n  const iframeWindow = iframe.contentWindow;\n  const oldDoc = iframeWindow.document;\n  return new Promise<void>((resolve) => {\n    function loop() {\n      setTimeout(() => {\n        let newDoc: Document;\n        try {\n          newDoc = iframeWindow.document;\n        } catch (err) {\n          newDoc = null;\n        }\n        // wait for document ready\n        if (!newDoc || newDoc == oldDoc) {\n          loop();\n          return;\n        }\n\n        // document ready, if is using ObjectURL, remove its \"blob:\" prefix\n        if (useObjectURL) {\n          const href = iframeWindow.location.href;\n          newDoc.open();\n          newDoc.close();\n\n          const deadline = Date.now() + 1e3;\n          const loop2 = function () {\n            if (Date.now() > deadline) {\n              // 一秒后 URL 没有变化\n              // 可能浏览器已经不支持使用这种奇技淫巧了，标记不再支持，并且回退到旧的方式加载\n              disableSandboxEmptyPageURL();\n              iframe.src = useObjectURL.mainHostPath;\n              stopIframeLoading(iframe, false).then(resolve);\n              return;\n            }\n\n            if (iframeWindow.location.href === href) setTimeout(loop2, 1);\n            else resolve();\n          };\n          loop2();\n\n          return;\n        }\n\n        // document ready\n        iframeWindow.stop ? iframeWindow.stop() : newDoc.execCommand(\"Stop\");\n        resolve();\n      }, 1);\n    }\n    loop();\n  });\n}\n\nexport function patchElementEffect(\n  element: (HTMLElement | Node | ShadowRoot) & { _hasPatch?: boolean },\n  iframeWindow: Window\n): void {\n  const proxyLocation = iframeWindow.__WUJIE.proxyLocation as Location;\n  if (element._hasPatch) return;\n  try {\n    Object.defineProperties(element, {\n      baseURI: {\n        configurable: true,\n        get: () => proxyLocation.protocol + \"//\" + proxyLocation.host + proxyLocation.pathname,\n        set: undefined,\n      },\n      ownerDocument: {\n        configurable: true,\n        get: () => iframeWindow.document,\n      },\n      _hasPatch: { get: () => true },\n    });\n  } catch (error) {\n    console.warn(error);\n  }\n  execHooks(iframeWindow.__WUJIE.plugins, \"patchElementHook\", element, iframeWindow);\n}\n\n/**\n * 子应用前进后退，同步路由到主应用\n * @param iframeWindow\n */\nexport function syncIframeUrlToWindow(iframeWindow: Window): void {\n  iframeWindow.addEventListener(\"hashchange\", () => syncUrlToWindow(iframeWindow));\n  iframeWindow.addEventListener(\"popstate\", () => {\n    syncUrlToWindow(iframeWindow);\n  });\n}\n\n/**\n * iframe插入脚本\n * @param scriptResult script请求结果\n * @param iframeWindow\n * @param rawElement 原始的脚本\n */\nexport function insertScriptToIframe(\n  scriptResult: ScriptObject | ScriptObjectLoader,\n  iframeWindow: Window,\n  rawElement?: HTMLScriptElement\n) {\n  const { src, module, content, crossorigin, crossoriginType, async, attrs, callback, onload } =\n    scriptResult as ScriptObjectLoader;\n  const scriptElement = iframeWindow.document.createElement(\"script\");\n  const nextScriptElement = iframeWindow.document.createElement(\"script\");\n  const { replace, plugins, proxyLocation } = iframeWindow.__WUJIE;\n  const jsLoader = getJsLoader({ plugins, replace });\n  let code = jsLoader(content, src, getCurUrl(proxyLocation));\n  // 添加属性\n  attrs &&\n    Object.keys(attrs)\n      .filter((key) => !Object.keys(scriptResult).includes(key))\n      .forEach((key) => scriptElement.setAttribute(key, String(attrs[key])));\n\n  // 内联脚本\n  if (content) {\n    // patch location\n    if (!iframeWindow.__WUJIE.degrade && !module) {\n      code = `(function(window, self, global, location) {\n      ${code}\n}).bind(window.__WUJIE.proxy)(\n  window.__WUJIE.proxy,\n  window.__WUJIE.proxy,\n  window.__WUJIE.proxy,\n  window.__WUJIE.proxyLocation,\n);`;\n    }\n    const descriptor = Object.getOwnPropertyDescriptor(scriptElement, \"src\");\n    // 部分浏览器 src 不可配置 取不到descriptor表示无该属性，可写\n    if (descriptor?.configurable || !descriptor) {\n      // 解决 webpack publicPath 为 auto 无法加载资源的问题\n      try {\n        Object.defineProperty(scriptElement, \"src\", { get: () => src || \"\" });\n      } catch (error) {\n        console.warn(error);\n      }\n    }\n  } else {\n    src && scriptElement.setAttribute(\"src\", src);\n    crossorigin && scriptElement.setAttribute(\"crossorigin\", crossoriginType);\n  }\n  module && scriptElement.setAttribute(\"type\", \"module\");\n  scriptElement.textContent = code || \"\";\n  nextScriptElement.textContent =\n    \"if(window.__WUJIE.execQueue && window.__WUJIE.execQueue.length){ window.__WUJIE.execQueue.shift()()}\";\n\n  const container = rawDocumentQuerySelector.call(iframeWindow.document, \"head\");\n  const execNextScript = () => !async && container.appendChild(nextScriptElement);\n  const afterExecScript = () => {\n    onload?.();\n    execNextScript();\n  };\n\n  // 错误情况处理\n  if (/^<!DOCTYPE html/i.test(code)) {\n    error(WUJIE_TIPS_SCRIPT_ERROR_REQUESTED, scriptResult);\n    return execNextScript();\n  }\n\n  // 打标记\n  if (rawElement) {\n    setTagToScript(scriptElement, getTagFromScript(rawElement));\n  }\n  // 外联脚本执行后的处理\n  const isOutlineScript = !content && src;\n  if (isOutlineScript) {\n    scriptElement.onload = afterExecScript;\n    scriptElement.onerror = afterExecScript;\n  }\n  container.appendChild(scriptElement);\n\n  // 调用回调\n  callback?.(iframeWindow);\n  // 执行 hooks\n  execHooks(plugins, \"appendOrInsertElementHook\", scriptElement, iframeWindow, rawElement);\n  // 内联脚本执行后的处理\n  !isOutlineScript && afterExecScript();\n}\n\n/**\n * 加载iframe替换子应用\n * @param src 地址\n * @param element\n * @param degradeAttrs\n */\nexport function renderIframeReplaceApp(\n  src: string,\n  element: HTMLElement,\n  degradeAttrs: { [key: string]: any } = {}\n): void {\n  const iframe = window.document.createElement(\"iframe\");\n  const defaultStyle = \"height:100%;width:100%\";\n  setAttrsToElement(iframe, { ...degradeAttrs, src, style: [defaultStyle, degradeAttrs.style].join(\";\") });\n  renderElementToContainer(iframe, element);\n}\n\nconst [getSandboxEmptyPageURL, disableSandboxEmptyPageURL] = (() => {\n  const disabledMarkKey = \"wujie:disableSandboxEmptyPageURL\";\n  let disabled = false;\n  try {\n    disabled = localStorage.getItem(disabledMarkKey) === \"true\";\n  } catch (e) {\n    // pass\n  }\n\n  if (disabled || typeof URL === \"undefined\" || typeof URL.createObjectURL !== \"function\")\n    return [() => \"\", () => void 0] as const;\n\n  let prevURL = \"\";\n  const getSandboxEmptyPageURL = () => {\n    if (disabled) return \"\";\n    if (prevURL) return prevURL;\n\n    const blob = new Blob([\"<!DOCTYPE html><html><head></head><body></body></html>\"], { type: \"text/html\" });\n    prevURL = URL.createObjectURL(blob);\n    return prevURL;\n  };\n\n  const disableSandboxEmptyPageURL = () => {\n    disabled = true;\n    try {\n      // TODO: 看能不能做上报，收集一下浏览器版本的情况\n      localStorage.setItem(disabledMarkKey, \"true\");\n    } catch (e) {}\n  };\n\n  return [getSandboxEmptyPageURL, disableSandboxEmptyPageURL];\n})();\n\n/**\n * js沙箱\n * 创建和主应用同源的iframe，路径携带了子路由的路由信息\n * iframe必须禁止加载html，防止进入主应用的路由逻辑\n */\nexport function iframeGenerator(\n  sandbox: WuJie,\n  attrs: { [key: string]: any },\n  mainHostPath: string,\n  appHostPath: string,\n  appRoutePath: string\n): HTMLIFrameElement {\n  let src = attrs && attrs.src;\n  let useObjectURL = false;\n  if (!src) {\n    src = getSandboxEmptyPageURL();\n    useObjectURL = !!src;\n    if (!src) src = mainHostPath; // fallback to mainHostPath\n  }\n\n  const iframe = window.document.createElement(\"iframe\");\n  const attrsMerge = {\n    style: \"display: none\",\n    ...attrs,\n    src,\n    name: sandbox.id,\n    [WUJIE_DATA_FLAG]: \"\",\n  };\n  setAttrsToElement(iframe, attrsMerge);\n  window.document.body.appendChild(iframe);\n\n  const iframeWindow = iframe.contentWindow;\n  // 变量需要提前注入，在入口函数通过变量防止死循环\n  patchIframeVariable(iframeWindow, sandbox, appHostPath);\n  sandbox.iframeReady = stopIframeLoading(iframe, useObjectURL && { mainHostPath }).then(() => {\n    if (!iframeWindow.__WUJIE) {\n      patchIframeVariable(iframeWindow, sandbox, appHostPath);\n    }\n    initIframeDom(iframeWindow, sandbox, mainHostPath, appHostPath);\n    /**\n     * 如果有同步优先同步，非同步从url读取\n     */\n    if (!isMatchSyncQueryById(iframeWindow.__WUJIE.id)) {\n      iframeWindow.history.replaceState(null, \"\", mainHostPath + appRoutePath);\n    }\n  });\n  return iframe;\n}\n", "import { patchElementEffect, renderIframeReplaceApp } from \"./iframe\";\nimport { renderElementToContainer } from \"./shadow\";\nimport { pushUrlToWindow } from \"./sync\";\nimport { documentProxyProperties, rawDocumentQuerySelector } from \"./common\";\nimport { WUJIE_TIPS_RELOAD_DISABLED, WUJIE_TIPS_GET_ELEMENT_BY_ID } from \"./constant\";\nimport {\n  getTargetValue,\n  anchorElementGenerator,\n  getDegradeIframe,\n  isCallable,\n  checkProxyFunction,\n  warn,\n  stopMainAppRun,\n} from \"./utils\";\n\n/**\n * location href 的set劫持操作\n */\nfunction locationHrefSet(iframe: HTMLIFrameElement, value: string, appHostPath: string): boolean {\n  const { shadowRoot, id, degrade, document, degradeAttrs } = iframe.contentWindow.__WUJIE;\n  let url = value;\n  if (!/^http/.test(url)) {\n    let hrefElement = anchorElementGenerator(url);\n    url = appHostPath + hrefElement.pathname + hrefElement.search + hrefElement.hash;\n    hrefElement = null;\n  }\n  iframe.contentWindow.__WUJIE.hrefFlag = true;\n  if (degrade) {\n    const iframeBody = rawDocumentQuerySelector.call(iframe.contentDocument, \"body\");\n    renderElementToContainer(document.documentElement, iframeBody);\n    renderIframeReplaceApp(window.decodeURIComponent(url), getDegradeIframe(id).parentElement, degradeAttrs);\n  } else renderIframeReplaceApp(url, shadowRoot.host.parentElement, degradeAttrs);\n  pushUrlToWindow(id, url);\n  return true;\n}\n\n/**\n * 非降级情况下window、document、location代理\n */\nexport function proxyGenerator(\n  iframe: HTMLIFrameElement,\n  urlElement: HTMLAnchorElement,\n  mainHostPath: string,\n  appHostPath: string\n): {\n  proxyWindow: Window;\n  proxyDocument: Object;\n  proxyLocation: Object;\n} {\n  const proxyWindow = new Proxy(iframe.contentWindow, {\n    get: (target: Window, p: PropertyKey): any => {\n      // location进行劫持\n      if (p === \"location\") {\n        return target.__WUJIE.proxyLocation;\n      }\n      // 判断自身\n      if (p === \"self\" || (p === \"window\" && Object.getOwnPropertyDescriptor(window, \"window\").get)) {\n        return target.__WUJIE.proxy;\n      }\n      // 不要绑定this\n      if (p === \"__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__\" || p === \"__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__\") {\n        return target[p];\n      }\n      // https://262.ecma-international.org/8.0/#sec-proxy-object-internal-methods-and-internal-slots-get-p-receiver\n      const descriptor = Object.getOwnPropertyDescriptor(target, p);\n      if (descriptor?.configurable === false && descriptor?.writable === false) {\n        return target[p];\n      }\n      // 修正this指针指向\n      return getTargetValue(target, p);\n    },\n\n    set: (target: Window, p: PropertyKey, value: any) => {\n      checkProxyFunction(target, value);\n      target[p] = value;\n      return true;\n    },\n\n    has: (target: Window, p: PropertyKey) => p in target,\n  });\n\n  // proxy document\n  const proxyDocument = new Proxy(\n    {},\n    {\n      get: function (_fakeDocument, propKey) {\n        const document = window.document;\n        const { shadowRoot, proxyLocation } = iframe.contentWindow.__WUJIE;\n        // iframe初始化完成后，webcomponent还未挂在上去，此时运行了主应用代码，必须中止\n        if (!shadowRoot) stopMainAppRun();\n        const rawCreateElement = iframe.contentWindow.__WUJIE_RAW_DOCUMENT_CREATE_ELEMENT__;\n        const rawCreateTextNode = iframe.contentWindow.__WUJIE_RAW_DOCUMENT_CREATE_TEXT_NODE__;\n        // need fix\n        if (propKey === \"createElement\" || propKey === \"createTextNode\") {\n          return new Proxy(document[propKey], {\n            apply(_createElement, _ctx, args) {\n              const rawCreateMethod = propKey === \"createElement\" ? rawCreateElement : rawCreateTextNode;\n              const element = rawCreateMethod.apply(iframe.contentDocument, args);\n              patchElementEffect(element, iframe.contentWindow);\n              return element;\n            },\n          });\n        }\n        if (propKey === \"documentURI\" || propKey === \"URL\") {\n          return (proxyLocation as Location).href;\n        }\n\n        // from shadowRoot\n        if (\n          propKey === \"getElementsByTagName\" ||\n          propKey === \"getElementsByClassName\" ||\n          propKey === \"getElementsByName\"\n        ) {\n          return new Proxy(shadowRoot.querySelectorAll, {\n            apply(querySelectorAll, _ctx, args) {\n              let arg = args[0];\n              if (_ctx !== iframe.contentDocument) {\n                return _ctx[propKey].apply(_ctx, args);\n              }\n\n              if (propKey === \"getElementsByTagName\" && arg === \"script\") {\n                return iframe.contentDocument.scripts;\n              }\n              if (propKey === \"getElementsByClassName\") arg = \".\" + arg;\n              if (propKey === \"getElementsByName\") arg = `[name=\"${arg}\"]`;\n\n              // FIXME: This string must be a valid CSS selector string; if it's not, a SyntaxError exception is thrown;\n              // so we should ensure that the program can execute normally in case of exceptions.\n              // reference: https://developer.mozilla.org/en-US/docs/Web/API/Document/querySelectorAll\n\n              let res: NodeList[] | [];\n              try {\n                res = querySelectorAll.call(shadowRoot, arg);\n              } catch (error) {\n                res = [];\n              }\n\n              return res;\n            },\n          });\n        }\n        if (propKey === \"getElementById\") {\n          return new Proxy(shadowRoot.querySelector, {\n            // case document.querySelector.call\n            apply(target, ctx, args) {\n              if (ctx !== iframe.contentDocument) {\n                return ctx[propKey]?.apply(ctx, args);\n              }\n              try {\n                return (\n                  target.call(shadowRoot, `[id=\"${args[0]}\"]`) ||\n                  iframe.contentWindow.__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__.call(\n                    iframe.contentWindow.document,\n                    `#${args[0]}`\n                  )\n                );\n              } catch (error) {\n                warn(WUJIE_TIPS_GET_ELEMENT_BY_ID);\n                return null;\n              }\n            },\n          });\n        }\n        if (propKey === \"querySelector\" || propKey === \"querySelectorAll\") {\n          const rawPropMap = {\n            querySelector: \"__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__\",\n            querySelectorAll: \"__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__\",\n          };\n          return new Proxy(shadowRoot[propKey], {\n            apply(target, ctx, args) {\n              if (ctx !== iframe.contentDocument) {\n                return ctx[propKey]?.apply(ctx, args);\n              }\n              // 二选一，优先shadowDom，除非采用array合并，排除base，防止对router造成影响\n              return (\n                target.apply(shadowRoot, args) ||\n                (args[0] === \"base\"\n                  ? null\n                  : iframe.contentWindow[rawPropMap[propKey]].call(iframe.contentWindow.document, args[0]))\n              );\n            },\n          });\n        }\n        if (propKey === \"documentElement\" || propKey === \"scrollingElement\") return shadowRoot.firstElementChild;\n        if (propKey === \"forms\") return shadowRoot.querySelectorAll(\"form\");\n        if (propKey === \"images\") return shadowRoot.querySelectorAll(\"img\");\n        if (propKey === \"links\") return shadowRoot.querySelectorAll(\"a\");\n        const { ownerProperties, shadowProperties, shadowMethods, documentProperties, documentMethods } =\n          documentProxyProperties;\n        if (ownerProperties.concat(shadowProperties).includes(propKey.toString())) {\n          if (propKey === \"activeElement\" && shadowRoot.activeElement === null) return shadowRoot.body;\n          return shadowRoot[propKey];\n        }\n        if (shadowMethods.includes(propKey.toString())) {\n          return getTargetValue(shadowRoot, propKey) ?? getTargetValue(document, propKey);\n        }\n        // from window.document\n        if (documentProperties.includes(propKey.toString())) {\n          return document[propKey];\n        }\n        if (documentMethods.includes(propKey.toString())) {\n          return getTargetValue(document, propKey);\n        }\n      },\n    }\n  );\n\n  // proxy location\n  const proxyLocation = new Proxy(\n    {},\n    {\n      get: function (_fakeLocation, propKey) {\n        const location = iframe.contentWindow.location;\n        if (\n          propKey === \"host\" ||\n          propKey === \"hostname\" ||\n          propKey === \"protocol\" ||\n          propKey === \"port\" ||\n          propKey === \"origin\"\n        ) {\n          return urlElement[propKey];\n        }\n        if (propKey === \"href\") {\n          return location[propKey].replace(mainHostPath, appHostPath);\n        }\n        if (propKey === \"reload\") {\n          warn(WUJIE_TIPS_RELOAD_DISABLED);\n          return () => null;\n        }\n        if (propKey === \"replace\") {\n          return new Proxy(location[propKey], {\n            apply(replace, _ctx, args) {\n              return replace.call(location, args[0]?.replace(appHostPath, mainHostPath));\n            },\n          });\n        }\n        return getTargetValue(location, propKey);\n      },\n      set: function (_fakeLocation, propKey, value) {\n        // 如果是跳转链接的话重开一个iframe\n        if (propKey === \"href\") {\n          return locationHrefSet(iframe, value, appHostPath);\n        }\n        iframe.contentWindow.location[propKey] = value;\n        return true;\n      },\n      ownKeys: function () {\n        return Object.keys(iframe.contentWindow.location).filter((key) => key !== \"reload\");\n      },\n      getOwnPropertyDescriptor: function (_target, key) {\n        return { enumerable: true, configurable: true, value: this[key] };\n      },\n    }\n  );\n  return { proxyWindow, proxyDocument, proxyLocation };\n}\n\n/**\n * 降级情况下document、location代理处理\n */\nexport function localGenerator(\n  iframe: HTMLIFrameElement,\n  urlElement: HTMLAnchorElement,\n  mainHostPath: string,\n  appHostPath: string\n): {\n  proxyDocument: Object;\n  proxyLocation: Object;\n} {\n  // 代理 document\n  const proxyDocument = {};\n  const sandbox = iframe.contentWindow.__WUJIE;\n  // 特殊处理\n  Object.defineProperties(proxyDocument, {\n    createElement: {\n      get: () => {\n        return function (...args) {\n          const element = iframe.contentWindow.__WUJIE_RAW_DOCUMENT_CREATE_ELEMENT__.apply(\n            iframe.contentDocument,\n            args\n          );\n          patchElementEffect(element, iframe.contentWindow);\n          return element;\n        };\n      },\n    },\n    createTextNode: {\n      get: () => {\n        return function (...args) {\n          const element = iframe.contentWindow.__WUJIE_RAW_DOCUMENT_CREATE_TEXT_NODE__.apply(\n            iframe.contentDocument,\n            args\n          );\n          patchElementEffect(element, iframe.contentWindow);\n          return element;\n        };\n      },\n    },\n    documentURI: {\n      get: () => (sandbox.proxyLocation as Location).href,\n    },\n    URL: {\n      get: () => (sandbox.proxyLocation as Location).href,\n    },\n    getElementsByTagName: {\n      get() {\n        return function (...args) {\n          const tagName = args[0];\n          if (tagName === \"script\") {\n            return iframe.contentDocument.scripts as any;\n          }\n          return sandbox.document.getElementsByTagName(tagName) as any;\n        };\n      },\n    },\n    getElementById: {\n      get() {\n        return function (...args) {\n          const id = args[0];\n          return (sandbox.document.getElementById(id) as any) || iframe.contentDocument.getElementById(id);\n        };\n      },\n    },\n  });\n  // 普通处理\n  const {\n    modifyLocalProperties,\n    modifyProperties,\n    ownerProperties,\n    shadowProperties,\n    shadowMethods,\n    documentProperties,\n    documentMethods,\n  } = documentProxyProperties;\n  modifyProperties\n    .filter((key) => !modifyLocalProperties.includes(key))\n    .concat(ownerProperties, shadowProperties, shadowMethods, documentProperties, documentMethods)\n    .forEach((key) => {\n      Object.defineProperty(proxyDocument, key, {\n        get: () => {\n          const value = sandbox.document?.[key];\n          return isCallable(value) ? value.bind(sandbox.document) : value;\n        },\n      });\n    });\n\n  // 代理 location\n  const proxyLocation = {};\n  const location = iframe.contentWindow.location;\n  const locationKeys = Object.keys(location);\n  const constantKey = [\"host\", \"hostname\", \"port\", \"protocol\", \"port\"];\n  constantKey.forEach((key) => {\n    proxyLocation[key] = urlElement[key];\n  });\n  Object.defineProperties(proxyLocation, {\n    href: {\n      get: () => location.href.replace(mainHostPath, appHostPath),\n      set: (value) => {\n        locationHrefSet(iframe, value, appHostPath);\n      },\n    },\n    reload: {\n      get() {\n        warn(WUJIE_TIPS_RELOAD_DISABLED);\n        return () => null;\n      },\n    },\n  });\n  locationKeys\n    .filter((key) => !constantKey.concat([\"href\", \"reload\"]).includes(key))\n    .forEach((key) => {\n      Object.defineProperty(proxyLocation, key, {\n        get: () => (isCallable(location[key]) ? location[key].bind(location) : location[key]),\n      });\n    });\n  return { proxyDocument, proxyLocation };\n}\n", "import { warn, error } from \"./utils\";\nimport { WUJIE_ALL_EVENT, WUJIE_TIPS_NO_SUBJECT } from \"./constant\";\n\nexport type EventObj = { [event: string]: Array<Function> };\n\n// 全部事件存储map\nexport const appEventObjMap = window.__POWERED_BY_WUJIE__\n  ? window.__WUJIE.inject.appEventObjMap\n  : new Map<String, EventObj>();\n\n// eventBus 事件中心\nexport class EventBus {\n  private id: string;\n  private eventObj: EventObj;\n\n  constructor(id: string) {\n    this.id = id;\n    this.$clear();\n    if (!appEventObjMap.get(this.id)) {\n      appEventObjMap.set(this.id, {});\n    }\n    this.eventObj = appEventObjMap.get(this.id);\n  }\n\n  // 监听事件\n  public $on(event: string, fn: Function): EventBus {\n    const cbs = this.eventObj[event];\n    if (!cbs) {\n      this.eventObj[event] = [fn];\n      return this;\n    }\n    if (!cbs.includes(fn)) cbs.push(fn);\n    return this;\n  }\n\n  /** 任何$emit都会导致监听函数触发，第一个参数为事件名，后续的参数为$emit的参数 */\n  public $onAll(fn: (event: string, ...args: Array<any>) => any): EventBus {\n    return this.$on(WUJIE_ALL_EVENT, fn);\n  }\n\n  // 一次性监听事件\n  public $once(event: string, fn: Function): void {\n    const on = function (...args: Array<any>) {\n      this.$off(event, on);\n      fn(...args);\n    }.bind(this);\n    this.$on(event, on);\n  }\n\n  // 取消监听\n  public $off(event: string, fn: Function): EventBus {\n    const cbs = this.eventObj[event];\n    if (!event || !cbs || !cbs.length) {\n      warn(`${event} ${WUJIE_TIPS_NO_SUBJECT}`);\n      return this;\n    }\n\n    let cb;\n    let i = cbs.length;\n    while (i--) {\n      cb = cbs[i];\n      if (cb === fn) {\n        cbs.splice(i, 1);\n        break;\n      }\n    }\n    return this;\n  }\n\n  // 取消监听$onAll\n  public $offAll(fn: Function): EventBus {\n    return this.$off(WUJIE_ALL_EVENT, fn);\n  }\n\n  // 发送事件\n  public $emit(event: string, ...args: Array<any>): EventBus {\n    let cbs = [];\n    let allCbs = [];\n\n    appEventObjMap.forEach((eventObj) => {\n      if (eventObj[event]) cbs = cbs.concat(eventObj[event]);\n      if (eventObj[WUJIE_ALL_EVENT]) allCbs = allCbs.concat(eventObj[WUJIE_ALL_EVENT]);\n    });\n\n    if (!event || (cbs.length === 0 && allCbs.length === 0)) {\n      warn(`${event} ${WUJIE_TIPS_NO_SUBJECT}`);\n    } else {\n      try {\n        for (let i = 0, l = cbs.length; i < l; i++) cbs[i](...args);\n        for (let i = 0, l = allCbs.length; i < l; i++) allCbs[i](event, ...args);\n      } catch (e) {\n        error(e);\n      }\n    }\n    return this;\n  }\n\n  // 清空当前所有的监听事件\n  public $clear(): EventBus {\n    const eventObj = appEventObjMap.get(this.id) ?? {};\n    const events = Object.keys(eventObj);\n    events.forEach((event) => delete eventObj[event]);\n    return this;\n  }\n}\n", "import {\n  iframeGenerator,\n  recoverEventListeners,\n  recoverDocumentListeners,\n  insertScriptToIframe,\n  patchEventTimeStamp,\n} from \"./iframe\";\nimport { syncUrlToWindow, syncUrlToIframe, clearInactiveAppUrl } from \"./sync\";\nimport {\n  createWujieWebComponent,\n  clearChild,\n  getPatchStyleElements,\n  renderElementToContainer,\n  renderTemplateToShadowRoot,\n  renderTemplateToIframe,\n  initRenderIframeAndContainer,\n  removeLoading,\n} from \"./shadow\";\nimport { proxyGenerator, localGenerator } from \"./proxy\";\nimport { ScriptResultList } from \"./entry\";\nimport { getPlugins, getPresetLoaders } from \"./plugin\";\nimport { removeEventListener } from \"./effect\";\nimport {\n  SandboxCache,\n  idToSandboxCacheMap,\n  addSandboxCacheWithWujie,\n  deleteWujieById,\n  rawElementAppendChild,\n  rawDocumentQuerySelector,\n} from \"./common\";\nimport { EventBus, appEventObjMap, EventObj } from \"./event\";\nimport { isFunction, wujieSupport, appRouteParse, requestIdleCallback, getAbsolutePath, eventTrigger } from \"./utils\";\nimport { WUJIE_DATA_ATTACH_CSS_FLAG } from \"./constant\";\nimport { plugin, ScriptObjectLoader, loadErrorHandler } from \"./index\";\n\nexport type lifecycle = (appWindow: Window) => any;\ntype lifecycles = {\n  beforeLoad: lifecycle;\n  beforeMount: lifecycle;\n  afterMount: lifecycle;\n  beforeUnmount: lifecycle;\n  afterUnmount: lifecycle;\n  activated: lifecycle;\n  deactivated: lifecycle;\n  loadError: loadErrorHandler;\n};\n/**\n * 基于 Proxy和iframe 实现的沙箱\n */\nexport default class Wujie {\n  public id: string;\n  /** 激活时路由地址 */\n  public url: string;\n  /** 子应用保活 */\n  public alive: boolean;\n  /** window代理 */\n  public proxy: WindowProxy;\n  /** document代理 */\n  public proxyDocument: Object;\n  /** location代理 */\n  public proxyLocation: Object;\n  /** 事件中心 */\n  public bus: EventBus;\n  /** 容器 */\n  public el: HTMLElement;\n  /** js沙箱 */\n  public iframe: HTMLIFrameElement;\n  /** css沙箱 */\n  public shadowRoot: ShadowRoot;\n  /** 子应用的template */\n  public template: string;\n  /** 子应用代码替换钩子 */\n  public replace: (code: string) => string;\n  /** 子应用自定义fetch */\n  public fetch: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\n  /** 子应用的生命周期 */\n  public lifecycles: lifecycles;\n  /** 子应用的插件 */\n  public plugins: Array<plugin>;\n  /** js沙箱ready态 */\n  public iframeReady: Promise<void>;\n  /** 子应用预加载态 */\n  public preload: Promise<void>;\n  /** 降级时渲染iframe的属性 */\n  public degradeAttrs: { [key: string]: any };\n  /** 子应用js执行队列 */\n  public execQueue: Array<Function>;\n  /** 子应用执行过标志 */\n  public execFlag: boolean;\n  /** 子应用激活标志 */\n  public activeFlag: boolean;\n  /** 子应用mount标志 */\n  public mountFlag: boolean;\n  /** 路由同步标志 */\n  public sync: boolean;\n  /** 子应用短路径替换，路由同步时生效 */\n  public prefix: { [key: string]: string };\n  /** 子应用跳转标志 */\n  public hrefFlag: boolean;\n  /** 子应用采用fiber模式执行 */\n  public fiber: boolean;\n  /** 子应用降级标志 */\n  public degrade: boolean;\n  /** 子应用降级document */\n  public document: Document;\n  /** 子应用styleSheet元素 */\n  public styleSheetElements: Array<HTMLLinkElement | HTMLStyleElement>;\n  /** 子应用head元素 */\n  public head: HTMLHeadElement;\n  /** 子应用body元素 */\n  public body: HTMLBodyElement;\n  /** 子应用dom监听事件留存，当降级时用于保存元素事件 */\n  public elementEventCacheMap: WeakMap<\n    Node,\n    Array<{ type: string; handler: EventListenerOrEventListenerObject; options: any }>\n  > = new WeakMap();\n\n  /** $wujie对象，提供给子应用的接口 */\n  public provide: {\n    bus: EventBus;\n    shadowRoot?: ShadowRoot;\n    props?: { [key: string]: any };\n    location?: Object;\n  };\n\n  /** 子应用嵌套场景，父应用传递给子应用的数据 */\n  public inject: {\n    idToSandboxMap: Map<String, SandboxCache>;\n    appEventObjMap: Map<String, EventObj>;\n    mainHostPath: string;\n  };\n\n  /** 激活子应用\n   * 1、同步路由\n   * 2、动态修改iframe的fetch\n   * 3、准备shadow\n   * 4、准备子应用注入\n   */\n  public async active(options: {\n    url: string;\n    sync?: boolean;\n    prefix?: { [key: string]: string };\n    template?: string;\n    el?: string | HTMLElement;\n    props?: { [key: string]: any };\n    alive?: boolean;\n    fetch?: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\n    replace?: (code: string) => string;\n  }): Promise<void> {\n    const { sync, url, el, template, props, alive, prefix, fetch, replace } = options;\n    this.url = url;\n    this.sync = sync;\n    this.alive = alive;\n    this.hrefFlag = false;\n    this.prefix = prefix ?? this.prefix;\n    this.replace = replace ?? this.replace;\n    this.provide.props = props ?? this.provide.props;\n    this.activeFlag = true;\n    // wait iframe init\n    await this.iframeReady;\n\n    // 处理子应用自定义fetch\n    // TODO fetch检验合法性\n    const iframeWindow = this.iframe.contentWindow;\n    const iframeFetch = fetch\n      ? (input: RequestInfo, init?: RequestInit) =>\n          fetch(typeof input === \"string\" ? getAbsolutePath(input, (this.proxyLocation as Location).href) : input, init)\n      : this.fetch;\n    if (iframeFetch) {\n      iframeWindow.fetch = iframeFetch;\n      this.fetch = iframeFetch;\n    }\n\n    // 处理子应用路由同步\n    if (this.execFlag && this.alive) {\n      // 当保活模式下子应用重新激活时，只需要将子应用路径同步回主应用\n      syncUrlToWindow(iframeWindow);\n    } else {\n      // 先将url同步回iframe，然后再同步回浏览器url\n      syncUrlToIframe(iframeWindow);\n      syncUrlToWindow(iframeWindow);\n    }\n\n    // inject template\n    this.template = template ?? this.template;\n\n    /* 降级处理 */\n    if (this.degrade) {\n      const iframeBody = rawDocumentQuerySelector.call(iframeWindow.document, \"body\") as HTMLElement;\n      const { iframe, container } = initRenderIframeAndContainer(this.id, el ?? iframeBody, this.degradeAttrs);\n      this.el = container;\n      // 销毁js运行iframe容器内部dom\n      if (el) clearChild(iframeBody);\n      // 修复vue的event.timeStamp问题\n      patchEventTimeStamp(iframe.contentWindow, iframeWindow);\n      // 当销毁iframe时主动unmount子应用\n      iframe.contentWindow.onunload = () => {\n        this.unmount();\n      };\n      if (this.document) {\n        if (this.alive) {\n          iframe.contentDocument.replaceChild(this.document.documentElement, iframe.contentDocument.documentElement);\n          // 保活场景需要事件全部恢复\n          recoverEventListeners(iframe.contentDocument.documentElement, iframeWindow);\n        } else {\n          await renderTemplateToIframe(iframe.contentDocument, this.iframe.contentWindow, this.template);\n          // 非保活场景需要恢复根节点的事件，防止react16监听事件丢失\n          recoverDocumentListeners(this.document.documentElement, iframe.contentDocument.documentElement, iframeWindow);\n        }\n      } else {\n        await renderTemplateToIframe(iframe.contentDocument, this.iframe.contentWindow, this.template);\n      }\n      this.document = iframe.contentDocument;\n      return;\n    }\n\n    if (this.shadowRoot) {\n      /*\n       document.addEventListener was transfer to shadowRoot.addEventListener\n       react 16 SyntheticEvent will remember document event for avoid repeat listen\n       shadowRoot have to dispatchEvent for react 16 so can't be destroyed\n       this may lead memory leak risk\n       */\n      this.el = renderElementToContainer(this.shadowRoot.host, el);\n      if (this.alive) return;\n    } else {\n      // 预执行无容器，暂时插入iframe内部触发Web Component的connect\n      const iframeBody = rawDocumentQuerySelector.call(iframeWindow.document, \"body\") as HTMLElement;\n      this.el = renderElementToContainer(createWujieWebComponent(this.id), el ?? iframeBody);\n    }\n\n    await renderTemplateToShadowRoot(this.shadowRoot, iframeWindow, this.template);\n    this.patchCssRules();\n\n    // inject shadowRoot to app\n    this.provide.shadowRoot = this.shadowRoot;\n  }\n\n  // 未销毁，空闲时才回调\n  public requestIdleCallback(callback) {\n    return requestIdleCallback(() => {\n      // 假如已经被销毁了\n      if (!this.iframe) return;\n      callback.apply(this);\n    });\n  }\n  /** 启动子应用\n   * 1、运行js\n   * 2、处理兼容样式\n   */\n  public async start(getExternalScripts: () => ScriptResultList): Promise<void> {\n    this.execFlag = true;\n    // 执行脚本\n    const scriptResultList = await getExternalScripts();\n    // 假如已经被销毁了\n    if (!this.iframe) return;\n    const iframeWindow = this.iframe.contentWindow;\n    // 标志位，执行代码前设置\n    iframeWindow.__POWERED_BY_WUJIE__ = true;\n    // 用户自定义代码前\n    const beforeScriptResultList: ScriptObjectLoader[] = getPresetLoaders(\"jsBeforeLoaders\", this.plugins);\n    // 用户自定义代码后\n    const afterScriptResultList: ScriptObjectLoader[] = getPresetLoaders(\"jsAfterLoaders\", this.plugins);\n    // 同步代码\n    const syncScriptResultList: ScriptResultList = [];\n    // async代码无需保证顺序，所以不用放入执行队列\n    const asyncScriptResultList: ScriptResultList = [];\n    // defer代码需要保证顺序并且DOMContentLoaded前完成，这里统一放置同步脚本后执行\n    const deferScriptResultList: ScriptResultList = [];\n    scriptResultList.forEach((scriptResult) => {\n      if (scriptResult.defer) deferScriptResultList.push(scriptResult);\n      else if (scriptResult.async) asyncScriptResultList.push(scriptResult);\n      else syncScriptResultList.push(scriptResult);\n    });\n\n    // 插入代码前\n    beforeScriptResultList.forEach((beforeScriptResult) => {\n      this.execQueue.push(() =>\n        this.fiber\n          ? this.requestIdleCallback(() => insertScriptToIframe(beforeScriptResult, iframeWindow))\n          : insertScriptToIframe(beforeScriptResult, iframeWindow)\n      );\n    });\n\n    // 同步代码\n    syncScriptResultList.concat(deferScriptResultList).forEach((scriptResult) => {\n      this.execQueue.push(() =>\n        scriptResult.contentPromise.then((content) =>\n          this.fiber\n            ? this.requestIdleCallback(() => insertScriptToIframe({ ...scriptResult, content }, iframeWindow))\n            : insertScriptToIframe({ ...scriptResult, content }, iframeWindow)\n        )\n      );\n    });\n\n    // 异步代码\n    asyncScriptResultList.forEach((scriptResult) => {\n      scriptResult.contentPromise.then((content) => {\n        this.fiber\n          ? this.requestIdleCallback(() => insertScriptToIframe({ ...scriptResult, content }, iframeWindow))\n          : insertScriptToIframe({ ...scriptResult, content }, iframeWindow);\n      });\n    });\n\n    //框架主动调用mount方法\n    this.execQueue.push(this.fiber ? () => this.requestIdleCallback(() => this.mount()) : () => this.mount());\n\n    //触发 DOMContentLoaded 事件\n    const domContentLoadedTrigger = () => {\n      eventTrigger(iframeWindow.document, \"DOMContentLoaded\");\n      eventTrigger(iframeWindow, \"DOMContentLoaded\");\n      this.execQueue.shift()?.();\n    };\n    this.execQueue.push(this.fiber ? () => this.requestIdleCallback(domContentLoadedTrigger) : domContentLoadedTrigger);\n\n    // 插入代码后\n    afterScriptResultList.forEach((afterScriptResult) => {\n      this.execQueue.push(() =>\n        this.fiber\n          ? this.requestIdleCallback(() => insertScriptToIframe(afterScriptResult, iframeWindow))\n          : insertScriptToIframe(afterScriptResult, iframeWindow)\n      );\n    });\n\n    //触发 loaded 事件\n    const domLoadedTrigger = () => {\n      eventTrigger(iframeWindow.document, \"readystatechange\");\n      eventTrigger(iframeWindow, \"load\");\n      this.execQueue.shift()?.();\n    };\n    this.execQueue.push(this.fiber ? () => this.requestIdleCallback(domLoadedTrigger) : domLoadedTrigger);\n    // 由于没有办法准确定位是哪个代码做了mount，保活、重建模式提前关闭loading\n    if (this.alive || !isFunction(this.iframe.contentWindow.__WUJIE_UNMOUNT)) removeLoading(this.el);\n    this.execQueue.shift()();\n\n    // 所有的execQueue队列执行完毕，start才算结束，保证串行的执行子应用\n    return new Promise((resolve) => {\n      this.execQueue.push(() => {\n        resolve();\n        this.execQueue.shift()?.();\n      });\n    });\n  }\n\n  /**\n   * 框架主动发起mount，如果子应用是异步渲染实例，比如将生命周__WUJIE_MOUNT放到async函数内\n   * 此时如果采用fiber模式渲染（主应用调用mount的时机也是异步不确定的），框架调用mount时可能\n   * 子应用的__WUJIE_MOUNT还没有挂载到window，所以这里封装一个mount函数，当子应用是异步渲染\n   * 实例时，子应用异步函数里面最后加上window.__WUJIE.mount()来主动调用\n   */\n  public mount(): void {\n    if (this.mountFlag) return;\n    if (isFunction(this.iframe.contentWindow.__WUJIE_MOUNT)) {\n      removeLoading(this.el);\n      this.lifecycles?.beforeMount?.(this.iframe.contentWindow);\n      this.iframe.contentWindow.__WUJIE_MOUNT();\n      this.lifecycles?.afterMount?.(this.iframe.contentWindow);\n      this.mountFlag = true;\n    }\n    if (this.alive) {\n      this.lifecycles?.activated?.(this.iframe.contentWindow);\n    }\n    this.execQueue.shift()?.();\n  }\n\n  /** 保活模式和使用proxyLocation.href跳转链接都不应该销毁shadow */\n  public async unmount(): Promise<void> {\n    this.activeFlag = false;\n    // 清理子应用过期的同步参数\n    clearInactiveAppUrl();\n    if (this.alive) {\n      this.lifecycles?.deactivated?.(this.iframe.contentWindow);\n    }\n    if (!this.mountFlag) return;\n    if (isFunction(this.iframe.contentWindow.__WUJIE_UNMOUNT) && !this.alive && !this.hrefFlag) {\n      this.lifecycles?.beforeUnmount?.(this.iframe.contentWindow);\n      await this.iframe.contentWindow.__WUJIE_UNMOUNT();\n      this.lifecycles?.afterUnmount?.(this.iframe.contentWindow);\n      this.mountFlag = false;\n      this.bus?.$clear();\n      if (!this.degrade) {\n        clearChild(this.shadowRoot);\n        // head body需要复用，每次都要清空事件\n        removeEventListener(this.head);\n        removeEventListener(this.body);\n      }\n      clearChild(this.head);\n      clearChild(this.body);\n    }\n  }\n\n  /** 销毁子应用 */\n  public destroy() {\n    this.unmount();\n    this.bus.$clear();\n    this.shadowRoot = null;\n    this.proxy = null;\n    this.proxyDocument = null;\n    this.proxyLocation = null;\n    this.execQueue = null;\n    this.provide = null;\n    this.degradeAttrs = null;\n    this.styleSheetElements = null;\n    this.bus = null;\n    this.replace = null;\n    this.fetch = null;\n    this.execFlag = null;\n    this.mountFlag = null;\n    this.hrefFlag = null;\n    this.document = null;\n    this.head = null;\n    this.body = null;\n    this.elementEventCacheMap = null;\n    this.lifecycles = null;\n    this.plugins = null;\n    this.provide = null;\n    this.inject = null;\n    this.execQueue = null;\n    this.prefix = null;\n    // 清除 dom\n    if (this.el) {\n      clearChild(this.el);\n      this.el = null;\n    }\n    // 清除 iframe 沙箱\n    if (this.iframe) {\n      const iframeWindow = this.iframe.contentWindow;\n      if (iframeWindow?.__WUJIE_EVENTLISTENER__) {\n        iframeWindow.__WUJIE_EVENTLISTENER__.forEach((o) => {\n          iframeWindow.removeEventListener(o.type, o.listener, o.options);\n        });\n      }\n      this.iframe.parentNode?.removeChild(this.iframe);\n      this.iframe = null;\n    }\n    deleteWujieById(this.id);\n  }\n\n  /** 当子应用再次激活后，只运行mount函数，样式需要重新恢复 */\n  public rebuildStyleSheets(): void {\n    if (this.styleSheetElements && this.styleSheetElements.length) {\n      this.styleSheetElements.forEach((styleSheetElement) => {\n        rawElementAppendChild.call(this.degrade ? this.document.head : this.shadowRoot.head, styleSheetElement);\n      });\n    }\n    this.patchCssRules();\n  }\n\n  /**\n   * 子应用样式打补丁\n   * 1、兼容:root选择器样式到:host选择器上\n   * 2、将@font-face定义到shadowRoot外部\n   */\n  public patchCssRules(): void {\n    if (this.degrade) return;\n    if (this.shadowRoot.host.hasAttribute(WUJIE_DATA_ATTACH_CSS_FLAG)) return;\n    const [hostStyleSheetElement, fontStyleSheetElement] = getPatchStyleElements(\n      Array.from(this.iframe.contentDocument.querySelectorAll(\"style\")).map(\n        (styleSheetElement) => styleSheetElement.sheet\n      )\n    );\n    if (hostStyleSheetElement) {\n      this.shadowRoot.head.appendChild(hostStyleSheetElement);\n      this.styleSheetElements.push(hostStyleSheetElement);\n    }\n    if (fontStyleSheetElement) {\n      this.shadowRoot.host.appendChild(fontStyleSheetElement);\n    }\n    (hostStyleSheetElement || fontStyleSheetElement) &&\n      this.shadowRoot.host.setAttribute(WUJIE_DATA_ATTACH_CSS_FLAG, \"\");\n  }\n\n  /**\n   * @param id 子应用的id，唯一标识\n   * @param url 子应用的url，可以包含protocol、host、path、query、hash\n   */\n  constructor(options: {\n    name: string;\n    url: string;\n    attrs: { [key: string]: any };\n    degradeAttrs: { [key: string]: any };\n    fiber: boolean;\n    degrade;\n    plugins: Array<plugin>;\n    lifecycles: lifecycles;\n  }) {\n    // 传递inject给嵌套子应用\n    if (window.__POWERED_BY_WUJIE__) this.inject = window.__WUJIE.inject;\n    else {\n      this.inject = {\n        idToSandboxMap: idToSandboxCacheMap,\n        appEventObjMap,\n        mainHostPath: window.location.protocol + \"//\" + window.location.host,\n      };\n    }\n    const { name, url, attrs, fiber, degradeAttrs, degrade, lifecycles, plugins } = options;\n    this.id = name;\n    this.fiber = fiber;\n    this.degrade = degrade || !wujieSupport;\n    this.bus = new EventBus(this.id);\n    this.url = url;\n    this.degradeAttrs = degradeAttrs;\n    this.provide = { bus: this.bus };\n    this.styleSheetElements = [];\n    this.execQueue = [];\n    this.lifecycles = lifecycles;\n    this.plugins = getPlugins(plugins);\n\n    // 创建目标地址的解析\n    const { urlElement, appHostPath, appRoutePath } = appRouteParse(url);\n    const { mainHostPath } = this.inject;\n    // 创建iframe\n    this.iframe = iframeGenerator(this, attrs, mainHostPath, appHostPath, appRoutePath);\n\n    if (this.degrade) {\n      const { proxyDocument, proxyLocation } = localGenerator(this.iframe, urlElement, mainHostPath, appHostPath);\n      this.proxyDocument = proxyDocument;\n      this.proxyLocation = proxyLocation;\n    } else {\n      const { proxyWindow, proxyDocument, proxyLocation } = proxyGenerator(\n        this.iframe,\n        urlElement,\n        mainHostPath,\n        appHostPath\n      );\n      this.proxy = proxyWindow;\n      this.proxyDocument = proxyDocument;\n      this.proxyLocation = proxyLocation;\n    }\n    this.provide.location = this.proxyLocation;\n\n    addSandboxCacheWithWujie(this.id, this);\n  }\n}\n", "import importHTML, { processCssLoader } from \"./entry\";\nimport { StyleObject, ScriptAttributes } from \"./template\";\nimport <PERSON><PERSON><PERSON>, { lifecycle } from \"./sandbox\";\nimport { defineWujieWebComponent, addLoading } from \"./shadow\";\nimport { processAppForHrefJump } from \"./sync\";\nimport { getPlugins } from \"./plugin\";\nimport {\n  wujieSupport,\n  mergeOptions,\n  isFunction,\n  requestIdleCallback,\n  isMatchSyncQueryById,\n  warn,\n  stopMainAppRun,\n} from \"./utils\";\nimport { getWujieById, getOptionsById, addSandboxCacheWithOptions } from \"./common\";\nimport { EventBus } from \"./event\";\nimport { WUJIE_TIPS_NOT_SUPPORTED } from \"./constant\";\n\nexport const bus = new EventBus(Date.now().toString());\n\nexport interface ScriptObjectLoader {\n  /** 脚本地址，内联为空 */\n  src?: string;\n  /** 脚本是否为module模块 */\n  module?: boolean;\n  /** 脚本是否为async执行 */\n  async?: boolean;\n  /** 脚本是否设置crossorigin */\n  crossorigin?: boolean;\n  /** 脚本crossorigin的类型 */\n  crossoriginType?: \"anonymous\" | \"use-credentials\" | \"\";\n  /** 脚本原始属性 */\n  attrs?: ScriptAttributes;\n  /** 内联script的代码 */\n  content?: string;\n  /** 执行回调钩子 */\n  callback?: (appWindow: Window) => any;\n  /** 子应用加载完毕事件 */\n  onload?: Function;\n}\nexport interface plugin {\n  /** 处理html的loader */\n  htmlLoader?: (code: string) => string;\n  /** js排除列表 */\n  jsExcludes?: Array<string | RegExp>;\n  /** js忽略列表 */\n  jsIgnores?: Array<string | RegExp>;\n  /** 处理js加载前的loader */\n  jsBeforeLoaders?: Array<ScriptObjectLoader>;\n  /** 处理js的loader */\n  jsLoader?: (code: string, url: string, base: string) => string;\n  /** 处理js加载后的loader */\n  jsAfterLoaders?: Array<ScriptObjectLoader>;\n  /** css排除列表 */\n  cssExcludes?: Array<string | RegExp>;\n  /** css忽略列表 */\n  cssIgnores?: Array<string | RegExp>;\n  /** 处理css加载前的loader */\n  cssBeforeLoaders?: Array<StyleObject>;\n  /** 处理css的loader */\n  cssLoader?: (code: string, url: string, base: string) => string;\n  /** 处理css加载后的loader */\n  cssAfterLoaders?: Array<StyleObject>;\n  /** 子应用 window addEventListener 钩子回调 */\n  windowAddEventListenerHook?: eventListenerHook;\n  /** 子应用 window removeEventListener 钩子回调 */\n  windowRemoveEventListenerHook?: eventListenerHook;\n  /** 子应用 document addEventListener 钩子回调 */\n  documentAddEventListenerHook?: eventListenerHook;\n  /** 子应用 document removeEventListener 钩子回调 */\n  documentRemoveEventListenerHook?: eventListenerHook;\n  /** 子应用 向body、head插入元素后执行的钩子回调 */\n  appendOrInsertElementHook?: <T extends Node>(element: T, iframeWindow: Window) => void;\n  /** 子应用劫持元素的钩子回调 */\n  patchElementHook?: <T extends Node>(element: T, iframeWindow: Window) => void;\n  /** 用户自定义覆盖子应用 window 属性 */\n  windowPropertyOverride?: (iframeWindow: Window) => void;\n  /** 用户自定义覆盖子应用 document 属性 */\n  documentPropertyOverride?: (iframeWindow: Window) => void;\n}\n\ntype eventListenerHook = (\n  iframeWindow: Window,\n  type: string,\n  handler: EventListenerOrEventListenerObject,\n  options?: boolean | AddEventListenerOptions\n) => void;\n\nexport type loadErrorHandler = (url: string, e: Error) => any;\n\ntype baseOptions = {\n  /** 唯一性用户必须保证 */\n  name: string;\n  /** 需要渲染的url */\n  url: string;\n  /** 需要渲染的html, 如果已有则无需从url请求 */\n  html?: string;\n  /** 代码替换钩子 */\n  replace?: (code: string) => string;\n  /** 自定义fetch */\n  fetch?: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\n  /** 注入给子应用的属性 */\n  props?: { [key: string]: any };\n  /** 自定义运行iframe的属性 */\n  attrs?: { [key: string]: any };\n  /** 自定义降级渲染iframe的属性 */\n  degradeAttrs?: { [key: string]: any };\n  /** 子应用采用fiber模式执行 */\n  fiber?: boolean;\n  /** 子应用保活，state不会丢失 */\n  alive?: boolean;\n  /** 子应用采用降级iframe方案 */\n  degrade?: boolean;\n  /** 子应用插件 */\n  plugins?: Array<plugin>;\n  /** 子应用生命周期 */\n  beforeLoad?: lifecycle;\n  beforeMount?: lifecycle;\n  afterMount?: lifecycle;\n  beforeUnmount?: lifecycle;\n  afterUnmount?: lifecycle;\n  activated?: lifecycle;\n  deactivated?: lifecycle;\n  loadError?: loadErrorHandler;\n};\n\nexport type preOptions = baseOptions & {\n  /** 预执行 */\n  exec?: boolean;\n};\n\nexport type startOptions = baseOptions & {\n  /** 渲染的容器 */\n  el: HTMLElement | string;\n  /**\n   * 路由同步开关\n   * 如果false，子应用跳转主应用路由无变化，但是主应用的history还是会增加\n   * https://html.spec.whatwg.org/multipage/history.html#the-history-interface\n   */\n  sync?: boolean;\n  /** 子应用短路径替换，路由同步时生效 */\n  prefix?: { [key: string]: string };\n  /** 子应用加载时loading元素 */\n  loading?: HTMLElement;\n};\n\ntype optionProperty = \"url\" | \"el\";\n\n/**\n * 合并 preOptions 和 startOptions，并且将 url 和 el 变成可选\n */\nexport type cacheOptions = Omit<preOptions & startOptions, optionProperty> &\n  Partial<Pick<startOptions, optionProperty>>;\n\n/**\n * 强制中断主应用运行\n * wujie.__WUJIE 如果为true说明当前运行环境是子应用\n * window.__POWERED_BY_WUJIE__ 如果为false说明子应用还没初始化完成\n * 上述条件同时成立说明主应用代码在iframe的loading阶段混入进来了，必须中断执行\n */\nif (window.__WUJIE && !window.__POWERED_BY_WUJIE__) {\n  stopMainAppRun();\n}\n\n// 处理子应用链接跳转\nprocessAppForHrefJump();\n\n// 定义webComponent容器\ndefineWujieWebComponent();\n\n// 如果不支持则告警\nif (!wujieSupport) warn(WUJIE_TIPS_NOT_SUPPORTED);\n\n/**\n * 缓存子应用配置\n */\nexport function setupApp(options: cacheOptions): void {\n  if (options.name) addSandboxCacheWithOptions(options.name, options);\n}\n\n/**\n * 运行无界app\n */\nexport async function startApp(startOptions: startOptions): Promise<Function | void> {\n  const sandbox = getWujieById(startOptions.name);\n  const cacheOptions = getOptionsById(startOptions.name);\n  // 合并缓存配置\n  const options = mergeOptions(startOptions, cacheOptions);\n  const {\n    name,\n    url,\n    html,\n    replace,\n    fetch,\n    props,\n    attrs,\n    degradeAttrs,\n    fiber,\n    alive,\n    degrade,\n    sync,\n    prefix,\n    el,\n    loading,\n    plugins,\n    lifecycles,\n  } = options;\n  // 已经初始化过的应用，快速渲染\n  if (sandbox) {\n    sandbox.plugins = getPlugins(plugins);\n    sandbox.lifecycles = lifecycles;\n    const iframeWindow = sandbox.iframe.contentWindow;\n    if (sandbox.preload) {\n      await sandbox.preload;\n    }\n    if (alive) {\n      // 保活\n      await sandbox.active({ url, sync, prefix, el, props, alive, fetch, replace });\n      // 预加载但是没有执行的情况\n      if (!sandbox.execFlag) {\n        sandbox.lifecycles?.beforeLoad?.(sandbox.iframe.contentWindow);\n        const { getExternalScripts } = await importHTML({\n          url,\n          html,\n          opts: {\n            fetch: fetch || window.fetch,\n            plugins: sandbox.plugins,\n            loadError: sandbox.lifecycles.loadError,\n            fiber,\n          },\n        });\n        await sandbox.start(getExternalScripts);\n      }\n      sandbox.lifecycles?.activated?.(sandbox.iframe.contentWindow);\n      return sandbox.destroy;\n    } else if (isFunction(iframeWindow.__WUJIE_MOUNT)) {\n      /**\n       * 子应用切换会触发webcomponent的disconnectedCallback调用sandbox.unmount进行实例销毁\n       * 此处是防止没有销毁webcomponent时调用startApp的情况，需要手动调用unmount\n       */\n      sandbox.unmount();\n      await sandbox.active({ url, sync, prefix, el, props, alive, fetch, replace });\n      // 正常加载的情况，先注入css，最后才mount。重新激活也保持同样的时序\n      sandbox.rebuildStyleSheets();\n      // 有渲染函数\n      sandbox.lifecycles?.beforeMount?.(sandbox.iframe.contentWindow);\n      iframeWindow.__WUJIE_MOUNT();\n      sandbox.lifecycles?.afterMount?.(sandbox.iframe.contentWindow);\n      sandbox.mountFlag = true;\n      return sandbox.destroy;\n    } else {\n      // 没有渲染函数\n      sandbox.destroy();\n    }\n  }\n\n  // 设置loading\n  addLoading(el, loading);\n  const newSandbox = new WuJie({ name, url, attrs, degradeAttrs, fiber, degrade, plugins, lifecycles });\n  newSandbox.lifecycles?.beforeLoad?.(newSandbox.iframe.contentWindow);\n  const { template, getExternalScripts, getExternalStyleSheets } = await importHTML({\n    url,\n    html,\n    opts: {\n      fetch: fetch || window.fetch,\n      plugins: newSandbox.plugins,\n      loadError: newSandbox.lifecycles.loadError,\n      fiber,\n    },\n  });\n\n  const processedHtml = await processCssLoader(newSandbox, template, getExternalStyleSheets);\n  await newSandbox.active({ url, sync, prefix, template: processedHtml, el, props, alive, fetch, replace });\n  await newSandbox.start(getExternalScripts);\n  return newSandbox.destroy;\n}\n\n/**\n * 预加载无界APP\n */\nexport function preloadApp(preOptions: preOptions): void {\n  requestIdleCallback((): void | Promise<void> => {\n    /**\n     * 已经存在\n     * url查询参数中有子应用的id，大概率是刷新浏览器或者分享url，此时需要直接打开子应用，无需预加载\n     */\n    if (getWujieById(preOptions.name) || isMatchSyncQueryById(preOptions.name)) return;\n    const cacheOptions = getOptionsById(preOptions.name);\n    // 合并缓存配置\n    const options = mergeOptions({ ...preOptions }, cacheOptions);\n    const {\n      name,\n      url,\n      html,\n      props,\n      alive,\n      replace,\n      fetch,\n      exec,\n      attrs,\n      degradeAttrs,\n      fiber,\n      degrade,\n      prefix,\n      plugins,\n      lifecycles,\n    } = options;\n\n    const sandbox = new WuJie({ name, url, attrs, degradeAttrs, fiber, degrade, plugins, lifecycles });\n    if (sandbox.preload) return sandbox.preload;\n    const runPreload = async () => {\n      sandbox.lifecycles?.beforeLoad?.(sandbox.iframe.contentWindow);\n      const { template, getExternalScripts, getExternalStyleSheets } = await importHTML({\n        url,\n        html,\n        opts: {\n          fetch: fetch || window.fetch,\n          plugins: sandbox.plugins,\n          loadError: sandbox.lifecycles.loadError,\n          fiber,\n        },\n      });\n      const processedHtml = await processCssLoader(sandbox, template, getExternalStyleSheets);\n      await sandbox.active({ url, props, prefix, alive, template: processedHtml, fetch, replace });\n      if (exec) {\n        await sandbox.start(getExternalScripts);\n      } else {\n        await getExternalScripts();\n      }\n    };\n    sandbox.preload = runPreload();\n  });\n}\n\n/**\n * 销毁无界APP\n */\nexport function destroyApp(id: string): void {\n  const sandbox = getWujieById(id);\n  if (sandbox) {\n    sandbox.destroy();\n  }\n}\n", "import { bus, preloadApp, startApp as rawStartApp, destroyApp, setupApp } from \"wujie\";\nimport { h, defineComponent } from \"vue\";\n\nconst wujieVueOptions = {\n  name: \"<PERSON>jieVue\",\n  props: {\n    width: { type: String, default: \"\" },\n    height: { type: String, default: \"\" },\n    name: { type: String, default: \"\" },\n    loading: { type: HTMLElement, default: undefined },\n    url: { type: String, default: \"\" },\n    sync: { type: Boolean, default: undefined },\n    prefix: { type: Object, default: undefined },\n    alive: { type: Boolean, default: undefined },\n    props: { type: Object, default: undefined },\n    attrs: { type: Object, default: undefined },\n    replace: { type: Function, default: undefined },\n    fetch: { type: Function, default: undefined },\n    fiber: { type: Boolean, default: undefined },\n    degrade: { type: Boolean, default: undefined },\n    plugins: { type: Array, default: null },\n    beforeLoad: { type: Function, default: null },\n    beforeMount: { type: Function, default: null },\n    afterMount: { type: Function, default: null },\n    beforeUnmount: { type: Function, default: null },\n    afterUnmount: { type: Function, default: null },\n    activated: { type: Function, default: null },\n    deactivated: { type: Function, default: null },\n    loadError: { type: Function, default: null },\n  },\n  data() {\n    return {\n      startAppQueue: Promise.resolve(),\n    };\n  },\n  mounted() {\n    bus.$onAll(this.handleEmit);\n    this.execStartApp();\n    this.$watch(\n      () => this.name + this.url,\n      () => this.execStartApp()\n    );\n  },\n  methods: {\n    handleEmit(event, ...args) {\n      this.$emit(event, ...args);\n    },\n    async startApp() {\n      try {\n        await rawStartApp({\n          name: this.name,\n          url: this.url,\n          el: this.$refs.wujie,\n          loading: this.loading,\n          alive: this.alive,\n          fetch: this.fetch,\n          props: this.props,\n          attrs: this.attrs,\n          replace: this.replace,\n          sync: this.sync,\n          prefix: this.prefix,\n          fiber: this.fiber,\n          degrade: this.degrade,\n          plugins: this.plugins,\n          beforeLoad: this.beforeLoad,\n          beforeMount: this.beforeMount,\n          afterMount: this.afterMount,\n          beforeUnmount: this.beforeUnmount,\n          afterUnmount: this.afterUnmount,\n          activated: this.activated,\n          deactivated: this.deactivated,\n          loadError: this.loadError,\n        });\n      } catch (error) {\n        console.log(error);\n      }\n    },\n    execStartApp() {\n      this.startAppQueue = this.startAppQueue.then(this.startApp);\n    },\n    destroy() {\n      destroyApp(this.name);\n    },\n  },\n  beforeDestroy() {\n    bus.$offAll(this.handleEmit);\n  },\n  render() {\n    return h(\"div\", {\n      style: {\n        width: this.width,\n        height: this.height,\n      },\n      ref: \"wujie\",\n    });\n  },\n};\n\nconst WujieVue = defineComponent(wujieVueOptions);\n\nWujieVue.setupApp = setupApp;\nWujieVue.preloadApp = preloadApp;\nWujieVue.bus = bus;\nWujieVue.destroyApp = destroyApp;\nWujieVue.install = function (app) {\n  app.component(\"WujieVue\", WujieVue);\n};\n\nexport default WujieVue;\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,aAASA,SAAQ,GAAG;AAClB;AAEA,aAAO,OAAO,UAAUA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AACjH,eAAO,OAAOA;AAAA,MAChB,IAAI,SAAUA,IAAG;AACf,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MACpH,GAAG,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAS,IAAI,OAAO,SAASD,SAAQ,CAAC;AAAA,IAC5F;AACA,WAAO,UAAUA,UAAS,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAS,IAAI,OAAO;AAAA;AAAA;;;ACT/F;AAAA;AAAA,QAAIE,WAAU,iBAAuB,SAAS;AAC9C,aAASC,uBAAsB;AAC7B;AACA,aAAO,UAAUA,uBAAsB,SAASA,uBAAsB;AACpE,eAAO;AAAA,MACT,GAAG,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAS,IAAI,OAAO;AACxE,UAAI,GACF,IAAI,CAAC,GACL,IAAI,OAAO,WACX,IAAI,EAAE,gBACN,IAAI,OAAO,kBAAkB,SAAUC,IAAGC,IAAGC,IAAG;AAC9C,QAAAF,GAAEC,EAAC,IAAIC,GAAE;AAAA,MACX,GACA,IAAI,cAAc,OAAO,SAAS,SAAS,CAAC,GAC5C,IAAI,EAAE,YAAY,cAClB,IAAI,EAAE,iBAAiB,mBACvB,IAAI,EAAE,eAAe;AACvB,eAAS,OAAOF,IAAGC,IAAGC,IAAG;AACvB,eAAO,OAAO,eAAeF,IAAGC,IAAG;AAAA,UACjC,OAAOC;AAAA,UACP,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,UAAU;AAAA,QACZ,CAAC,GAAGF,GAAEC,EAAC;AAAA,MACT;AACA,UAAI;AACF,eAAO,CAAC,GAAG,EAAE;AAAA,MACf,SAASD,IAAG;AACV,iBAAS,SAASG,QAAOH,IAAGC,IAAGC,IAAG;AAChC,iBAAOF,GAAEC,EAAC,IAAIC;AAAA,QAChB;AAAA,MACF;AACA,eAAS,KAAKF,IAAGC,IAAGC,IAAGE,IAAG;AACxB,YAAIC,KAAIJ,MAAKA,GAAE,qBAAqB,YAAYA,KAAI,WAClDK,KAAI,OAAO,OAAOD,GAAE,SAAS,GAC7BE,KAAI,IAAI,QAAQH,MAAK,CAAC,CAAC;AACzB,eAAO,EAAEE,IAAG,WAAW;AAAA,UACrB,OAAO,iBAAiBN,IAAGE,IAAGK,EAAC;AAAA,QACjC,CAAC,GAAGD;AAAA,MACN;AACA,eAAS,SAASN,IAAGC,IAAGC,IAAG;AACzB,YAAI;AACF,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,KAAKF,GAAE,KAAKC,IAAGC,EAAC;AAAA,UAClB;AAAA,QACF,SAASF,IAAG;AACV,iBAAO;AAAA,YACL,MAAM;AAAA,YACN,KAAKA;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,QAAE,OAAO;AACT,UAAIQ,KAAI,kBACN,IAAI,kBACJ,IAAI,aACJ,IAAI,aACJ,IAAI,CAAC;AACP,eAAS,YAAY;AAAA,MAAC;AACtB,eAAS,oBAAoB;AAAA,MAAC;AAC9B,eAAS,6BAA6B;AAAA,MAAC;AACvC,UAAI,IAAI,CAAC;AACT,aAAO,GAAG,GAAG,WAAY;AACvB,eAAO;AAAA,MACT,CAAC;AACD,UAAI,IAAI,OAAO,gBACb,IAAI,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;AAC1B,WAAK,MAAM,KAAK,EAAE,KAAK,GAAG,CAAC,MAAM,IAAI;AACrC,UAAI,IAAI,2BAA2B,YAAY,UAAU,YAAY,OAAO,OAAO,CAAC;AACpF,eAAS,sBAAsBR,IAAG;AAChC,SAAC,QAAQ,SAAS,QAAQ,EAAE,QAAQ,SAAUC,IAAG;AAC/C,iBAAOD,IAAGC,IAAG,SAAUD,IAAG;AACxB,mBAAO,KAAK,QAAQC,IAAGD,EAAC;AAAA,UAC1B,CAAC;AAAA,QACH,CAAC;AAAA,MACH;AACA,eAAS,cAAcA,IAAGC,IAAG;AAC3B,iBAAS,OAAOC,IAAGO,IAAGJ,IAAGC,IAAG;AAC1B,cAAIC,KAAI,SAASP,GAAEE,EAAC,GAAGF,IAAGS,EAAC;AAC3B,cAAI,YAAYF,GAAE,MAAM;AACtB,gBAAIG,KAAIH,GAAE,KACRC,KAAIE,GAAE;AACR,mBAAOF,MAAK,YAAYV,SAAQU,EAAC,KAAK,EAAE,KAAKA,IAAG,SAAS,IAAIP,GAAE,QAAQO,GAAE,OAAO,EAAE,KAAK,SAAUR,IAAG;AAClG,qBAAO,QAAQA,IAAGK,IAAGC,EAAC;AAAA,YACxB,GAAG,SAAUN,IAAG;AACd,qBAAO,SAASA,IAAGK,IAAGC,EAAC;AAAA,YACzB,CAAC,IAAIL,GAAE,QAAQO,EAAC,EAAE,KAAK,SAAUR,IAAG;AAClC,cAAAU,GAAE,QAAQV,IAAGK,GAAEK,EAAC;AAAA,YAClB,GAAG,SAAUV,IAAG;AACd,qBAAO,OAAO,SAASA,IAAGK,IAAGC,EAAC;AAAA,YAChC,CAAC;AAAA,UACH;AACA,UAAAA,GAAEC,GAAE,GAAG;AAAA,QACT;AACA,YAAIL;AACJ,UAAE,MAAM,WAAW;AAAA,UACjB,OAAO,SAAS,MAAMF,IAAGI,IAAG;AAC1B,qBAAS,6BAA6B;AACpC,qBAAO,IAAIH,GAAE,SAAUA,IAAGC,IAAG;AAC3B,uBAAOF,IAAGI,IAAGH,IAAGC,EAAC;AAAA,cACnB,CAAC;AAAA,YACH;AACA,mBAAOA,KAAIA,KAAIA,GAAE,KAAK,4BAA4B,0BAA0B,IAAI,2BAA2B;AAAA,UAC7G;AAAA,QACF,CAAC;AAAA,MACH;AACA,eAAS,iBAAiBD,IAAGC,IAAGE,IAAG;AACjC,YAAIK,KAAID;AACR,eAAO,SAAUH,IAAGC,IAAG;AACrB,cAAIG,OAAM,EAAG,OAAM,MAAM,8BAA8B;AACvD,cAAIA,OAAM,GAAG;AACX,gBAAI,YAAYJ,GAAG,OAAMC;AACzB,mBAAO;AAAA,cACL,OAAO;AAAA,cACP,MAAM;AAAA,YACR;AAAA,UACF;AACA,eAAKF,GAAE,SAASC,IAAGD,GAAE,MAAME,QAAK;AAC9B,gBAAIC,KAAIH,GAAE;AACV,gBAAIG,IAAG;AACL,kBAAIG,KAAI,oBAAoBH,IAAGH,EAAC;AAChC,kBAAIM,IAAG;AACL,oBAAIA,OAAM,EAAG;AACb,uBAAOA;AAAA,cACT;AAAA,YACF;AACA,gBAAI,WAAWN,GAAE,OAAQ,CAAAA,GAAE,OAAOA,GAAE,QAAQA,GAAE;AAAA,qBAAa,YAAYA,GAAE,QAAQ;AAC/E,kBAAIK,OAAMD,GAAG,OAAMC,KAAI,GAAGL,GAAE;AAC5B,cAAAA,GAAE,kBAAkBA,GAAE,GAAG;AAAA,YAC3B,MAAO,cAAaA,GAAE,UAAUA,GAAE,OAAO,UAAUA,GAAE,GAAG;AACxD,YAAAK,KAAI;AACJ,gBAAIE,KAAI,SAASV,IAAGC,IAAGE,EAAC;AACxB,gBAAI,aAAaO,GAAE,MAAM;AACvB,kBAAIF,KAAIL,GAAE,OAAO,IAAI,GAAGO,GAAE,QAAQ,EAAG;AACrC,qBAAO;AAAA,gBACL,OAAOA,GAAE;AAAA,gBACT,MAAMP,GAAE;AAAA,cACV;AAAA,YACF;AACA,wBAAYO,GAAE,SAASF,KAAI,GAAGL,GAAE,SAAS,SAASA,GAAE,MAAMO,GAAE;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AACA,eAAS,oBAAoBV,IAAGC,IAAG;AACjC,YAAIE,KAAIF,GAAE,QACRO,KAAIR,GAAE,SAASG,EAAC;AAClB,YAAIK,OAAM,EAAG,QAAOP,GAAE,WAAW,MAAM,YAAYE,MAAKH,GAAE,SAAS,QAAQ,MAAMC,GAAE,SAAS,UAAUA,GAAE,MAAM,GAAG,oBAAoBD,IAAGC,EAAC,GAAG,YAAYA,GAAE,WAAW,aAAaE,OAAMF,GAAE,SAAS,SAASA,GAAE,MAAM,IAAI,UAAU,sCAAsCE,KAAI,UAAU,IAAI;AAC1R,YAAIC,KAAI,SAASI,IAAGR,GAAE,UAAUC,GAAE,GAAG;AACrC,YAAI,YAAYG,GAAE,KAAM,QAAOH,GAAE,SAAS,SAASA,GAAE,MAAMG,GAAE,KAAKH,GAAE,WAAW,MAAM;AACrF,YAAII,KAAID,GAAE;AACV,eAAOC,KAAIA,GAAE,QAAQJ,GAAED,GAAE,UAAU,IAAIK,GAAE,OAAOJ,GAAE,OAAOD,GAAE,SAAS,aAAaC,GAAE,WAAWA,GAAE,SAAS,QAAQA,GAAE,MAAM,IAAIA,GAAE,WAAW,MAAM,KAAKI,MAAKJ,GAAE,SAAS,SAASA,GAAE,MAAM,IAAI,UAAU,kCAAkC,GAAGA,GAAE,WAAW,MAAM;AAAA,MAC9P;AACA,eAAS,aAAaF,IAAG;AACvB,YAAIC,KAAI;AAAA,UACN,QAAQD,GAAE,CAAC;AAAA,QACb;AACA,aAAKA,OAAMC,GAAE,WAAWD,GAAE,CAAC,IAAI,KAAKA,OAAMC,GAAE,aAAaD,GAAE,CAAC,GAAGC,GAAE,WAAWD,GAAE,CAAC,IAAI,KAAK,WAAW,KAAKC,EAAC;AAAA,MAC3G;AACA,eAAS,cAAcD,IAAG;AACxB,YAAIC,KAAID,GAAE,cAAc,CAAC;AACzB,QAAAC,GAAE,OAAO,UAAU,OAAOA,GAAE,KAAKD,GAAE,aAAaC;AAAA,MAClD;AACA,eAAS,QAAQD,IAAG;AAClB,aAAK,aAAa,CAAC;AAAA,UACjB,QAAQ;AAAA,QACV,CAAC,GAAGA,GAAE,QAAQ,cAAc,IAAI,GAAG,KAAK,MAAM,IAAE;AAAA,MAClD;AACA,eAAS,OAAOC,IAAG;AACjB,YAAIA,MAAK,OAAOA,IAAG;AACjB,cAAIC,KAAID,GAAE,CAAC;AACX,cAAIC,GAAG,QAAOA,GAAE,KAAKD,EAAC;AACtB,cAAI,cAAc,OAAOA,GAAE,KAAM,QAAOA;AACxC,cAAI,CAAC,MAAMA,GAAE,MAAM,GAAG;AACpB,gBAAIQ,KAAI,IACNJ,KAAI,SAAS,OAAO;AAClB,qBAAO,EAAEI,KAAIR,GAAE,SAAS,KAAI,EAAE,KAAKA,IAAGQ,EAAC,EAAG,QAAO,KAAK,QAAQR,GAAEQ,EAAC,GAAG,KAAK,OAAO,OAAI;AACpF,qBAAO,KAAK,QAAQ,GAAG,KAAK,OAAO,MAAI;AAAA,YACzC;AACF,mBAAOJ,GAAE,OAAOA;AAAA,UAClB;AAAA,QACF;AACA,cAAM,IAAI,UAAUP,SAAQG,EAAC,IAAI,kBAAkB;AAAA,MACrD;AACA,aAAO,kBAAkB,YAAY,4BAA4B,EAAE,GAAG,eAAe;AAAA,QACnF,OAAO;AAAA,QACP,cAAc;AAAA,MAChB,CAAC,GAAG,EAAE,4BAA4B,eAAe;AAAA,QAC/C,OAAO;AAAA,QACP,cAAc;AAAA,MAChB,CAAC,GAAG,kBAAkB,cAAc,OAAO,4BAA4B,GAAG,mBAAmB,GAAG,EAAE,sBAAsB,SAAUD,IAAG;AACnI,YAAIC,KAAI,cAAc,OAAOD,MAAKA,GAAE;AACpC,eAAO,CAAC,CAACC,OAAMA,OAAM,qBAAqB,yBAAyBA,GAAE,eAAeA,GAAE;AAAA,MACxF,GAAG,EAAE,OAAO,SAAUD,IAAG;AACvB,eAAO,OAAO,iBAAiB,OAAO,eAAeA,IAAG,0BAA0B,KAAKA,GAAE,YAAY,4BAA4B,OAAOA,IAAG,GAAG,mBAAmB,IAAIA,GAAE,YAAY,OAAO,OAAO,CAAC,GAAGA;AAAA,MACvM,GAAG,EAAE,QAAQ,SAAUA,IAAG;AACxB,eAAO;AAAA,UACL,SAASA;AAAA,QACX;AAAA,MACF,GAAG,sBAAsB,cAAc,SAAS,GAAG,OAAO,cAAc,WAAW,GAAG,WAAY;AAChG,eAAO;AAAA,MACT,CAAC,GAAG,EAAE,gBAAgB,eAAe,EAAE,QAAQ,SAAUA,IAAGE,IAAGE,IAAGK,IAAGJ,IAAG;AACtE,mBAAWA,OAAMA,KAAI;AACrB,YAAIC,KAAI,IAAI,cAAc,KAAKN,IAAGE,IAAGE,IAAGK,EAAC,GAAGJ,EAAC;AAC7C,eAAO,EAAE,oBAAoBH,EAAC,IAAII,KAAIA,GAAE,KAAK,EAAE,KAAK,SAAUN,IAAG;AAC/D,iBAAOA,GAAE,OAAOA,GAAE,QAAQM,GAAE,KAAK;AAAA,QACnC,CAAC;AAAA,MACH,GAAG,sBAAsB,CAAC,GAAG,OAAO,GAAG,GAAG,WAAW,GAAG,OAAO,GAAG,GAAG,WAAY;AAC/E,eAAO;AAAA,MACT,CAAC,GAAG,OAAO,GAAG,YAAY,WAAY;AACpC,eAAO;AAAA,MACT,CAAC,GAAG,EAAE,OAAO,SAAUN,IAAG;AACxB,YAAIC,KAAI,OAAOD,EAAC,GACdE,KAAI,CAAC;AACP,iBAASE,MAAKH,GAAG,CAAAC,GAAE,KAAKE,EAAC;AACzB,eAAOF,GAAE,QAAQ,GAAG,SAAS,OAAO;AAClC,iBAAOA,GAAE,UAAS;AAChB,gBAAIF,KAAIE,GAAE,IAAI;AACd,gBAAIF,MAAKC,GAAG,QAAO,KAAK,QAAQD,IAAG,KAAK,OAAO,OAAI;AAAA,UACrD;AACA,iBAAO,KAAK,OAAO,MAAI;AAAA,QACzB;AAAA,MACF,GAAG,EAAE,SAAS,QAAQ,QAAQ,YAAY;AAAA,QACxC,aAAa;AAAA,QACb,OAAO,SAAS,MAAMC,IAAG;AACvB,cAAI,KAAK,OAAO,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ,GAAG,KAAK,OAAO,OAAI,KAAK,WAAW,MAAM,KAAK,SAAS,QAAQ,KAAK,MAAM,GAAG,KAAK,WAAW,QAAQ,aAAa,GAAG,CAACA,GAAG,UAASC,MAAK,KAAM,SAAQA,GAAE,OAAO,CAAC,KAAK,EAAE,KAAK,MAAMA,EAAC,KAAK,CAAC,MAAM,CAACA,GAAE,MAAM,CAAC,CAAC,MAAM,KAAKA,EAAC,IAAI;AAAA,QACtR;AAAA,QACA,MAAM,SAAS,OAAO;AACpB,eAAK,OAAO;AACZ,cAAIF,KAAI,KAAK,WAAW,CAAC,EAAE;AAC3B,cAAI,YAAYA,GAAE,KAAM,OAAMA,GAAE;AAChC,iBAAO,KAAK;AAAA,QACd;AAAA,QACA,mBAAmB,SAAS,kBAAkBC,IAAG;AAC/C,cAAI,KAAK,KAAM,OAAMA;AACrB,cAAIC,KAAI;AACR,mBAAS,OAAOE,IAAGK,IAAG;AACpB,mBAAOH,GAAE,OAAO,SAASA,GAAE,MAAML,IAAGC,GAAE,OAAOE,IAAGK,OAAMP,GAAE,SAAS,QAAQA,GAAE,MAAM,IAAI,CAAC,CAACO;AAAA,UACzF;AACA,mBAASA,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,gBAAIJ,KAAI,KAAK,WAAWI,EAAC,GACvBH,KAAID,GAAE;AACR,gBAAI,WAAWA,GAAE,OAAQ,QAAO,OAAO,KAAK;AAC5C,gBAAIA,GAAE,UAAU,KAAK,MAAM;AACzB,kBAAIE,KAAI,EAAE,KAAKF,IAAG,UAAU,GAC1BK,KAAI,EAAE,KAAKL,IAAG,YAAY;AAC5B,kBAAIE,MAAKG,IAAG;AACV,oBAAI,KAAK,OAAOL,GAAE,SAAU,QAAO,OAAOA,GAAE,UAAU,IAAE;AACxD,oBAAI,KAAK,OAAOA,GAAE,WAAY,QAAO,OAAOA,GAAE,UAAU;AAAA,cAC1D,WAAWE,IAAG;AACZ,oBAAI,KAAK,OAAOF,GAAE,SAAU,QAAO,OAAOA,GAAE,UAAU,IAAE;AAAA,cAC1D,OAAO;AACL,oBAAI,CAACK,GAAG,OAAM,MAAM,wCAAwC;AAC5D,oBAAI,KAAK,OAAOL,GAAE,WAAY,QAAO,OAAOA,GAAE,UAAU;AAAA,cAC1D;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ,SAAS,OAAOL,IAAGC,IAAG;AAC5B,mBAASC,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,gBAAIO,KAAI,KAAK,WAAWP,EAAC;AACzB,gBAAIO,GAAE,UAAU,KAAK,QAAQ,EAAE,KAAKA,IAAG,YAAY,KAAK,KAAK,OAAOA,GAAE,YAAY;AAChF,kBAAIJ,KAAII;AACR;AAAA,YACF;AAAA,UACF;AACA,UAAAJ,OAAM,YAAYL,MAAK,eAAeA,OAAMK,GAAE,UAAUJ,MAAKA,MAAKI,GAAE,eAAeA,KAAI;AACvF,cAAIC,KAAID,KAAIA,GAAE,aAAa,CAAC;AAC5B,iBAAOC,GAAE,OAAON,IAAGM,GAAE,MAAML,IAAGI,MAAK,KAAK,SAAS,QAAQ,KAAK,OAAOA,GAAE,YAAY,KAAK,KAAK,SAASC,EAAC;AAAA,QACzG;AAAA,QACA,UAAU,SAAS,SAASN,IAAGC,IAAG;AAChC,cAAI,YAAYD,GAAE,KAAM,OAAMA,GAAE;AAChC,iBAAO,YAAYA,GAAE,QAAQ,eAAeA,GAAE,OAAO,KAAK,OAAOA,GAAE,MAAM,aAAaA,GAAE,QAAQ,KAAK,OAAO,KAAK,MAAMA,GAAE,KAAK,KAAK,SAAS,UAAU,KAAK,OAAO,SAAS,aAAaA,GAAE,QAAQC,OAAM,KAAK,OAAOA,KAAI;AAAA,QAC1N;AAAA,QACA,QAAQ,SAAS,OAAOD,IAAG;AACzB,mBAASC,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,gBAAIC,KAAI,KAAK,WAAWD,EAAC;AACzB,gBAAIC,GAAE,eAAeF,GAAG,QAAO,KAAK,SAASE,GAAE,YAAYA,GAAE,QAAQ,GAAG,cAAcA,EAAC,GAAG;AAAA,UAC5F;AAAA,QACF;AAAA,QACA,SAAS,SAAS,OAAOF,IAAG;AAC1B,mBAASC,KAAI,KAAK,WAAW,SAAS,GAAGA,MAAK,GAAG,EAAEA,IAAG;AACpD,gBAAIC,KAAI,KAAK,WAAWD,EAAC;AACzB,gBAAIC,GAAE,WAAWF,IAAG;AAClB,kBAAII,KAAIF,GAAE;AACV,kBAAI,YAAYE,GAAE,MAAM;AACtB,oBAAIK,KAAIL,GAAE;AACV,8BAAcF,EAAC;AAAA,cACjB;AACA,qBAAOO;AAAA,YACT;AAAA,UACF;AACA,gBAAM,MAAM,uBAAuB;AAAA,QACrC;AAAA,QACA,eAAe,SAAS,cAAcR,IAAGC,IAAGE,IAAG;AAC7C,iBAAO,KAAK,WAAW;AAAA,YACrB,UAAU,OAAOH,EAAC;AAAA,YAClB,YAAYC;AAAA,YACZ,SAASE;AAAA,UACX,GAAG,WAAW,KAAK,WAAW,KAAK,MAAM,IAAI;AAAA,QAC/C;AAAA,MACF,GAAG;AAAA,IACL;AACA,WAAO,UAAUL,sBAAqB,OAAO,QAAQ,aAAa,MAAM,OAAO,QAAQ,SAAS,IAAI,OAAO;AAAA;AAAA;;;AC/S3G;AAAA;AAEA,QAAI,UAAU,6BAAyC;AACvD,WAAO,UAAU;AAGjB,QAAI;AACF,2BAAqB;AAAA,IACvB,SAAS,sBAAsB;AAC7B,UAAI,OAAO,eAAe,UAAU;AAClC,mBAAW,qBAAqB;AAAA,MAClC,OAAO;AACL,iBAAS,KAAK,wBAAwB,EAAE,OAAO;AAAA,MACjD;AAAA,IACF;AAAA;AAAA;;;ACdA,SAAS,mBAAmB,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC/C,MAAI;AACF,QAAI,IAAI,EAAE,CAAC,EAAE,CAAC,GACZ,IAAI,EAAE;AAAA,EACV,SAASa,IAAG;AACV,WAAO,KAAK,EAAEA,EAAC;AAAA,EACjB;AACA,IAAE,OAAO,EAAE,CAAC,IAAI,QAAQ,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC;AAC9C;AACA,SAAS,kBAAkB,GAAG;AAC5B,SAAO,WAAY;AACjB,QAAI,IAAI,MACN,IAAI;AACN,WAAO,IAAI,QAAQ,SAAU,GAAG,GAAG;AACjC,UAAI,IAAI,EAAE,MAAM,GAAG,CAAC;AACpB,eAAS,MAAMA,IAAG;AAChB,2BAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,QAAQA,EAAC;AAAA,MACtD;AACA,eAAS,OAAOA,IAAG;AACjB,2BAAmB,GAAG,GAAG,GAAG,OAAO,QAAQ,SAASA,EAAC;AAAA,MACvD;AACA,YAAM,MAAM;AAAA,IACd,CAAC;AAAA,EACH;AACF;;;;;;;;;ACxBA,SAAS,gBAAgB,GAAG;AAC1B,MAAI,MAAM,QAAQ,CAAC,EAAG,QAAO;AAC/B;;;ACFA,SAAS,sBAAsB,GAAG,GAAG;AACnC,MAAI,IAAI,QAAQ,IAAI,OAAO,eAAe,OAAO,UAAU,EAAE,OAAO,QAAQ,KAAK,EAAE,YAAY;AAC/F,MAAI,QAAQ,GAAG;AACb,QAAI,GACF,GACA,GACA,GACA,IAAI,CAAC,GACL,IAAI,MACJ,IAAI;AACN,QAAI;AACF,UAAI,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,GAAG;AACrC,YAAI,OAAO,CAAC,MAAM,EAAG;AACrB,YAAI;AAAA,MACN,MAAO,QAAO,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,GAAG,UAAU,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,WAAW,IAAI,IAAI,KAAG;AAAA,IACzF,SAASC,IAAG;AACV,UAAI,MAAI,IAAIA;AAAA,IACd,UAAE;AACA,UAAI;AACF,YAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,MAAM,IAAI,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC,MAAM,GAAI;AAAA,MACzE,UAAE;AACA,YAAI,EAAG,OAAM;AAAA,MACf;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;AC1BA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,GAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AACpD,SAAO;AACT;;;ACHA,SAAS,4BAA4B,GAAG,GAAG;AACzC,MAAI,GAAG;AACL,QAAI,YAAY,OAAO,EAAG,QAAO,kBAAiB,GAAG,CAAC;AACtD,QAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACvC,WAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAiB,GAAG,CAAC,IAAI;AAAA,EACtN;AACF;;;ACPA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;;;ACEA,SAAS,eAAe,GAAG,GAAG;AAC5B,SAAO,gBAAe,CAAC,KAAK,sBAAqB,GAAG,CAAC,KAAK,4BAA2B,GAAG,CAAC,KAAK,iBAAgB;AAChH;;;ACLA,SAAS,mBAAmB,GAAG;AAC7B,MAAI,MAAM,QAAQ,CAAC,EAAG,QAAO,kBAAiB,CAAC;AACjD;;;ACHA,SAAS,iBAAiB,GAAG;AAC3B,MAAI,eAAe,OAAO,UAAU,QAAQ,EAAE,OAAO,QAAQ,KAAK,QAAQ,EAAE,YAAY,EAAG,QAAO,MAAM,KAAK,CAAC;AAChH;;;ACFA,SAAS,qBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;;;ACEA,SAAS,mBAAmB,GAAG;AAC7B,SAAO,mBAAkB,CAAC,KAAK,iBAAgB,CAAC,KAAK,4BAA2B,CAAC,KAAK,mBAAkB;AAC1G;;;ACLO,IAAMC,eAAe;AACrB,IAAMC,kBAAkB;AACxB,IAAMC,kBAAkB;AACxB,IAAMC,+BAA+B;AACrC,IAAMC,+BAA+B;AACrC,IAAMC,oBAAoB;AAC1B,IAAMC,6BAA6B;AAGnC,IAAMC,qBAAqB;AAC3B,IAAMC,kBAAkB;AACxB,IAAMC,oBACX;AACK,IAAMC,sBACX;AAEK,IAAMC,oBAAiB;AAgBvB,IAAMC,oBAAoB;AAC1B,IAAMC,6BAA6B;AACnC,IAAMC,sBAAsB;AAC5B,IAAMC,6BAA6BD,sBAAsB;AACzD,IAAME,wBAAwB;AAC9B,IAAMC,sBAAsB;AAC5B,IAAMC,2BAA2B;AACjC,IAAMC,oCAAoC;AAC1C,IAAMC,iCAAiC;AACvC,IAAMC,kCAAkC;AACxC,IAAMC,2BAA2B;AACjC,IAAMC,uBAAuB;AAC7B,IAAMC,+BACX;;;ACjCK,SAASC,WAAWC,OAAqB;AAC9C,SAAO,OAAOA,UAAU;AAC1B;AAEO,SAASC,eAAeC,SAAkB;AAC/C,UACEA,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASC,YAAY,OAAM,WAC3BD,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASC,YAAY,OAAM,YAC3BD,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASC,YAAY,OAAM,aAC3BD,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASC,YAAY,OAAM;AAE/B;AAEO,IAAMC,eAAeC,OAAOC,SAASD,OAAOE;AAQnD,IAAMC,gBAAgB,OAAOC,SAASC,QAAQ,cAAc,OAAOD,SAASC,QAAQ;AACpF,IAAMC,qBAAqB,oBAAIC,QAAmC;AAC3D,IAAMC,aAAa,SAAbA,YAAcC,IAAY;AACrC,MAAIH,mBAAmBI,IAAID,EAAE,GAAG;AAC9B,WAAO;EACT;AAEA,MAAME,WAAWR,gBAAgB,OAAOM,OAAO,cAAc,OAAOA,OAAO,cAAc,OAAOA,OAAO;AACvG,MAAIE,UAAU;AACZL,uBAAmBM,IAAIH,IAAIE,QAAQ;EACrC;AACA,SAAOA;AACT;AAEA,IAAME,aAAa,oBAAIN,QAAmC;AACnD,SAASO,kBAAkBL,IAAsB;AACtD,MAAII,WAAWH,IAAID,EAAE,GAAG;AACtB,WAAOI,WAAWE,IAAIN,EAAE;EAC1B;AACA,MAAMO,UAAUP,GAAGQ,KAAKC,QAAQ,QAAQ,MAAM,KAAK,CAACT,GAAGU,eAAe,WAAW;AACjFN,aAAWD,IAAIH,IAAIO,OAAO;AAC1B,SAAOA;AACT;AAEA,IAAMI,uBAAuB,oBAAIb,QAA4C;AACtE,SAASc,gBAAgBZ,IAAqC;AACnE,MAAMa,sBACJb,GAAGc,aAAad,GAAGc,UAAUC,gBAAgBf,MAAMgB,OAAOC,oBAAoBjB,GAAGc,SAAS,EAAEI,SAAS;AAEvG,MAAIL,oBAAqB,QAAO;AAEhC,MAAIF,qBAAqBV,IAAID,EAAE,GAAG;AAChC,WAAOW,qBAAqBL,IAAIN,EAAE;EACpC;AAEA,MAAImB,gBAAgBN;AACpB,MAAI,CAACM,eAAe;AAClB,QAAMC,WAAWpB,GAAGqB,SAAS;AAC7B,QAAMC,6BAA6B;AACnC,QAAMC,aAAa;AACnBJ,oBAAgBG,2BAA2BE,KAAKJ,QAAQ,KAAKG,WAAWC,KAAKJ,QAAQ;EACvF;AAEAT,uBAAqBR,IAAIH,IAAImB,aAAa;AAC1C,SAAOA;AACT;AAGA,IAAMM,gBAAgB,oBAAI3B,QAGxB;AAEK,SAAS4B,mBAAmBC,QAAmDzC,OAAY;AAChG,MAAIa,WAAWb,KAAK,KAAK,CAACmB,kBAAkBnB,KAAK,KAAK,CAAC0B,gBAAgB1B,KAAK,GAAG;AAC7E,QAAI,CAACuC,cAAcxB,IAAI0B,MAAM,GAAG;AAC9BF,oBAActB,IAAIwB,QAAQ,oBAAI7B,QAAQ,CAAC;AACvC2B,oBAAcnB,IAAIqB,MAAM,EAAExB,IAAIjB,OAAOA,KAAK;IAC5C,WAAW,CAACuC,cAAcnB,IAAIqB,MAAM,EAAE1B,IAAIf,KAAK,GAAG;AAChDuC,oBAAcnB,IAAIqB,MAAM,EAAExB,IAAIjB,OAAOA,KAAK;IAC5C;EACF;AACF;AAEO,SAAS0C,eAAeD,QAAaE,GAAa;AACvD,MAAM3C,QAAQyC,OAAOE,CAAC;AACtB,MAAIJ,cAAcxB,IAAI0B,MAAM,KAAKF,cAAcnB,IAAIqB,MAAM,EAAE1B,IAAIf,KAAK,GAAG;AACrE,WAAOuC,cAAcnB,IAAIqB,MAAM,EAAErB,IAAIpB,KAAK;EAC5C;AACA,MAAIa,WAAWb,KAAK,KAAK,CAACmB,kBAAkBnB,KAAK,KAAK,CAAC0B,gBAAgB1B,KAAK,GAAG;AAC7E,QAAM4C,aAAaC,SAASjB,UAAUkB,KAAKC,KAAK/C,OAAOyC,MAAM;AAC7D,QAAIF,cAAcxB,IAAI0B,MAAM,GAAG;AAC7BF,oBAAcnB,IAAIqB,MAAM,EAAExB,IAAIjB,OAAO4C,UAAU;IACjD,OAAO;AACLL,oBAActB,IAAIwB,QAAQ,oBAAI7B,QAAQ,CAAC;AACvC2B,oBAAcnB,IAAIqB,MAAM,EAAExB,IAAIjB,OAAO4C,UAAU;IACjD;AACA,aAAWI,OAAOhD,OAAO;AACvB4C,iBAAWI,GAAG,IAAIhD,MAAMgD,GAAG;IAC7B;AACA,QAAIhD,MAAMwB,eAAe,WAAW,KAAK,CAACoB,WAAWpB,eAAe,WAAW,GAAG;AAEhFM,aAAOmB,eAAeL,YAAY,aAAa;QAAE5C,OAAOA,MAAM4B;QAAWsB,YAAY;QAAOC,UAAU;MAAK,CAAC;IAC9G;AACA,WAAOP;EACT;AACA,SAAO5C;AACT;AAEO,SAASoD,iBAAiBC,IAA+B;AAC9D,SAAOhD,OAAOI,SAAS6C,cAAa,UAAAC,OAAWC,cAAY,IAAA,EAAAD,OAAKF,IAAE,IAAA,CAAI;AACxE;AAEO,SAASI,kBAAkBC,SAAsBC,OAA+B;AACrF7B,SAAO8B,KAAKD,KAAK,EAAEE,QAAQ,SAACvC,MAAS;AACnCoC,YAAQI,aAAaxC,MAAMqC,MAAMrC,IAAI,CAAC;EACxC,CAAC;AACH;AAEO,SAASyC,cAAcC,KAI5B;AACA,MAAI,CAACA,KAAK;AACRC,UAAMC,iBAAiB;AACvB,UAAM,IAAIC,MAAM;EAClB;AACA,MAAMC,aAAaC,uBAAuBL,GAAG;AAC7C,MAAMM,cAAcF,WAAWG,WAAW,OAAOH,WAAWI;AAC5D,MAAIC,eAAeL,WAAWM,WAAWN,WAAWO,SAASP,WAAWQ;AACxE,MAAI,CAACH,aAAaI,WAAW,GAAG,EAAGJ,gBAAe,MAAMA;AACxD,SAAO;IAAEL;IAAYE;IAAaG;EAAa;AACjD;AAEO,SAASJ,uBAAuBL,KAAgC;AACrE,MAAMN,UAAUrD,OAAOI,SAASqE,cAAc,GAAG;AACjDpB,UAAQqB,OAAOf;AACfN,UAAQqB,OAAOrB,QAAQqB;AACvB,SAAOrB;AACT;AAEO,SAASsB,yBAAyBC,eAA6D;AACpG,MAAMC,cAAcD,cAAcN,UAAU;AAC5C,SAAOQ,mBAAI,IAAIC,gBAAgBF,WAAW,EAAEG,QAAQ,CAAC,EAAEC,OAAO,SAAC3C,GAAG4C,GAAM;AACtE5C,MAAE4C,EAAE,CAAC,CAAC,IAAIA,EAAE,CAAC;AACb,WAAO5C;EACT,GAAG,CAAC,CAA2B;AACjC;AAKO,SAAS6C,qBAAqBnC,IAAqB;AACxD,MAAMoC,WAAWT,yBAAyBX,uBAAuBhE,OAAOqF,SAASX,IAAI,CAAC;AACtF,SAAOjD,OAAO8B,KAAK6B,QAAQ,EAAEE,SAAStC,EAAE;AAC1C;AAMO,SAASuC,uBACdC,cACAC,YAOAC,MACM;AAEN,MAAMC,yBAAyBH,aAAaI,QAAQrE,UAAUkC;AAC9DgC,aAAWlE,UAAUkC,eAAe,SAAUxC,MAActB,OAAqB;AAC/E,QAAIkG,cAAclG;AAClB,QAAIsB,SAASyE,KAAMG,eAAcC,gBAAgBnG,OAAO,KAAKoG,WAAW,IAAI,IAAI;AAChFJ,2BAAuBjD,KAAK,MAAMzB,MAAM4E,WAAW;EACrD;AAEA,MAAMG,iCAAiCvE,OAAOwE,yBAAyBR,WAAWlE,WAAWmE,IAAI;AACjG,MAAQ7C,aAAuCmD,+BAAvCnD,YAAYqD,eAA2BF,+BAA3BE,cAAcnF,OAAaiF,+BAAbjF,KAAKH,OAAQoF,+BAARpF;AACvCa,SAAOmB,eAAe6C,WAAWlE,WAAWmE,MAAM;IAChD7C;IACAqD;IACAnF,KAAK,SAALA,MAAiB;AACf,aAAOA,KAAI2B,KAAK,IAAI;IACtB;IACA9B,KAAK,SAALA,IAAe8D,MAAM;AACnB9D,WAAI8B,KAAK,MAAMoD,gBAAgBpB,MAAM,KAAKqB,SAAS,IAAI,CAAC;IAC1D;EACF,CAAC;AAEH;AAEO,SAASI,UAAUC,eAA+B;AACvD,MAAMf,YAAWe;AACjB,SAAOf,UAASnB,WAAW,OAAOmB,UAASlB,OAAOkB,UAAShB;AAC7D;AAEO,SAASyB,gBAAgBnC,KAAa0C,MAAc9B,MAAwB;AACjF,MAAI;AAEF,QAAIZ,KAAK;AAEP,UAAIY,QAAQZ,IAAIa,WAAW,GAAG,EAAG,QAAOb;AACxC,aAAO,IAAI2C,IAAI3C,KAAK0C,IAAI,EAAE3B;IAC5B,MAAO,QAAOf;EAChB,SAAE4C,SAAM;AACN,WAAO5C;EACT;AACF;AAIO,SAAS6C,WAAWxD,IAAYyD,QAA2C;AAAA,MAAAC;AAChF,MAAIC,gBAAgB3C,uBAAuBhE,OAAOqF,SAASX,IAAI;AAC/D,MAAMU,WAAWT,yBAAyBgC,aAAa;AACvDA,kBAAgB;AAChB,MAAMC,UAAU5G,OAAO6G,mBAAmBzB,SAASpC,EAAE,KAAK,EAAE;AAC5D,MAAM8D,kBAAcJ,iBAAGE,QAAQG,MAAM,YAAY,OAAC,QAAAL,mBAAA,SAAA,SAA3BA,eAA8B,CAAC;AACtD,MAAID,UAAUK,gBAAgB;AAC5B,WAAOF,QAAQI,QAAO,IAAA9D,OAAK4D,gBAAc,GAAA,GAAKL,OAAOK,cAAc,CAAC;EACtE;AACA,SAAOF;AACT;AAEO,IAAMK,sBAAsBjH,OAAOiH,uBAAwB,SAACC,IAAY;AAAA,SAAKC,WAAWD,IAAI,CAAC;AAAC;AAE9F,SAASE,aAAaC,WAA8C;AACzE,SAAO,OAAOA,cAAc,WAAYjH,SAAS6C,cAAcoE,SAAS,IAAoBA;AAC9F;AAEO,SAASC,KAAKC,KAAaC,OAAkB;AAAA,MAAAC;AAClD,GAAAA,WAAAC,aAAO,QAAAD,aAAA,UAAPA,SAASH,KAAI,iBAAApE,OAAkBqE,GAAG,GAAIC,KAAI;AAC5C;AAEO,SAAS5D,MAAM2D,KAAaC,OAAkB;AAAA,MAAAG;AACnD,GAAAA,YAAAD,aAAO,QAAAC,cAAA,UAAPA,UAAS/D,MAAK,kBAAAV,OAAmBqE,GAAG,GAAIC,KAAI;AAC9C;AAEO,SAASI,cAAcb,OAAO;AACnC,MAAMc,QAAQd,MAAM7F,QAAQ,GAAG,IAAI;AACnC,MAAM4G,MAAMf,MAAMgB,YAAY,GAAG;AACjC,SAAOhB,MAAMiB,UAAUH,OAAOC,GAAG;AACnC;AAEO,SAASG,qBAAqBC,OAAO;AAC1C,MAAIC,QAAOD,KAAK,MAAK,UAAU;AAC7B,WAAO;EACT;AACA,MAAI;AACF,QAAAE,OAA6B,IAAI9B,IAAI4B,OAAO7C,SAASX,IAAI,GAAjD2D,SAAMD,KAANC,QAAQhE,WAAQ+D,KAAR/D;AAChB,QAAMiE,QAAQjE,SAASkE,MAAM,GAAG;AAEhCD,UAAME,IAAI;AACV,WAAA,GAAAtF,OAAUmF,MAAM,EAAAnF,OAAGoF,MAAMG,KAAK,GAAG,GAAC,GAAA;EACpC,SAASC,GAAG;AACVhB,YAAQJ,KAAKoB,CAAC;AACd,WAAO;EACT;AACF;AAGO,SAASC,QAAQC,QAA6D;AACnF,SAAO,SAAUC,MAAmC;AAAA,aAAAC,OAAAC,UAAApH,QAAlBqH,OAAI,IAAAC,MAAAH,OAAA,IAAAA,OAAA,IAAA,CAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAAJF,WAAIE,OAAA,CAAA,IAAAH,UAAAG,IAAA;IAAA;AACpC,WAAON,OAAO3D,OAAO,SAACkE,SAAS1I,IAAE;AAAA,aAAMf,WAAWe,EAAE,IAAIA,GAAE2I,MAAA,QAAA,CAACD,OAAO,EAAAjG,OAAK8F,IAAI,CAAA,IAAIG;IAAO,GAAGN,QAAQ,EAAE;EACrG;AACF;AAGO,SAASQ,SAASnC,IAAqB;AAC5CoC,UAAQC,QAAQ,EAAEC,KAAKtC,EAAE;AAC3B;AAGO,SAASuC,UAAUC,SAAwBC,UAA6C;AAAA,WAAAC,QAAAb,UAAApH,QAAxBqH,OAAI,IAAAC,MAAAW,QAAA,IAAAA,QAAA,IAAA,CAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAAJb,SAAIa,QAAA,CAAA,IAAAd,UAAAc,KAAA;EAAA;AACzE,MAAI;AACF,QAAIH,WAAWA,QAAQ/H,SAAS,GAAG;AACjC+H,cACGI,IAAI,SAACC,QAAM;AAAA,eAAKA,OAAOJ,QAAQ;MAAC,CAAA,EAChCK,OAAO,SAACC,MAAI;AAAA,eAAKvK,WAAWuK,IAAI;MAAC,CAAA,EACjCzG,QAAQ,SAACyG,MAAI;AAAA,eAAKA,KAAIb,MAAA,QAAIJ,IAAI;MAAC,CAAA;IACpC;EACF,SAASN,GAAG;AACV9E,UAAM8E,CAAC;EACT;AACF;AAEO,SAASwB,gBAAgB7G,SAA+B;AAAA,MAAA8G;AAC7D,WAAOA,mBAAA9G,QAAQxD,aAAO,QAAAsK,qBAAA,SAAA,SAAfA,iBAAiBrK,YAAY,OAAM;AAC5C;AAEA,IAAIsK,QAAQ;AACL,SAASC,eAAehH,SAA4BiH,KAAoB;AAC7E,MAAIJ,gBAAgB7G,OAAO,GAAG;AAC5B,QAAMkH,YAAYD,OAAOE,OAAOJ,OAAO;AACvC/G,YAAQI,aAAagH,iBAAiBF,SAAS;EACjD;AACF;AAEO,SAASG,iBAAiBrH,SAA2C;AAC1E,MAAI6G,gBAAgB7G,OAAO,GAAG;AAC5B,WAAOA,QAAQsH,aAAaF,eAAe;EAC7C;AACA,SAAO;AACT;AAGO,SAASG,aAAaC,SAAuBC,cAA4B;AAC9E,SAAO;IACL7J,MAAM4J,QAAQ5J;IACd8J,IAAIF,QAAQE,OAAMD,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcC;IAChCpH,KAAKkH,QAAQlH,QAAOmH,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcnH;IAClCqH,MAAMH,QAAQG,SAAQF,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcE;IACpCC,MAAMJ,QAAQI,SAASC,SAAYL,QAAQI,OAAOH,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcG;IAChEjE,SAAS6D,QAAQ7D,YAAW8D,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAc9D;IAC1CmE,OAAON,QAAQM,UAASL,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcK;IACtCC,OAAOP,QAAQO,UAASN,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcM;IACtCC,MAAMR,QAAQQ,SAASH,SAAYL,QAAQQ,OAAOP,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcO;IAChE5E,QAAQoE,QAAQpE,WAAUqE,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcrE;IACxC6E,SAAST,QAAQS,YAAWR,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcQ;;IAE1ChI,OAAOuH,QAAQvH,UAAU4H,SAAYL,QAAQvH,SAAQwH,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcxH,UAAS,CAAC;IAC7EiI,cAAcV,QAAQU,iBAAiBL,SAAYL,QAAQU,gBAAeT,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcS,iBAAgB,CAAC;;IAEzGC,OAAOX,QAAQW,UAAUN,SAAYL,QAAQW,SAAQV,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcU,WAAUN,SAAYJ,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcU,QAAQ;IAC/GC,OAAOZ,QAAQY,UAAUP,SAAYL,QAAQY,QAAQX,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcW;IACnEC,SAASb,QAAQa,YAAYR,SAAYL,QAAQa,UAAUZ,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcY;IACzEhC,SAASmB,QAAQnB,YAAWoB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcpB;IAC1CiC,YAAY;MACVC,YAAYf,QAAQe,eAAcd,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcc;MAChDC,aAAahB,QAAQgB,gBAAef,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAce;MAClDC,YAAYjB,QAAQiB,eAAchB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcgB;MAChDC,eAAelB,QAAQkB,kBAAiBjB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAciB;MACtDC,cAAcnB,QAAQmB,iBAAgBlB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAckB;MACpDC,WAAWpB,QAAQoB,cAAanB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcmB;MAC9CC,aAAarB,QAAQqB,gBAAepB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcoB;MAClDC,WAAWtB,QAAQsB,cAAarB,iBAAY,QAAZA,iBAAY,SAAA,SAAZA,aAAcqB;IAChD;EACF;AACF;AAKO,SAASC,aAAarB,IAAqCsB,WAAmBC,QAAc;AACjG,MAAIC;AACJ,MAAI,OAAOvM,OAAOwM,gBAAgB,YAAY;AAC5CD,YAAQ,IAAIC,YAAYH,WAAW;MAAEC;IAAO,CAAC;EAC/C,OAAO;AACLC,YAAQnM,SAASqM,YAAY,aAAa;AAC1CF,UAAMG,gBAAgBL,WAAW,MAAM,OAAOC,MAAM;EACtD;AACAvB,KAAG4B,cAAcJ,KAAK;AACxB;AAEO,SAASK,iBAAiB;AAC/BtF,OAAKuF,0BAA0B;AAC/B,QAAM,IAAI/I,MAAMgJ,mBAAmB;AACrC;;;ACrXA,IAAMC,mBAAmB;AACzB,IAAMC,mBAAmB;AACzB,IAAMC,mBAAmB;AACzB,IAAMC,oBAAoB;AAC1B,IAAMC,qBAAqB;AAC3B,IAAMC,qBAAqB;AAC3B,IAAMC,oBAAoB;AAC1B,IAAMC,yBAAyB;AAC/B,IAAMC,sBAAsB;AAC5B,IAAMC,iBAAiB;AACvB,IAAMC,iCAAiC;AACvC,IAAMC,kBAAkB;AACxB,IAAMC,eAAe;AACrB,IAAMC,kBAAkB;AACxB,IAAMC,mBAAmB;AACzB,IAAMC,mBAAmB;AACzB,IAAMC,qBAAqB;AAC3B,IAAMC,oBAAoB;AAC1B,IAAMC,qBAAqB;AAC3B,IAAMC,sBAAsB;AAC5B,IAAMC,qBAAqB;AAgD3B,SAASC,YAAYC,KAAK;AACxB,SAAOA,IAAIC,WAAW,IAAI,KAAKD,IAAIC,WAAW,SAAS,KAAKD,IAAIC,WAAW,UAAU;AACvF;AAEA,SAASC,cAAcC,MAAMC,SAAS;AACpC,SAAO,IAAIC,IAAIF,MAAMC,OAAO,EAAEE,SAAS;AACzC;AAEA,SAASC,sBAAsBC,MAAM;AACnC,MAAMC,cAAc,CAClB,mBACA,UACA,0BACA,mBACA,wBAAwB;AAE1B,SAAO,CAACD,QAAQC,YAAYC,QAAQF,IAAI,MAAM;AAChD;AAOO,SAASG,mBAAmBC,cAAc;AAC/C,MAAMC,UAAU;AAChB,MAAMC,UAAUD,QAAQE,KAAKH,YAAY;AAEzC,MAAI,CAACE,SAAS;AACZ,WAAO,CAAC;EACV;AAEA,MAAME,mBAAmBF,QAAQ,CAAC;AAClC,MAAMG,oBAAoB;AAC1B,MAAMC,mBAAmB,CAAC;AAE1B,MAAIC;AACJ,UAAQA,mBAAmBF,kBAAkBF,KAAKC,gBAAgB,OAAO,MAAM;AAC7E,QAAMI,gBAAgBD,iBAAiB,CAAC;AACxC,QAAME,iBAAiBF,iBAAiB,CAAC;AACzCD,qBAAiBE,aAAa,IAAIC;EACpC;AAEA,SAAOH;AACT;AAEA,SAASI,0BAA0B;AACjC,MAAMC,IAAIC,OAAOC,SAASC,cAAc,QAAQ;AAChD,SAAO,cAAcH;AACvB;AAEO,IAAMI,uBAAuB,SAAvBA,sBAAwBC,UAAQ;AAAA,MAAEC,oBAAiBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAK,SAAA,QAAAG,OAC9DJ,oBAAoB,mCAAmC,IAAE,QAAA,EAAAI,OAASL,UAAQ,wBAAA;AAAA;AAC7E,IAAMM,8BAA8B,SAA9BA,6BAA+BC,OAAK;AAAA,SAAA,qBAAAF,OAA0BE,OAAK,wBAAA;AAAA;AACzE,IAAMC,yBAAyB,SAAzBA,wBAA0BC,WAAS;AAAA,MAAE7B,OAAIsB,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAE,SAAA,QAAAG,OACjDzB,MAAI,UAAA,EAAAyB,OAAWI,WAAS,wBAAA;AAAA;AAC3B,IAAMC,4BAA4B;AAClC,IAAMC,8BAA8B,SAA9BA,6BAA+BvC,KAAG;AAAA,SAAA,qBAAAiC,OAA0BjC,OAAO,QAAM,wBAAA;AAAA;AAC/E,IAAMwC,+BAA+B,SAA/BA,8BAAgCH,WAAWI,eAAa;AAAA,SAAA,QAAAR,OAC3DQ,gBAAgB,aAAa,UAAQ,UAAA,EAAAR,OAAWI,WAAS,uBAAA;AAAA;AAapD,SAAf,WAAmCK,KAAatC,SAAiBuC,qBAAgD;AAC/G,MAAMC,UAA0B,CAAA;AAChC,MAAMC,SAAwB,CAAA;AAC9B,MAAIC,QAAQ;AACZ,MAAML,gBAAgBnB,wBAAwB;AAC9C,MAAMyB,WAAWL,IAKdM,QAAQtD,oBAAoB,EAAE,EAE9BsD,QAAQ7D,gBAAgB,SAAC8D,OAAU;AAIlC,QAAMC,YAAY,CAAC,CAACD,MAAMA,MAAMzD,gBAAgB;AAChD,QAAI0D,WAAW;AACb,UAAMC,YAAYF,MAAMA,MAAMxD,gBAAgB;AAC9C,UAAM2D,cAAcH,MAAMA,MAAMtD,iBAAiB;AAEjD,UAAIwD,WAAW;AACb,YAAME,OAAOF,aAAaA,UAAU,CAAC;AACrC,YAAIG,UAAUD;AAEd,YAAIA,QAAQ,CAACtD,YAAYsD,IAAI,GAAG;AAC9BC,oBAAUpD,cAAcmD,MAAMjD,OAAO;QACvC;AACA,YAAIgD,aAAa;AACf,iBAAOb,4BAA4Be,OAAO;QAC5C;AAEAT,eAAOU,KAAK;UAAEC,KAAKF;QAAQ,CAAC;AAC5B,eAAO3B,qBAAqB2B,OAAO;MACrC;IACF;AAEA,QAAMG,wBACJR,MAAMA,MAAM7D,8BAA8B,KAAK6D,MAAMA,MAAM5D,eAAe,KAAK,CAAC4D,MAAMA,MAAM3D,YAAY;AAC1G,QAAImE,uBAAuB;AACzB,UAAAC,eAAuBT,MAAMA,MAAM5D,eAAe,GAACsE,gBAAAC,eAAAF,cAAA,CAAA,GAAxC9B,WAAQ+B,cAAA,CAAA;AACnB,aAAOhC,qBAAqBC,UAAU,IAAI;IAC5C;AAEA,WAAOqB;EACT,CAAC,EACAD,QAAQzD,iBAAiB,SAAC0D,OAAU;AACnC,QAAIrD,mBAAmBiE,KAAKZ,KAAK,GAAG;AAClC,aAAOV,4BAA4B,YAAY;IACjD,OAAO;AACL,UAAMuB,OAAOC,cAAcd,KAAK;AAChCJ,aAAOU,KAAK;QAAEC,KAAK;QAAIQ,SAASF;MAAK,CAAC;AACtC,aAAO5B,4BAA4BW,OAAOd,SAAS,CAAC;IACtD;EACF,CAAC,EACAiB,QAAQtE,kBAAkB,SAACuE,OAAOgB,WAAc;AAC/C,QAAMC,eAAeD,UAAUhB,MAAMpD,mBAAmB;AACxD,QAAMsE,iBAAiB,CAAC,CAACF,UAAUhB,MAAM/D,mBAAmB;AAC5D,QAAMkF,sBAAsBH,UAAUhB,MAAMnD,kBAAkB;AAC9D,QAAMuE,mBAAkBD,wBAAmB,QAAnBA,wBAAmB,SAAA,SAAnBA,oBAAsB,CAAC,MAAK;AACpD,QAAME,qBACH7B,iBAAiB,CAAC,CAACwB,UAAUhB,MAAMhE,sBAAsB,KAAO,CAACwD,iBAAiB0B;AAGrF,QAAMI,yBAAyBN,UAAUhB,MAAMpE,iBAAiB;AAChE,QAAM2F,oBAAoBD,0BAA0BA,uBAAuB,CAAC;AAC5E,QAAI,CAAChE,sBAAsBiE,iBAAiB,GAAG;AAC7C,aAAOvB;IACT;AAGA,QAAItE,iBAAiBkF,KAAKZ,KAAK,KAAKgB,UAAUhB,MAAMrE,gBAAgB,GAAG;AAKrE,UAAM6F,qBAAqBR,UAAUhB,MAAMnE,kBAAkB;AAC7D,UAAM4F,wBAAwBT,UAAUhB,MAAMrE,gBAAgB;AAC9D,UAAI+F,mBAAmBD,yBAAyBA,sBAAsB,CAAC;AAEvE,UAAI5B,SAAS2B,oBAAoB;AAC/B,cAAM,IAAIG,YAAY,2CAA2C;MACnE,OAAO;AAEL,YAAID,oBAAoB,CAAC5E,YAAY4E,gBAAgB,GAAG;AACtDA,6BAAmBzE,cAAcyE,kBAAkBvE,OAAO;QAC5D;AAEA0C,gBAAQA,SAAU2B,sBAAsBE;MAC1C;AAEA,UAAIT,cAAc;AAChB,eAAO3B,4BAA4BoC,oBAAoB,SAAS;MAClE;AAEA,UAAIL,oBAAoB;AACtB,eAAO9B,6BAA6BmC,oBAAoB,WAAWlC,aAAa;MAClF;AAEA,UAAIkC,kBAAkB;AACpB,YAAME,gBAAgB,CAAC,CAACZ,UAAUhB,MAAMlE,kBAAkB;AAC1D,YAAM+F,gBAAgB,CAAC,CAACb,UAAUhB,MAAMjE,iBAAiB;AACzD4D,gBAAQW,KACNsB,iBAAiBC,gBACb;UACEC,OAAOF;UACPG,OAAOF;UACPtB,KAAKmB;UACLM,QAAQd;UACRe,aAAa,CAAC,CAACd;UACfe,iBAAiBd;UACjBe,OAAOzE,mBAAmBsC,KAAK;QACjC,IACA;UACEO,KAAKmB;UACLM,QAAQd;UACRe,aAAa,CAAC,CAACd;UACfe,iBAAiBd;UACjBe,OAAOzE,mBAAmBsC,KAAK;QACjC,CACN;AACA,eAAOb,uBACLuC,kBACCE,iBAAiB,WAAaC,iBAAiB,WAAY,EAC9D;MACF;AAEA,aAAO7B;IACT,OAAO;AACL,UAAIiB,cAAc;AAChB,eAAO3B,4BAA4B,SAAS;MAC9C;AAEA,UAAI+B,oBAAoB;AACtB,eAAO9B,6BAA6B,WAAWC,aAAa;MAC9D;AAGA,UAAMqB,OAAOC,cAAcd,KAAK;AAGhC,UAAMoC,qBAAqBvB,KAAKwB,MAAM,SAAS,EAAEC,MAAM,SAACC,MAAI;AAAA,eAAK,CAACA,KAAKC,KAAK,KAAKD,KAAKC,KAAK,EAAExF,WAAW,IAAI;MAAC,CAAA;AAE7G,UAAI,CAACoF,sBAAsBvB,MAAM;AAC/BlB,gBAAQW,KAAK;UACXC,KAAK;UACLQ,SAASF;UACTmB,QAAQd;UACRe,aAAa,CAAC,CAACd;UACfe,iBAAiBd;UACjBe,OAAOzE,mBAAmBsC,KAAK;QACjC,CAAC;MACH;AAEA,aAAOX;IACT;EACF,CAAC;AAEH,MAAIoD,YAAY;IACd3C;IACAH;IACAC;;IAEAC,OAAOA,SAASF,QAAQA,QAAQb,SAAS,CAAC;EAC5C;AACA,MAAI,OAAOY,wBAAwB,YAAY;AAC7C+C,gBAAY/C,oBAAoB+C,SAAS;EAC3C;AAEA,SAAOA;AACT;;;AC5SO,SAASC,aAAYC,MAAqC;AAAA,MAAlCC,UAAOD,KAAPC,SAASC,UAAOF,KAAPE;AACtC,SAAO,SAACC,MAAY;AAAA,QAAEC,MAAWC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAE,QAAEG,OAAYH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAAA,WAClDE,QAAQR,QAAQS,IAAI,SAACC,QAAM;AAAA,aAAKA,OAAOC;IAAS,CAAA,CAAC,EAAEV,UAAUA,QAAQC,IAAI,IAAIA,MAAMC,KAAKI,IAAI;EAAC;AACjG;AAKO,SAASK,YAAWC,QAAqC;AAAA,MAAlCb,UAAOa,OAAPb,SAASC,UAAOY,OAAPZ;AACrC,SAAO,SAACC,MAAY;AAAA,QAAEC,MAAWC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;AAAE,QAAEG,OAAYH,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAAA,WAClDE,QAAQR,QAAQS,IAAI,SAACC,QAAM;AAAA,aAAKA,OAAOI;IAAQ,CAAA,CAAC,EAAEb,UAAUA,QAAQC,IAAI,IAAIA,MAAMC,KAAKI,IAAI;EAAC;AAChG;AAMO,SAASQ,iBAAiBC,YAA+BhB,SAAmD;AACjH,MAAMiB,UAAkDjB,QACrDS,IAAI,SAACC,QAAM;AAAA,WAAKA,OAAOM,UAAU;EAAC,CAAA,EAClCE,OAAO,SAACD,UAAO;AAAA,WAAKA,aAAO,QAAPA,aAAO,SAAA,SAAPA,SAASZ;EAAM,CAAA;AACtC,MAAMc,MAAMF,QAAQG,OAAO,SAACC,YAAYC,YAAU;AAAA,WAAKD,WAAWE,OAAOD,UAAU;EAAC,GAAE,CAAA,CAAE;AACxF,SAAON,eAAe,qBAAqBG,IAAIK,QAAQ,IAAIL;AAC7D;AAMO,SAASM,iBAAiBT,YAA+BhB,SAAmD;AACjH,SAAOA,QACJS,IAAI,SAACC,QAAM;AAAA,WAAKA,OAAOM,UAAU;EAAC,CAAA,EAClCE,OAAO,SAACD,SAAO;AAAA,WAAKA,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASZ;EAAM,CAAA,EACnCe,OAAO,SAACC,YAAYC,YAAU;AAAA,WAAKD,WAAWE,OAAOD,UAAU;EAAC,GAAE,CAAA,CAAE;AACzE;AAGO,SAASI,WAAWC,KAAaC,eAAmD;AACzF,SAAOA,cAAcC,KAAK,SAACC,QAAM;AAAA,WAAM,OAAOA,WAAW,WAAWH,QAAQG,SAASA,OAAOC,KAAKJ,GAAG;EAAC,CAAC;AACxG;AAKA,SAASK,uBAAuB9B,MAAcC,KAAaI,MAAc;AACvE,MAAM0B,UAAU9B,MAAM+B,gBAAgB/B,KAAKI,IAAI,IAAIA;AAcnD,MAAM4B,SAAS;AAEf,SAAOjC,KAAKD,QAAQkC,QAAQ,SAACC,IAAIC,KAAKV,KAAKW,MAAS;AAClD,QAAMC,aAAa;AACnB,QAAMC,WAAWD,WAAWR,KAAKJ,GAAG;AAGpC,QAAIa,UAAU;AACZ,aAAOJ;IACT;AAEA,WAAA,OAAAb,OAAcc,GAAG,EAAAd,OAAGW,gBAAgBP,KAAKM,OAAO,CAAC,EAAAV,OAAGe,MAAI,GAAA;EAC1D,CAAC;AACH;AAEA,IAAMG,gBAAgB;EACpB9B,WAAWqB;;EAEXU,kBAAkB,CAAC;IAAEC,SAAS;EAAqC,CAAC;AACtE;AAEO,SAASC,WAAW5C,SAAuC;AAChE,SAAO6C,MAAMC,QAAQ9C,OAAO,IAAC,CAAIyC,aAAa,EAAAlB,OAAAwB,mBAAK/C,OAAO,CAAA,IAAI,CAACyC,aAAa;AAC9E;;;;;;;;;;;;;;;;;;;;;;;;ACxDA,IAAMO,aAAa,CAAC;AACpB,IAAMC,cAAc,CAAC;AACrB,IAAMC,iBAAiB,CAAC;AAExB,IAAI,CAACC,OAAOC,OAAO;AACjBC,QAAMC,mBAAmB;AACzB,QAAM,IAAIC,MAAM;AAClB;AACA,IAAMC,eAAeL,OAAOC,MAAMK,KAAKN,MAAM;AAE7C,SAASO,mBAAmBC,KAAK;AAC/B,SAAOA;AACT;AAKA,SAAsBC,iBAAgBC,IAAAC,KAAAC,KAAA;AAAA,SAAAC,kBAAAC,MAAA,MAAAC,SAAA;AAAA;AAiBtC,SAAAF,oBAAA;AAAAA,sBAAAG,kBAAAC,mBAAAA,QAAAC,KAjBO,SAAAC,QACLC,SACAC,UACAC,wBAA6C;AAAA,QAAAC,QAAAC,kBAAAC,kBAAAC;AAAA,WAAAT,mBAAAA,QAAAU,KAAA,SAAAC,SAAAC,UAAA;AAAA,aAAA,EAAA,SAAAA,SAAAC,OAAAD,SAAAE,MAAA;QAAA,KAAA;AAEvCR,mBAASS,UAAUZ,QAAQa,aAAa;AAExCT,6BAAmBU,QAAQd,QAAQe,QAAQC,IAAI,SAACC,QAAM;AAAA,mBAAKA,OAAOC;UAAS,CAAA,CAAC;AAC5Eb,6BAAoCH,uBAAuB,EAAEc,IAAI,SAAAG,QAAA;AAAA,gBAAGC,MAAGD,OAAHC,KAAKC,SAAMF,OAANE,QAAQC,iBAAcH,OAAdG;AAAc,mBAAQ;cAC3GF;cACAC;cACAC,gBAAgBA,eAAeC,KAAK,SAACC,SAAO;AAAA,uBAAKpB,iBAAiBoB,SAASJ,KAAKjB,MAAM;cAAC,CAAA;YACzF;UAAC,CAAC;AAACM,mBAAAE,OAAA;AAAA,iBACqBc,aAAaxB,UAAUI,gBAAgB;QAAC,KAAA;AAA1DC,sBAASG,SAAAiB;AAAA,iBAAAjB,SAAAkB,OAAA,UACR3B,QAAQ4B,UAAU5B,QAAQ4B,QAAQtB,SAAS,IAAIA,SAAS;QAAA,KAAA;QAAA,KAAA;AAAA,iBAAAG,SAAAoB,KAAA;MAAA;IAAA,GAAA9B,OAAA;EAAA,CAChE,CAAA;AAAA,SAAAN,kBAAAC,MAAA,MAAAC,SAAA;AAAA;AAAA,SAMc8B,aAAYK,KAAAC,KAAA;AAAA,SAAAC,cAAAtC,MAAA,MAAAC,SAAA;AAAA;AAAA,SAAAqC,gBAAA;AAAAA,kBAAApC,kBAAAC,mBAAAA,QAAAC,KAA3B,SAAAmC,SAA4BhC,UAAUiC,iBAAgC;AAAA,QAAA5B;AAAA,WAAAT,mBAAAA,QAAAU,KAAA,SAAA4B,UAAAC,WAAA;AAAA,aAAA,EAAA,SAAAA,UAAA1B,OAAA0B,UAAAzB,MAAA;QAAA,KAAA;AAChEL,sBAAYL;AAAQ,iBAAAmC,UAAAT,OAAA,UAEjBU,QAAQC,IACbJ,gBAAgBlB,IAAI,SAACuB,aAAaC,OAAK;AAAA,mBACrCD,YAAYjB,eAAeC,KAAK,SAACC,SAAY;AAC3C,kBAAIe,YAAYnB,KAAK;AACnBd,4BAAYA,UAAUsB,QACpBa,qBAAqBF,YAAYnB,GAAG,GACpCmB,YAAYlB,SAAM,eAAAqB,OACCH,YAAYnB,KAAG,qCAAA,IAAA,aAAAsB,OACjBH,YAAYnB,KAAG,KAAA,EAAAsB,OAAMlB,SAAO,UAAA,CAC/C;cACF,WAAWA,SAAS;AAClBlB,4BAAYA,UAAUsB,QACpBe,4BAA4BH,KAAK,GAAC,0BAAAE,OACRF,OAAK,KAAA,EAAAE,OAAMlB,SAAO,UAAA,CAC9C;cACF;YACF,CAAC;UAAC,CACJ,CACF,EAAED,KAAK,WAAA;AAAA,mBAAMjB;UAAS,CAAA,CAAC;QAAA,KAAA;QAAA,KAAA;AAAA,iBAAA8B,UAAAP,KAAA;MAAA;IAAA,GAAAI,QAAA;EAAA,CACxB,CAAA;AAAA,SAAAD,cAAAtC,MAAA,MAAAC,SAAA;AAAA;AAED,IAAMiD,eAAe,SAAfA,cAAgBC,MAAI;AAAA,SAAKA,KAAKC,WAAW,GAAG;AAAC;AAEnD,IAAMC,cAAc,SAAdA,aACJ3B,KACA4B,OACAnE,OACAoE,SACAC,WAA4B;AAAA,SAE5BF,MAAM5B,GAAG,MACR4B,MAAM5B,GAAG,IAAIvC,MAAMuC,GAAG,EACpBG,KAAK,SAAC4B,UAAa;AAGlB,QAAIA,SAASC,UAAU,KAAK;AAC1BJ,YAAM5B,GAAG,IAAI;AACb,UAAI6B,SAAS;AACXnE,cAAMuE,gCAAgC;UAAEjC;UAAK+B;QAAS,CAAC;AACvDD,sBAAS,QAATA,cAAS,UAATA,UAAY9B,KAAK,IAAIpC,MAAMqE,8BAA8B,CAAC;AAC1D,eAAO;MACT,OAAO;AACLvE,cAAMwE,mCAAmC;UAAElC;UAAK+B;QAAS,CAAC;AAC1DD,sBAAS,QAATA,cAAS,UAATA,UAAY9B,KAAK,IAAIpC,MAAMsE,iCAAiC,CAAC;AAC7D,cAAM,IAAItE,MAAMsE,iCAAiC;MACnD;IACF;AACA,WAAOH,SAASI,KAAK;EACvB,CAAC,EAAC,OAAA,EACK,SAACC,GAAM;AACZR,UAAM5B,GAAG,IAAI;AACb,QAAI6B,SAAS;AACXnE,YAAMuE,gCAAgCjC,GAAG;AACzC8B,oBAAS,QAATA,cAAS,UAATA,UAAY9B,KAAKoC,CAAC;AAClB,aAAO;IACT,OAAO;AACL1E,YAAMwE,mCAAmClC,GAAG;AAC5C8B,oBAAS,QAATA,cAAS,UAATA,UAAY9B,KAAKoC,CAAC;AAClB,aAAO;IACT;EACF,CAAC;AAAE;AAGA,SAAStD,wBACduD,QAGiB;AAAA,MAFjB5E,QAAoEc,UAAA+D,SAAA,KAAA/D,UAAA,CAAA,MAAAgE,SAAAhE,UAAA,CAAA,IAAGV;AAAY,MACnFiE,YAA2BvD,UAAA+D,SAAA,IAAA/D,UAAA,CAAA,IAAAgE;AAE3B,SAAOF,OAAOzC,IAAI,SAAA4C,MAA8B;AAAA,QAA3BxC,MAAGwC,KAAHxC,KAAKI,UAAOoC,KAAPpC,SAASH,SAAMuC,KAANvC;AAEjC,QAAIG,SAAS;AACX,aAAO;QAAEJ,KAAK;QAAIE,gBAAgBe,QAAQwB,QAAQrC,OAAO;MAAE;IAC7D,WAAWoB,aAAaxB,GAAG,GAAG;AAE5B,aAAO;QAAEA,KAAK;QAAIE,gBAAgBe,QAAQwB,QAAQC,cAAc1C,GAAG,CAAC;MAAE;IACxE,OAAO;AAEL,aAAO;QACLA;QACAC;QACAC,gBAAgBD,SAASgB,QAAQwB,QAAQ,EAAE,IAAId,YAAY3B,KAAK3C,YAAYI,OAAO,MAAMqE,SAAS;MACpG;IACF;EACF,CAAC;AACH;AAGO,SAASa,oBACdC,SAIkB;AAAA,MAHlBC,QAAoEC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGG;AAAY,MACnFC,YAA2BJ,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAAA,MAC3BG,QAAcL,UAAAC,SAAA,IAAAD,UAAA,CAAA,IAAAE;AAGd,SAAOJ,QAAQQ,IAAI,SAACC,QAAW;AAC7B,QAAQC,MAAsCD,OAAtCC,KAAKC,QAAiCF,OAAjCE,OAAOC,QAA0BH,OAA1BG,OAAOC,SAAmBJ,OAAnBI,QAAQC,SAAWL,OAAXK;AACnC,QAAIC,iBAAiB;AAErB,SAAKJ,SAASC,UAAUF,OAAO,CAACG,QAAQ;AACtCE,uBAAiB,IAAIC,QAAQ,SAACC,SAASC,QAAM;AAAA,eAC3CX,QACIY,oBAAoB,WAAA;AAAA,iBAAMC,YAAYV,KAAKW,aAAapB,OAAO,OAAOK,SAAS,EAAEgB,KAAKL,SAASC,MAAM;QAAC,CAAA,IACtGE,YAAYV,KAAKW,aAAapB,OAAO,OAAOK,SAAS,EAAEgB,KAAKL,SAASC,MAAM;MAAC,CAClF;IAEF,WAAYL,UAAUH,OAAQI,QAAQ;AACpCC,uBAAiBC,QAAQC,QAAQ,EAAE;IAErC,WAAW,CAACP,KAAK;AACfK,uBAAiBC,QAAQC,QAAQR,OAAOc,OAAO;IAEjD,OAAO;AACLR,uBAAiBK,YAAYV,KAAKW,aAAapB,OAAO,OAAOK,SAAS;IACxE;AAEA,QAAIO,UAAU,CAACF,MAAOF,QAAOG,QAAQ;AACrC,WAAAY,cAAAA,cAAA,CAAA,GAAYf,MAAM,GAAA,CAAA,GAAA;MAAEM;IAAc,CAAA;EACpC,CAAC;AACH;AAEe,SAAf,WAAmCU,QAIN;AAAA,MAAAC,aAAAC;AAC3B,MAAQC,MAAoBH,OAApBG,KAAKC,OAAeJ,OAAfI,MAAMC,OAASL,OAATK;AACnB,MAAMC,SAAKL,cAAGG,KAAKE,WAAK,QAAAL,gBAAA,SAAAA,cAAIM;AAC5B,MAAMC,SAAKN,cAAGE,KAAKI,WAAK,QAAAN,gBAAA,SAAAA,cAAI;AAC5B,MAAQO,UAAuBL,KAAvBK,SAASC,YAAcN,KAAdM;AACjB,MAAMC,aAAaF,UAAUG,QAAQH,QAAQI,IAAI,SAACC,QAAM;AAAA,WAAKA,OAAOH;EAAU,CAAA,CAAC,IAAII;AACnF,MAAMC,aAAaC,iBAAiB,cAAcR,OAAO;AACzD,MAAMS,cAAcD,iBAAiB,eAAeR,OAAO;AAC3D,MAAMU,YAAYF,iBAAiB,aAAaR,OAAO;AACvD,MAAMW,aAAaH,iBAAiB,cAAcR,OAAO;AACzD,MAAMY,gBAAgBC;AAEtB,MAAMC,qBAAqB,SAArBA,oBAAsBpB,MAAKE,OAAMM,aAAU;AAAA,YAC9CN,QACGmB,QAAQC,QAAQpB,KAAI,IACpBC,MAAMH,IAAG,EACNuB,KAAK,SAACC,UAAa;AAClB,UAAIA,SAASC,UAAU,KAAK;AAC1BC,cAAMC,iCAAiC;UAAE3B,KAAAA;UAAKwB;QAAS,CAAC;AACxDjB,sBAAS,QAATA,cAAS,UAATA,UAAYP,MAAK,IAAI4B,MAAMD,+BAA+B,CAAC;AAC3D,eAAO;MACT;AACA,aAAOH,SAASK,KAAK;IACvB,CAAC,EAAC,OAAA,EACK,SAACC,GAAM;AACZC,qBAAe/B,IAAG,IAAI;AACtBO,oBAAS,QAATA,cAAS,UAATA,UAAYP,MAAK8B,CAAC;AAClB,aAAOT,QAAQW,OAAOF,CAAC;IACzB,CAAC,GACLP,KAAK,SAACrB,OAAS;AACf,UAAM+B,kBAAkBf,cAAclB,IAAG;AACzC,UAAAkC,cAAsCC,WAAW3B,YAAWN,KAAI,GAAG+B,eAAe,GAA1EG,WAAQF,YAARE,UAAUC,UAAOH,YAAPG,SAASC,SAAMJ,YAANI;AAC3B,aAAO;QACLF;QACAH;QACAM,oBAAoB,SAApBA,qBAAkB;AAAA,iBAChBA,oBACEF,QACGG,OAAO,SAACC,QAAM;AAAA,mBAAK,CAACA,OAAOC,OAAO,CAACC,WAAWF,OAAOC,KAAK7B,UAAU;UAAC,CAAA,EACrEH,IAAI,SAAC+B,QAAM;AAAA,mBAAAG,cAAAA,cAAA,CAAA,GAAWH,MAAM,GAAA,CAAA,GAAA;cAAEI,QAAQJ,OAAOC,OAAOC,WAAWF,OAAOC,KAAK1B,SAAS;YAAC,CAAA;UAAA,CAAG,GAC3Fb,OACAI,WACAF,KACF;QAAC;QACHyC,wBAAwB,SAAxBA,yBAAsB;AAAA,iBACpBA,wBACER,OACGE,OAAO,SAACO,OAAK;AAAA,mBAAK,CAACA,MAAML,OAAO,CAACC,WAAWI,MAAML,KAAK3B,WAAW;UAAC,CAAA,EACnEL,IAAI,SAACqC,OAAK;AAAA,mBAAAH,cAAAA,cAAA,CAAA,GAAWG,KAAK,GAAA,CAAA,GAAA;cAAEF,QAAQE,MAAML,OAAOC,WAAWI,MAAML,KAAKzB,UAAU;YAAC,CAAA;UAAA,CAAG,GACxFd,OACAI,SACF;QAAC;MACL;IACF,CAAC;EAAC;AAEJ,MAAIN,SAAI,QAAJA,SAAI,UAAJA,KAAMK,QAAQ0C,KAAK,SAACrC,QAAM;AAAA,WAAKA,OAAOH;EAAU,CAAA,GAAG;AACrD,WAAOY,mBAAmBpB,KAAKE,MAAMM,UAAU;EAEjD,OAAO;AACL,WAAOuB,eAAe/B,GAAG,MAAM+B,eAAe/B,GAAG,IAAIoB,mBAAmBpB,KAAKE,MAAMM,UAAU;EAC/F;AACF;;;ACxQA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,MAAI,EAAE,aAAa,GAAI,OAAM,IAAI,UAAU,mCAAmC;AAChF;;;ACDA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AACjC,QAAI,IAAI,EAAE,CAAC;AACX,MAAE,aAAa,EAAE,cAAc,OAAI,EAAE,eAAe,MAAI,WAAW,MAAM,EAAE,WAAW,OAAK,OAAO,eAAe,GAAG,cAAc,EAAE,GAAG,GAAG,CAAC;AAAA,EAC7I;AACF;AACA,SAAS,aAAa,GAAG,GAAG,GAAG;AAC7B,SAAO,KAAK,kBAAkB,EAAE,WAAW,CAAC,GAAG,KAAK,kBAAkB,GAAG,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACjH,UAAU;AAAA,EACZ,CAAC,GAAG;AACN;;;;;;ACXA,SAAS,uBAAuB,GAAG;AACjC,MAAI,WAAW,EAAG,OAAM,IAAI,eAAe,2DAA2D;AACtG,SAAO;AACT;;;ACDA,SAAS,2BAA2B,GAAG,GAAG;AACxC,MAAI,MAAM,YAAY,QAAQ,CAAC,KAAK,cAAc,OAAO,GAAI,QAAO;AACpE,MAAI,WAAW,EAAG,OAAM,IAAI,UAAU,0DAA0D;AAChG,SAAO,uBAAsB,CAAC;AAChC;;;ACNA,SAAS,gBAAgB,GAAG;AAC1B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUyC,IAAG;AAC3F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C,GAAG,gBAAgB,CAAC;AACtB;;;ACJA,SAAS,gBAAgB,GAAG,GAAG;AAC7B,SAAO,kBAAkB,OAAO,iBAAiB,OAAO,eAAe,KAAK,IAAI,SAAUC,IAAGC,IAAG;AAC9F,WAAOD,GAAE,YAAYC,IAAGD;AAAA,EAC1B,GAAG,gBAAgB,GAAG,CAAC;AACzB;;;ACHA,SAAS,UAAU,GAAG,GAAG;AACvB,MAAI,cAAc,OAAO,KAAK,SAAS,EAAG,OAAM,IAAI,UAAU,oDAAoD;AAClH,IAAE,YAAY,OAAO,OAAO,KAAK,EAAE,WAAW;AAAA,IAC5C,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC,GAAG,OAAO,eAAe,GAAG,aAAa;AAAA,IACxC,UAAU;AAAA,EACZ,CAAC,GAAG,KAAK,gBAAe,GAAG,CAAC;AAC9B;;;ACZA,SAAS,kBAAkB,GAAG;AAC5B,MAAI;AACF,WAAO,OAAO,SAAS,SAAS,KAAK,CAAC,EAAE,QAAQ,eAAe;AAAA,EACjE,SAAS,GAAG;AACV,WAAO,cAAc,OAAO;AAAA,EAC9B;AACF;;;ACNA,SAAS,4BAA4B;AACnC,MAAI;AACF,QAAI,IAAI,CAAC,QAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAAA,EACxF,SAASE,IAAG;AAAA,EAAC;AACb,UAAQ,4BAA4B,SAASC,6BAA4B;AACvE,WAAO,CAAC,CAAC;AAAA,EACX,GAAG;AACL;;;ACLA,SAAS,WAAW,GAAG,GAAG,GAAG;AAC3B,MAAI,0BAAyB,EAAG,QAAO,QAAQ,UAAU,MAAM,MAAM,SAAS;AAC9E,MAAI,IAAI,CAAC,IAAI;AACb,IAAE,KAAK,MAAM,GAAG,CAAC;AACjB,MAAI,IAAI,KAAK,EAAE,KAAK,MAAM,GAAG,CAAC,GAAG;AACjC,SAAO,KAAK,gBAAe,GAAG,EAAE,SAAS,GAAG;AAC9C;;;ACJA,SAAS,iBAAiB,GAAG;AAC3B,MAAI,IAAI,cAAc,OAAO,MAAM,oBAAI,IAAI,IAAI;AAC/C,SAAO,mBAAmB,SAASC,kBAAiBC,IAAG;AACrD,QAAI,SAASA,MAAK,CAAC,kBAAiBA,EAAC,EAAG,QAAOA;AAC/C,QAAI,cAAc,OAAOA,GAAG,OAAM,IAAI,UAAU,oDAAoD;AACpG,QAAI,WAAW,GAAG;AAChB,UAAI,EAAE,IAAIA,EAAC,EAAG,QAAO,EAAE,IAAIA,EAAC;AAC5B,QAAE,IAAIA,IAAG,OAAO;AAAA,IAClB;AACA,aAAS,UAAU;AACjB,aAAO,WAAUA,IAAG,WAAW,gBAAe,IAAI,EAAE,WAAW;AAAA,IACjE;AACA,WAAO,QAAQ,YAAY,OAAO,OAAOA,GAAE,WAAW;AAAA,MACpD,aAAa;AAAA,QACX,OAAO;AAAA,QACP,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,cAAc;AAAA,MAChB;AAAA,IACF,CAAC,GAAG,gBAAe,SAASA,EAAC;AAAA,EAC/B,GAAG,iBAAiB,CAAC;AACvB;;;;;;;;;;;;;;;;;;;;;;;;;;;ACfO,IAAMC,sBAAsBC,OAAOC,uBACtCD,OAAOE,QAAQC,OAAOC,iBACtB,oBAAIC,IAA0B;AAE3B,SAASC,aAAaC,IAA0B;AAAA,MAAAC;AACrD,WAAOA,wBAAAT,oBAAoBU,IAAIF,EAAE,OAAC,QAAAC,0BAAA,SAAA,SAA3BA,sBAA6BE,UAAS;AAC/C;AAEO,SAASC,eAAeJ,IAAiC;AAAA,MAAAK;AAC9D,WAAOA,yBAAAb,oBAAoBU,IAAIF,EAAE,OAAC,QAAAK,2BAAA,SAAA,SAA3BA,uBAA6BC,YAAW;AACjD;AAEO,SAASC,yBAAyBP,IAAYQ,SAAsB;AACzE,MAAMC,aAAajB,oBAAoBU,IAAIF,EAAE;AAC7C,MAAIS,WAAYjB,qBAAoBkB,IAAIV,IAAEW,eAAAA,eAAA,CAAA,GAAOF,UAAU,GAAA,CAAA,GAAA;IAAEN,OAAOK;EAAO,CAAA,CAAE;MACxEhB,qBAAoBkB,IAAIV,IAAI;IAAEG,OAAOK;EAAQ,CAAC;AACrD;AAEO,SAASI,gBAAgBZ,IAAY;AAC1C,MAAMS,aAAajB,oBAAoBU,IAAIF,EAAE;AAC7C,MAAIS,eAAU,QAAVA,eAAU,UAAVA,WAAYH,QAASd,qBAAoBkB,IAAIV,IAAI;IAAEM,SAASG,WAAWH;EAAQ,CAAC;AACpFd,sBAAmB,QAAA,EAAQQ,EAAE;AAC/B;AAEO,SAASa,2BAA2Bb,IAAYM,SAA6B;AAClF,MAAMG,aAAajB,oBAAoBU,IAAIF,EAAE;AAC7C,MAAIS,WAAYjB,qBAAoBkB,IAAIV,IAAEW,eAAAA,eAAA,CAAA,GAAOF,UAAU,GAAA,CAAA,GAAA;IAAEH;EAAO,CAAA,CAAE;MACjEd,qBAAoBkB,IAAIV,IAAI;IAAEM;EAAQ,CAAC;AAC9C;AAGO,IAAMQ,0BAA0B;;EAErCC,uBAAuB,CACrB,iBACA,kBACA,eACA,OACA,wBACA,gBAAgB;;EAIlBC,kBAAkB,CAChB,iBACA,kBACA,eACA,OACA,wBACA,0BACA,qBACA,kBACA,iBACA,oBACA,mBACA,oBACA,SACA,UACA,OAAO;;EAITC,kBAAkB,CAChB,iBACA,qBACA,YACA,qBACA,cACA,qBACA,oBACA,2BACA,sBACA,aAAa;;EAIfC,eAAe,CACb,UACA,YACA,gBACA,oBACA,qBACA,iBACA,iBAAiB;;EAInBC,oBAAoB,CAClB,gBACA,cACA,eACA,cACA,OACA,WACA,UACA,qBACA,UACA,kBACA,gBACA,2BACA,WACA,cACA,YACA,mBACA,OAAO;;EAITC,iBAAiB,CACf,eACA,0BACA,eACA,kBACA,wBACA,0BACA,YACA,SAAS;;EAIXC,gBAAgB,CACd,uBACA,sBACA,gBACA,eACA,iBACA,YACA,YACA,YACA,sBACA,qBACA,6BACA,oBAAoB;;EAItBC,iBAAiB,CAAC,QAAQ,MAAM;AAClC;AAGO,IAAMC,oCAAoC,CAAC,oBAAoB,kBAAkB;AACjF,IAAMC,sBAAsB,CAAC,oBAAoB;AAEjD,IAAMC,qCAAqC,CAChD,oBACA,mBACA,mBACA,oBACA,SACA,WACA,YACA,OAAO;AAIF,IAAMC,mCAAmC,CAAC,qBAAqB,oBAAoB;AAGnF,IAAMC,kCAAkC,CAC7C,cACA,YACA,oBACA,QACA,gBACA,UACA,WACA,SACA,oBAAoB;AAIf,IAAMC,mBAAmB,CAAC,UAAU,kBAAkB,YAAY,WAAW,sBAAsB;AAGnG,IAAMC,4BAA4B;EACvCC,KAAK;EACLC,GAAG;EACHC,QAAQ;AACV;AAGO,IAAMC,wBAAwB,CAAC,oBAAoB,kBAAkB,cAAc,WAAW;AAG9F,IAAMC,qBAAqB,CAChC,oBACA,4DACA,yBACA,YACA,mBACA,OAAO;AAKF,IAAMC,wBAAwBC,YAAYC,UAAUC;AACpD,IAAMC,wBAAwBH,YAAYC,UAAUG;AACpD,IAAMC,qBAAqBL,YAAYC,UAAUK;AACjD,IAAMC,sBAAsBC,gBAAgBP,UAAUQ;AACtD,IAAMC,sBAAsBC,gBAAgBV,UAAUQ;AACtD,IAAMG,sBAAsBC,KAAKZ,UAAUa;AAC3C,IAAMC,yBAAyBF,KAAKZ,UAAUe;AAC9C,IAAMC,4BAA4B5D,OAAOyD;AACzC,IAAMI,+BAA+B7D,OAAO2D;AAC5C,IAAMG,iBAAiBN,KAAKZ,UAAUC;AACtC,IAAMkB,2BAA2B/D,OAAOC,uBAC3CD,OAAOgE,wCACPC,SAASrB,UAAUsB;;;;;;;;;;;;;;;;;;;;;;;;AC1LvB,SAASC,iBACPC,GACAC,eACa;AACbC,SAAOC,iBAAiBH,GAAG;IACzBI,YAAY;MACVC,KAAKJ;IACP;IACAK,QAAQ;MACND,KAAKJ;IACP;EACF,CAAC;AAED,SAAOD;AACT;AAKA,SAASO,yBAAyBC,SAA8CC,OAAqB;AACnG,MAAMC,cAAc,IAAIC,YAAYF,KAAK;AACzC,MAAMG,eAAeb,iBAAiBW,aAAa,WAAA;AAAA,WAAMF;EAAO,CAAA;AAChE,MAAIK,WAAWL,QAAO,KAAAM,OAAML,KAAK,CAAA,CAAG,GAAG;AACrCD,YAAO,KAAAM,OAAML,KAAK,CAAA,EAAIG,YAAY;EACpC,OAAO;AACLJ,YAAQO,cAAcH,YAAY;EACpC;AACF;AAKA,SAASI,6BAA6BC,mBAA0DC,SAAgB;AAC9G,MAAI,CAACD,kBAAkBE,aAAaD,QAAQE,QAAS;AACrD,MAAMC,UAAU,SAAVA,WAAgB;AACpB,QAAAC,wBAAuDC,sBAAsB,CAACN,kBAAkBO,KAAK,CAAC,GAACC,yBAAAC,eAAAJ,uBAAA,CAAA,GAAhGK,wBAAqBF,uBAAA,CAAA,GAAEG,wBAAqBH,uBAAA,CAAA;AACnD,QAAIE,uBAAuB;AACzBT,cAAQW,WAAWC,KAAKC,YAAYJ,qBAAqB;IAC3D;AACA,QAAIC,uBAAuB;AACzBV,cAAQW,WAAWG,KAAKD,YAAYH,qBAAqB;IAC3D;AACAX,sBAAkBgB,WAAWC;EAC/B;AACA,MAAIjB,kBAAkBgB,UAAU;AAC9BE,iBAAalB,kBAAkBgB,QAAQ;EACzC;AACAhB,oBAAkBgB,WAAWG,WAAWf,SAAS,EAAE;AACrD;AAKA,SAASgB,uBACPpB,mBACAqB,WACApB,SACAqB,QACA;AAAA,MAAAC;AACA,MAAIvB,kBAAkBwB,eAAgB;AACtC,MAAMC,gBAAgBxC,OAAOyC,yBAAyBC,QAAQC,WAAW,WAAW;AACpF,MAAMC,gBAAgB5C,OAAOyC,yBAAyBI,YAAYF,WAAW,WAAW;AACxF,MAAMG,kBAAkB9C,OAAOyC,yBAAyBM,KAAKJ,WAAW,aAAa;AACrF,MAAMK,iBAAaV,wBAAGvB,kBAAkBO,WAAK,QAAAgB,0BAAA,SAAA,SAAvBA,sBAAyBW;AAE/C,WAASC,uBAAuB;AAC9B,QAAI,CAACF,cAAe;AACpBjC,sBAAkBO,MAAM2B,aAAa,SAACE,MAAcC,OAA2B;AAC7EZ,sBAAiBzB,kBAAkBE,aAAakC,OAASpC,kBAAkBsC,aAAaF;AACxF,aAAOH,cAAcM,KAAKvC,kBAAkBO,OAAO6B,MAAMC,KAAK;IAChE;EACF;AACAF,uBAAqB;AAErB,MAAIV,eAAe;AACjBxC,WAAOC,iBAAiBc,mBAAmB;MACzCE,WAAW;QACTd,KAAK,SAALA,MAAiB;AACf,iBAAOqC,cAAcrC,IAAImD,KAAKvC,iBAAiB;QACjD;QACAwC,KAAK,SAALA,IAAeC,MAAc;AAAA,cAAAC,QAAA;AAC3BjB,wBAAce,IAAID,KAAKvC,mBAAmBqB,UAAUoB,MAAM,IAAInB,MAAM,CAAC;AACrEqB,mBAAS,WAAA;AAAA,mBAAM5C,6BAA6B2C,OAAMzC,OAAO;UAAC,CAAA;QAC5D;MACF;IACF,CAAC;EACH;AAEAhB,SAAOC,iBAAiBc,mBAAmB;IACzCsC,WAAW;MACTlD,KAAK,SAALA,MAAiB;AACf,eAAOyC,cAAczC,IAAImD,KAAKvC,iBAAiB;MACjD;MACAwC,KAAK,SAALA,IAAeC,MAAc;AAAA,YAAAG,SAAA;AAC3Bf,sBAAcW,IAAID,KAAKvC,mBAAmBqB,UAAUoB,MAAM,IAAInB,MAAM,CAAC;AACrEqB,iBAAS,WAAA;AAAA,iBAAM5C,6BAA6B6C,QAAM3C,OAAO;QAAC,CAAA;MAC5D;IACF;IACA4C,aAAa;MACXzD,KAAK,SAALA,MAAiB;AACf,eAAO2C,gBAAgB3C,IAAImD,KAAKvC,iBAAiB;MACnD;MACAwC,KAAK,SAALA,IAAeC,MAAc;AAAA,YAAAK,SAAA;AAC3Bf,wBAAgBS,IAAID,KAAKvC,mBAAmBqB,UAAUoB,MAAM,IAAInB,MAAM,CAAC;AACvEqB,iBAAS,WAAA;AAAA,iBAAM5C,6BAA6B+C,QAAM7C,OAAO;QAAC,CAAA;MAC5D;IACF;IACAa,aAAa;MACXiC,OAAO,SAAPA,MAAiBC,MAAkB;AAAA,YAAAC,SAAA;AACjCN,iBAAS,WAAA;AAAA,iBAAM5C,6BAA6BkD,QAAMhD,OAAO;QAAC,CAAA;AAC1D,YAAI+C,KAAKE,aAAalB,KAAKmB,WAAW;AACpC,cAAMC,MAAMC,eAAed,KACzBvC,mBACAA,kBAAkBsD,cAAcC,eAAelC,UAAU2B,KAAKH,aAAa,IAAIvB,MAAM,CAAC,CACxF;AAEAa,+BAAqB;AACrB,iBAAOiB;QACT,MAAO,QAAOC,eAAeL,IAAI;MACnC;IACF;IACAxB,gBAAgB;MAAEpC,KAAK,SAALA,MAAG;AAAA,eAAQ;MAAI;IAAC;EACpC,CAAC;AACH;AAEA,IAAIoE,yBAAyBC,QAAQC,QAAQ;AAC7C,SAASC,2BAA2BC,MAGjC;AACD,SAAO,SAASC,0BAEdC,UACAC,UACA;AAAA,QAAAC,SAAA;AACA,QAAIzE,UAAUuE;AACd,QAAQG,6BAAwCL,KAAxCK,4BAA4BC,UAAYN,KAAZM;AACpC,QAAMjE,UAAUkE,aAAaD,OAAO;AAEpC,QAAQE,qBAA0FnE,QAA1FmE,oBAAoBC,UAAsEpE,QAAtEoE,SAASC,QAA6DrE,QAA7DqE,OAAOC,UAAsDtE,QAAtDsE,SAASC,SAA6CvE,QAA7CuE,QAAQC,aAAqCxE,QAArCwE,YAAYC,gBAAyBzE,QAAzByE,eAAeC,QAAU1E,QAAV0E;AAExF,QAAI,CAACC,eAAerF,QAAQsF,OAAO,KAAK,CAACX,SAAS;AAChD,UAAMd,MAAMa,2BAA2B1B,KAAK,MAAMhD,SAASwE,QAAQ;AACnEe,yBAAmBvF,SAASiF,OAAOO,aAAa;AAChDC,gBAAUT,SAAS,6BAA6BhF,SAASiF,OAAOO,aAAa;AAC7E,aAAO3B;IACT;AAEA,QAAM6B,iBAAiBT,OAAOU;AAC9B,QAAM5D,SAAS6D,UAAUT,aAAa;AAGtC,QAAInF,QAAQsF,SAAS;AAAA,UAAAO;AACnB,eAAAA,mBAAQ7F,QAAQsF,aAAO,QAAAO,qBAAA,SAAA,SAAfA,iBAAiBC,YAAY,GAAC;QACpC,KAAK,QAAQ;AACX,cAAAC,OAA4B/F,SAApBgG,OAAID,KAAJC,MAAMC,MAAGF,KAAHE,KAAKC,OAAIH,KAAJG;AACnB,cAAMC,YAAYF,QAAQ,gBAAgBC,SAAS,cAAcF,KAAKI,SAAS,MAAM;AAErF,cAAI,CAACD,WAAW;AACd,gBAAMtC,OAAMa,2BAA2B1B,KAAK,MAAMhD,SAASwE,QAAQ;AACnEiB,sBAAUT,SAAS,6BAA6BhF,SAASiF,OAAOO,aAAa;AAC7E,mBAAO3B;UACT;AAEA,cAAImC,QAAQ,CAACK,WAAWL,MAAMM,iBAAiB,eAAetB,OAAO,CAAC,GAAG;AACvEuB,oCACE,CAAC;cAAEC,KAAKR;cAAMS,QAAQJ,WAAWL,MAAMM,iBAAiB,cAActB,OAAO,CAAC;YAAE,CAAC,GACjFD,OACAG,WAAWwB,SACb,EAAEC,QAAQ,SAAAC,QAAA;AAAA,kBAAGJ,OAAGI,OAAHJ,KAAKC,SAAMG,OAANH,QAAQI,iBAAcD,OAAdC;AAAc,qBACtCA,eAAeC,KACb,SAACC,UAAY;AAEX,oBAAMC,WAAWC,mBAAmBjH,QAAQkH,SAAS;AACrD,oBAAIT,UAAUD,MAAK;AAYjB9B,6CAA2B1B,KAAKyB,QAAMzE,SAASwE,QAAQ;gBACzD,OAAO;AAEL,sBAAM/D,qBAAoBiF,eAAeyB,cAAc,OAAO;AAE9D,sBAAMrF,aAAYsF,aAAa;oBAAEpC;oBAASF;kBAAQ,CAAC;AACnDrE,kBAAAA,mBAAkBE,YAAYmB,WAAUiF,UAASP,MAAKzE,MAAM;AAC5D8C,qCAAmBwC,KAAK5G,kBAAiB;AACzC6G,oCAAkB7G,oBAAmBuG,QAAQ;AAC7CtC,6CAA2B1B,KAAKyB,QAAMhE,oBAAmB+D,QAAQ;AAEjEhE,+CAA6BC,oBAAmBC,OAAO;AACvDX,2CAAyBC,SAAS,MAAM;gBAC1C;AACAA,0BAAU;cACZ,GACA,WAAM;AACJD,yCAAyBC,SAAS,OAAO;AACzCA,0BAAU;cACZ,CACF;YAAC,CACH;UACF;AAEA,cAAMuH,UAAU7B,eAAe8B,cAAa,gBAAAlH,OAAiB0F,MAAI,oBAAA,CAAoB;AACrF,iBAAOtB,2BAA2B1B,KAAK,MAAMuE,SAAS/C,QAAQ;QAChE;QACA,KAAK,SAAS;AACZ,cAAM/D,oBAAsC8D;AAC5CM,6BAAmBwC,KAAK5G,iBAAiB;AACzC,cAAMsG,UAAUtG,kBAAkBE;AAClC,cAAMmB,YAAYsF,aAAa;YAAEpC;YAASF;UAAQ,CAAC;AACnDiC,sBAAYtG,kBAAkBE,YAAYmB,UAAUiF,SAAS,IAAIhF,MAAM;AACvE,cAAM8B,QAAMa,2BAA2B1B,KAAK,MAAMhD,SAASwE,QAAQ;AAEnE3C,iCAAuBpB,mBAAmBqB,WAAWpB,SAASqB,MAAM;AACpEvB,uCAA6BC,mBAAmBC,OAAO;AACvD+E,oBAAUT,SAAS,6BAA6BhF,SAASiF,OAAOO,aAAa;AAC7E,iBAAO3B;QACT;QACA,KAAK,UAAU;AACb4D,yBAAezH,OAAO;AACtB,cAAA0H,SAAyC1H,SAAjCwG,MAAGkB,OAAHlB,KAAKmB,OAAID,OAAJC,MAAMzB,QAAIwB,OAAJxB,MAAM0B,cAAWF,OAAXE;AAEzB,cAAIpB,OAAO,CAACH,WAAWG,KAAKF,iBAAiB,cAActB,OAAO,CAAC,GAAG;AACpE,gBAAM6C,aAAa,SAAbA,YAAcC,cAA+B;AAEjD,kBAAIpH,QAAQuE,WAAW,KAAM,QAAO8C,KAAKC,wBAAwB;AACjE,kBAAMC,SAAS,SAATA,UAAe;AACnBlI,yCAAyBC,SAAS,MAAM;AACxCA,0BAAU;cACZ;AACAkI,mCAAoBC,eAAAA,eAAA,CAAA,GAAML,YAAY,GAAA,CAAA,GAAA;gBAAEG;cAAM,CAAA,GAAIvH,QAAQuE,OAAOO,eAAexF,OAAO;YACzF;AACA,gBAAMoI,gBAAgB;cACpB5B;cACA6B,QAAQnC,UAAS;cACjBoC,aAAaV,gBAAgB;cAC7BW,iBAAiBX,eAAe;cAChCnB,QAAQJ,WAAWG,KAAKF,iBAAiB,aAAatB,OAAO,CAAC;cAC9DwD,OAAOvB,mBAAmBjH,QAAQkH,SAAS;YAC7C;AACAuB,gCAAmB,CAACL,aAAa,GAAGrD,OAAOG,WAAWwB,WAAWtB,KAAK,EAAEuB,QAAQ,SAACmB,cAAiB;AAChG7D,uCAAyBA,uBAAuB6C,KAAK,WAAA;AAAA,uBACnDgB,aAAajB,eAAeC,KAC1B,SAACC,UAAY;AAAA,sBAAA2B;AACX,sBAAIhI,QAAQiI,cAAc,KAAM,QAAOZ,KAAKC,wBAAwB;AACpE,sBAAMY,oBAAeF,qBAAGhI,QAAQiI,eAAS,QAAAD,uBAAA,SAAA,SAAjBA,mBAAmBG;AAC3CnI,0BAAQiI,UAAUtB,KAAK,WAAA;AAAA,2BACrBjC,QACI1E,QAAQoI,oBAAoB,WAAM;AAChCjB,iCAAUM,eAAAA,eAAA,CAAA,GAAML,YAAY,GAAA,CAAA,GAAA;wBAAEf,SAAAA;sBAAO,CAAA,CAAE;oBACzC,CAAC,IACDc,WAAUM,eAAAA,eAAA,CAAA,GAAML,YAAY,GAAA,CAAA,GAAA;sBAAEf,SAAAA;oBAAO,CAAA,CAAE;kBAAC,CAC9C;AAEA,sBAAI,CAAC6B,iBAAiBlI,SAAQiI,UAAUI,MAAM,EAAE;gBAClD,GACA,WAAM;AACJhJ,2CAAyBC,SAAS,OAAO;AACzCA,4BAAU;gBACZ,CACF;cAAC,CACH;YACF,CAAC;UACH,OAAO;AAAA,gBAAAgJ;AACL,gBAAMJ,mBAAeI,sBAAGtI,QAAQiI,eAAS,QAAAK,wBAAA,SAAA,SAAjBA,oBAAmBH;AAC3CnI,oBAAQiI,UAAUtB,KAAK,WAAA;AAAA,qBACrBjC,QACI1E,QAAQoI,oBAAoB,WAAM;AAChCZ,qCACE;kBAAE1B,KAAK;kBAAMO,SAASY;kBAAMa,OAAOvB,mBAAmBjH,QAAQkH,SAAS;gBAAE,GACzExG,QAAQuE,OAAOO,eACfxF,OACF;cACF,CAAC,IACDkI,qBACE;gBAAE1B,KAAK;gBAAMO,SAASY;gBAAMa,OAAOvB,mBAAmBjH,QAAQkH,SAAS;cAAE,GACzExG,QAAQuE,OAAOO,eACfxF,OACF;YAAC,CACP;AACA,gBAAI,CAAC4I,gBAAiBlI,SAAQiI,UAAUI,MAAM,EAAE;UAClD;AAEA,cAAMxB,WAAU7B,eAAe8B,cAAa,kBAAAlH,OAAmBkG,KAAG,oBAAA,CAAoB;AACtF,iBAAO9B,2BAA2B1B,KAAK,MAAMuE,UAAS/C,QAAQ;QAChE;QAEA,KAAK,UAAU;AAEb,cAAIxE,QAAQiJ,aAAaC,eAAe,MAAM,IAAI;AAChD,mBAAOpF,eAAed,KAAKmG,yBAAyBnG,KAAK,KAAKe,eAAe,MAAM,GAAG/D,OAAO;UAC/F;AACA,cAAM6D,QAAMa,2BAA2B1B,KAAK,MAAMhD,SAASwE,QAAQ;AACnEiB,oBAAUT,SAAS,6BAA6BhF,SAASiF,OAAOO,aAAa;AAC7E,iBAAO3B;QACT;QACA;MACF;IACF;EACF;AACF;AAEA,SAASuF,4BAA4BC,YAA+B1E,SAAiB;AACnF,MAAM2E,WAAWC,iBAAiBF,UAAU;AAC5C,MAAM3I,UAAUkE,aAAaD,OAAO;AACpC,MAAQM,SAAWvE,QAAXuE;AACR,MAAMuE,eAAevE,OAAOO,cAAciE,4BAA4BC,cAAa,UAAApJ,OACvEqJ,iBAAe,IAAA,EAAArJ,OAAKgJ,UAAQ,IAAA,CACxC;AACA,MAAIE,iBAAiB,MAAM;AACzBzB,SAAK6B,sBAAoB,WAAAtJ,OAAaqJ,iBAAe,IAAA,EAAArJ,OAAKgJ,UAAQ,KAAA,CAAK;EACzE;AACA,SAAO;IAAEE;IAAcvE;EAAO;AAChC;AAEA,SAAS4E,gBAAgBxF,MAAgF;AACvG,SAAO,SAASyF,SAASC,OAAoB;AAC3C,QAAM/J,UAAU+J;AAChB,QAAQC,sBAAgC3F,KAAhC2F,oBAAoBrF,UAAYN,KAAZM;AAC5B,QAAI3E,WAAWiK,gBAAgBjK,OAAO,GAAG;AACvC,UAAAkK,wBAAyBd,4BAA4BpJ,SAA8B2E,OAAO,GAAlF6E,eAAYU,sBAAZV;AACR,aAAOA,iBAAiB;IAC1B;AACA,WAAOQ,oBAAmBhK,OAAO;EACnC;AACF;AAEA,SAASmK,mBAAmB9F,MAAmF;AAC7G,SAAO,SAAS+F,YAAYC,OAAa;AACvC,QAAMrK,UAAUqK;AAChB,QAAQC,yBAAmCjG,KAAnCiG,uBAAuB3F,UAAYN,KAAZM;AAC/B,QAAI3E,WAAWiK,gBAAgBjK,OAAO,GAAG;AACvC,UAAAuK,yBAAiCnB,4BAA4BpJ,SAA8B2E,OAAO,GAA1F6E,eAAYe,uBAAZf,cAAcvE,SAAMsF,uBAANtF;AACtB,UAAIuE,iBAAiB,MAAM;AACzB,eAAOvE,OAAOO,cAAciE,4BAA4BW,YAAYZ,YAAY;MAClF;AACA,aAAO;IACT;AACA,WAAOc,uBAAsBtK,OAAO;EACtC;AACF;AAKA,SAASwK,mBAAmBxK,SAA4C;AACtE,MAAMyK,cAAc,oBAAIC,IAAkD;AAC1E1K,UAAQ2K,kBAAkBF;AAE1BzK,UAAQ4K,mBAAmB,SACzB1E,MACA2E,UACAC,SACG;AACH,QAAMC,YAAYN,YAAY5K,IAAIqG,IAAI,KAAK,CAAA;AAC3CuE,gBAAYxH,IAAIiD,MAAI,CAAA,EAAA5F,OAAA0K,mBAAMD,SAAS,GAAA,CAAEF,QAAQ,CAAA,CAAC;AAC9C,WAAOI,oBAAoBjI,KAAKhD,SAASkG,MAAM2E,UAAUC,OAAO;EAClE;AAEA9K,UAAQkL,sBAAsB,SAC5BhF,MACA2E,UACAC,SACG;AACH,QAAMK,gBAAgBV,YAAY5K,IAAIqG,IAAI;AAC1C,QAAMpD,QAAQqI,kBAAa,QAAbA,kBAAa,SAAA,SAAbA,cAAeC,QAAQP,QAAQ;AAC7C,QAAIM,kBAAa,QAAbA,kBAAa,UAAbA,cAAetC,UAAU/F,UAAU,IAAI;AACzCqI,oBAAcE,OAAOvI,OAAO,CAAC;IAC/B;AACA,WAAOwI,uBAAuBtI,KAAKhD,SAASkG,MAAM2E,UAAUC,OAAO;EACrE;AACF;AAKO,SAASI,oBAAoBlL,SAA4C;AAC9E,MAAMyK,cAAczK,QAAQ2K;AAC5BK,qBAAIP,YAAYc,QAAQ,CAAC,EAAE5E,QAAQ,SAAA6E,OAAuB;AAAA,QAAAC,QAAAvK,eAAAsK,OAAA,CAAA,GAArBtF,OAAIuF,MAAA,CAAA,GAAEV,YAASU,MAAA,CAAA;AAClDV,cAAUpE,QAAQ,SAACkE,UAAQ;AAAA,aAAKS,uBAAuBtI,KAAKhD,SAASkG,MAAM2E,QAAQ;IAAC,CAAA;EACtF,CAAC;AACH;AAMO,SAASa,kBAAkBC,SAA+BC,IAAYhL,SAAwB;AAEnG,MAAI,CAACA,SAAS;AACZ4J,uBAAmBmB,QAAOrK,IAAI;AAC9BkJ,uBAAmBmB,QAAOE,IAAuB;EACnD;AAEAF,EAAAA,QAAOrK,KAAKC,cAAc6C,2BAA2B;IACnDM,4BAA4BZ;IAC5Ba,SAASiH;EACX,CAAC;AACDD,EAAAA,QAAOrK,KAAKwK,eAAe1H,2BAA2B;IACpDM,4BAA4BqH;IAC5BpH,SAASiH;EACX,CAAC;AACDD,EAAAA,QAAOrK,KAAK8I,cAAcD,mBAAmB;IAC3CG,uBAAuBA,sBAAsB0B,KAAKL,QAAOrK,IAAI;IAC7DqD,SAASiH;EACX,CAAC;AACDD,EAAAA,QAAOrK,KAAKwI,WAAWD,gBAAgB;IACrCG,oBAAoBA,mBAAmBgC,KAAKL,QAAOrK,IAAI;IACvDqD,SAASiH;EACX,CAAC;AACDD,EAAAA,QAAO7B,WAAWD,gBAAgB;IAChCG,oBAAoBA,mBAAmBgC,KAAKL,OAAM;IAClDhH,SAASiH;EACX,CAAC;AACDD,EAAAA,QAAOE,KAAKtK,cAAc6C,2BAA2B;IACnDM,4BAA4BZ;IAC5Ba,SAASiH;EACX,CAAC;AACDD,EAAAA,QAAOE,KAAKC,eAAe1H,2BAA2B;IACpDM,4BAA4BuH;IAC5BtH,SAASiH;EACX,CAAC;AACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrbA,IAAMM,iBAAiB;EACrB,SAAS;AACX;AAYO,SAASC,0BAA0B;AACxC,MAAMC,iBAAiBC,OAAOD;AAC9B,MAAIA,kBAAkB,EAACA,mBAAc,QAAdA,mBAAc,UAAdA,eAAgBE,IAAI,WAAW,IAAG;AAAA,QACjDC,WAAQ,SAAAC,cAAA;AAAA,eAAAD,YAAA;AAAAE,wBAAA,MAAAF,SAAA;AAAA,eAAAG,WAAA,MAAAH,WAAAI,SAAA;MAAA;AAAAC,gBAAAL,WAAAC,YAAA;AAAA,aAAAK,aAAAN,WAAA,CAAA;QAAAO,KAAA;QAAAC,OACZ,SAAAC,oBAA0B;AACxB,cAAI,KAAKC,WAAY;AACrB,cAAMA,aAAa,KAAKC,aAAa;YAAEC,MAAM;UAAO,CAAC;AACrD,cAAMC,UAAUC,aAAa,KAAKC,aAAaC,YAAY,CAAC;AAC5DC,6BAAmBP,YAAYG,QAAQK,OAAOC,aAAa;AAC3DN,kBAAQH,aAAaA;QACvB;MAAC,GAAA;QAAAH,KAAA;QAAAC,OAED,SAAAY,uBAA6B;AAC3B,cAAMP,UAAUC,aAAa,KAAKC,aAAaC,YAAY,CAAC;AAC5DH,sBAAO,QAAPA,YAAO,UAAPA,QAASQ,QAAQ;QACnB;MAAC,CAAA,CAAA;IAAA,EAAAC,iBAZoBC,WAAW,CAAA;AAclC1B,uBAAc,QAAdA,mBAAc,UAAdA,eAAgB2B,OAAO,aAAaxB,QAAQ;EAC9C;AACF;AAEO,SAASyB,wBAAwBC,IAAyB;AAC/D,MAAMC,iBAAiB7B,OAAO8B,SAASC,cAAc,WAAW;AAChEF,iBAAeG,aAAad,cAAcU,EAAE;AAC5CC,iBAAeI,UAAUC,IAAIC,kBAAkB;AAC/C,SAAON;AACT;AAKO,SAASO,yBACdC,SACAC,mBACa;AACb,MAAMC,YAAYC,aAAaF,iBAAiB;AAChD,MAAIC,aAAa,CAACA,UAAUE,SAASJ,OAAO,GAAG;AAE7C,QAAI,CAACE,UAAUG,cAAa,OAAAC,OAAQC,mBAAiB,GAAA,CAAG,GAAG;AAEzDC,iBAAWN,SAAS;IACtB;AAEA,QAAIF,SAAS;AACXS,4BAAsBC,KAAKR,WAAWF,OAAO;IAC/C;EACF;AACA,SAAOE;AACT;AAKO,SAASS,6BACdpB,IACAqB,QAEuD;AAAA,MADvDC,eAAoC5C,UAAA6C,SAAA,KAAA7C,UAAA,CAAA,MAAA8C,SAAA9C,UAAA,CAAA,IAAG,CAAC;AAExC,MAAMc,SAASiC,sBAAsBzB,IAAIsB,YAAY;AACrD,MAAMX,YAAYH,yBAAyBhB,QAAQ6B,MAAM;AACzD,MAAMK,kBAAkBlC,OAAOC,cAAcS;AAC7CwB,kBAAgBC,KAAK;AACrBD,kBAAgBE,MAAM,wDAAwD;AAC9EF,kBAAgBG,MAAM;AACtB,SAAO;IAAErC;IAAQmB;EAAU;AAC7B;AAEA,SAGemB,4BAA2BC,IAAAC,KAAA;AAAA,SAAAC,6BAAAC,MAAA,MAAAxD,SAAA;AAAA;AA2C1C,SAAAuD,+BAAA;AAAAA,iCAAAE,kBAAAC,oBAAAA,QAAAC,KA3CA,SAAAC,QAA2CnD,SAAgBoD,MAAqB;AAAA,QAAArC,WAAAsC,SAAAC,SAAAC,eAAAC,WAAAC,kBAAAC,iBAAAC;AAAA,WAAAV,oBAAAA,QAAAW,KAAA,SAAAC,SAAAC,UAAA;AAAA,aAAA,EAAA,SAAAA,SAAAC,OAAAD,SAAAE,MAAA;QAAA,KAAA;AACxEjD,UAAAA,YAAWf,QAAQK,OAAOkC;AACxBc,oBAAoCrD,QAApCqD,SAASC,UAA2BtD,QAA3BsD,SAASC,gBAAkBvD,QAAlBuD;AACpBC,sBAAYS,aAAa;YAAEZ;YAASC;UAAQ,CAAC;AAC7CG,6BAAmBS,iBAAiB,oBAAoBb,OAAO;AAC/DK,4BAAkBQ,iBAAiB,mBAAmBb,OAAO;AAC7DM,mBAASQ,UAAUZ,aAAa;AAACO,mBAAAE,OAAA;AAAA,iBAE1BI,QAAQC,IAAI,CACvBD,QAAQC,IACNC,wBAAuBb,kBAAkBzD,QAAQuE,OAAOvE,QAAQwE,WAAWC,SAAS,EAAEC,IACpF,SAAAC,MAAA;AAAA,gBAAGC,MAAGD,KAAHC,KAAKC,iBAAcF,KAAdE;AAAc,mBAAOA,eAAeC,KAAK,SAACC,SAAO;AAAA,qBAAM;gBAAEH;gBAAKG;cAAQ;YAAC,CAAC;UAAC,CACnF,CACF,EAAED,KAAK,SAACE,aAAgB;AACtBA,wBAAYC,QAAQ,SAAAC,QAAsB;AAAA,kBAAnBN,MAAGM,OAAHN,KAAKG,UAAOG,OAAPH;AAC1B,kBAAI,CAACA,QAAS;AACd,kBAAMI,eAAepE,UAASC,cAAc,OAAO;AACnDmE,2BAAalE,aAAa,QAAQ,UAAU;AAC5CkE,2BAAaC,YAAYrE,UAASsE,eAAeN,UAAUvB,UAAUuB,SAASH,KAAKjB,MAAM,IAAIoB,OAAO,CAAC;AACrG,kBAAMO,OAAOlC,KAAKzB,cAAc,MAAM;AACtC,kBAAM4D,OAAOnC,KAAKzB,cAAc,MAAM;AACtCyB,mBAAKoC,aAAaL,cAAcG,QAAQC,QAAQnC,KAAKqC,UAAU;YACjE,CAAC;UACH,CAAC,GACDrB,QAAQC,IACNC,wBAAuBZ,iBAAiB1D,QAAQuE,OAAOvE,QAAQwE,WAAWC,SAAS,EAAEC,IACnF,SAAAgB,QAAA;AAAA,gBAAGd,MAAGc,OAAHd,KAAKC,iBAAca,OAAdb;AAAc,mBAAOA,eAAeC,KAAK,SAACC,SAAO;AAAA,qBAAM;gBAAEH;gBAAKG;cAAQ;YAAC,CAAC;UAAC,CACnF,CACF,EAAED,KAAK,SAACE,aAAgB;AACtBA,wBAAYC,QAAQ,SAAAU,OAAsB;AAAA,kBAAnBf,MAAGe,MAAHf,KAAKG,UAAOY,MAAPZ;AAC1B,kBAAI,CAACA,QAAS;AACd,kBAAMI,eAAepE,UAASC,cAAc,OAAO;AACnDmE,2BAAalE,aAAa,QAAQ,UAAU;AAC5CkE,2BAAaC,YAAYrE,UAASsE,eAAeN,UAAUvB,UAAUuB,SAASH,KAAKjB,MAAM,IAAIoB,OAAO,CAAC;AACrG3B,mBAAKgC,YAAYD,YAAY;YAC/B,CAAC;UACH,CAAC,CAAC,CACH,EAAEL,KACD,WAAA;AAAA,mBAAM1B;UAAI,GACV,WAAA;AAAA,mBAAMA;UAAI,CACZ;QAAC,KAAA;AAAA,iBAAAU,SAAA8B,OAAA,UAAA9B,SAAA+B,IAAA;QAAA,KAAA;QAAA,KAAA;AAAA,iBAAA/B,SAAAgC,KAAA;MAAA;IAAA,GAAA3C,OAAA;EAAA,CACF,CAAA;AAAA,SAAAL,6BAAAC,MAAA,MAAAxD,SAAA;AAAA;AAGD,SAASwG,mBAAmB3C,MAAuBkC,MAAuBC,MAAwC;AAChH,MAAMS,cAAc5C,KAAKzB,cAAc,MAAM;AAC7C,MAAMsE,cAAc7C,KAAKzB,cAAc,MAAM;AAC7C,MAAIqE,aAAa;AACf,WAAOA,YAAYP,YAAY;AAC7BS,qBAAelE,KAAKsD,MAAMU,YAAYP,WAAWU,UAAU,IAAI,CAAC;AAChEH,kBAAYI,YAAYJ,YAAYP,UAAU;IAChD;AACAO,gBAAYK,WAAWC,aAAahB,MAAMU,WAAW;EACvD;AACA,MAAIC,aAAa;AACf,WAAOA,YAAYR,YAAY;AAC7BS,qBAAelE,KAAKuD,MAAMU,YAAYR,WAAWU,UAAU,IAAI,CAAC;AAChEF,kBAAYG,YAAYH,YAAYR,UAAU;IAChD;AACAQ,gBAAYI,WAAWC,aAAaf,MAAMU,WAAW;EACvD;AACA,SAAO7C;AACT;AAKA,SAASmD,qBAAqBC,cAAsBC,UAAmC;AACrF,MAAMzG,UAAUwG,aAAaE;AAC7B,MAAQpB,OAAgCtF,QAAhCsF,MAAMC,OAA0BvF,QAA1BuF,MAAMoB,QAAoB3G,QAApB2G,OAAOC,WAAa5G,QAAb4G;AAC3B,MAAM7F,YAAWyF,aAAazF;AAC9B,MAAM8F,SAAS,IAAIC,UAAU;AAC7B,MAAMC,iBAAiBF,OAAOG,gBAAgBP,UAAU,WAAW;AAGnE,MAAMQ,aAAaF,eAAeG;AAClC,MAAMC,mBAAmBF,WAAWG;AACpC,MAAIhE,OAAOrC,UAASC,cAAc,MAAM;AACxCoC,OAAKiE,YAAYZ;AACjB,WAASa,IAAI,GAAGA,IAAIH,iBAAiB/E,QAAQkF,KAAK;AAChDlE,SAAKnC,aAAakG,iBAAiBG,CAAC,EAAEC,MAAMJ,iBAAiBG,CAAC,EAAE3H,KAAK;EACvE;AAEA,MAAI,CAACgH,SAASC,UAAU;AACtBxD,WAAO2C,mBAAmB3C,MAAMkC,MAAMC,IAAI;EAC5C,OAAO;AACLvF,YAAQsF,OAAOlC,KAAKzB,cAAc,MAAM;AACxC3B,YAAQuF,OAAOnC,KAAKzB,cAAc,MAAM;EAC1C;AACA,MAAM6F,kBAAkBzG,UAAS0G,iBAAiBrE,MAAMsE,WAAWC,cAAc,MAAM,KAAK;AAC5F,MAAIC,cAAcJ,gBAAgBK;AAClC,SAAOD,aAAa;AAClBxH,uBAAmBwH,aAAapB,YAAY;AAC5C,QAAMsB,eAAeC,0BAA0BH,YAAYI,OAAO;AAClE,QAAMC,MAAML,YAAYE,YAAY;AACpC,QAAIA,aAAcF,aAAY3G,aAAa6G,cAAcI,gBAAgBD,KAAKL,YAAYO,WAAW,EAAE,CAAC;AACxGP,kBAAcJ,gBAAgBY,SAAS;EACzC;AACA,MAAI,CAAChF,KAAKzB,cAAc,MAAM,GAAG;AAC/B,QAAM2D,QAAOvE,UAASC,cAAc,MAAM;AAC1CoC,SAAKgC,YAAYE,KAAI;EACvB;AACA,MAAI,CAAClC,KAAKzB,cAAc,MAAM,GAAG;AAC/B,QAAM4D,QAAOxE,UAASC,cAAc,MAAM;AAC1CoC,SAAKgC,YAAYG,KAAI;EACvB;AACA,SAAOnC;AACT;AAKA,SAAsBiF,2BAA0BC,KAAAC,KAAAC,KAAA;AAAA,SAAAC,4BAAA1F,MAAA,MAAAxD,SAAA;AAAA;AAwB/C,SAAAkJ,8BAAA;AAAAA,gCAAAzF,kBAAAC,oBAAAA,QAAAC,KAxBM,SAAAwF,SACL7I,YACA2G,cACAC,UAAgB;AAAA,QAAArD,MAAAuF,eAAAC;AAAA,WAAA3F,oBAAAA,QAAAW,KAAA,SAAAiF,UAAAC,WAAA;AAAA,aAAA,EAAA,SAAAA,UAAA/E,OAAA+E,UAAA9E,MAAA;QAAA,KAAA;AAEVZ,iBAAOmD,qBAAqBC,cAAcC,QAAQ;AACxDqC,oBAAA9E,OAAA;AAAA,iBAC4BrB,4BAA4B6D,aAAaE,SAAStD,IAAI;QAAC,KAAA;AAA7EuF,0BAAaG,UAAAjD;AAEnBhG,qBAAWuF,YAAYuD,aAAa;AAC9BC,kBAAQ7H,SAASC,cAAc,KAAK;AAC1C4H,gBAAM3H,aAAa,SAAS8H,iBAAiB;AAC7CJ,wBAAcnD,aAAaoD,OAAOD,cAAclD,UAAU;AAC1D5F,qBAAWyF,OAAOzF,WAAW8B,cAAc,MAAM;AACjD9B,qBAAW0F,OAAO1F,WAAW8B,cAAc,MAAM;AAGjDqH,iBAAOC,eAAepJ,WAAW4F,YAAY,cAAc;YACzDyD,YAAY;YACZC,cAAc;YACdjK,KAAK,SAALA,MAAG;AAAA,qBAAQsH,aAAazF;YAAQ;UAClC,CAAC;AAEDqI,4BAAkBvJ,YAAY2G,aAAaE,QAAQ7F,IAAI,KAAK;QAAE,KAAA;QAAA,KAAA;AAAA,iBAAAiI,UAAAhD,KAAA;MAAA;IAAA,GAAA4C,QAAA;EAAA,CAC/D,CAAA;AAAA,SAAAD,4BAAA1F,MAAA,MAAAxD,SAAA;AAAA;AAEM,SAAS+C,sBAAsBzB,IAA0E;AAAA,MAA9DsB,eAAoC5C,UAAA6C,SAAA,KAAA7C,UAAA,CAAA,MAAA8C,SAAA9C,UAAA,CAAA,IAAG,CAAC;AACxF,MAAMc,SAASU,SAASC,cAAc,QAAQ;AAC9C,MAAMqI,eAAe;AACrBC,oBAAkBjJ,QAAMkJ,eAAAA,eAAA,CAAA,GACnBpH,YAAY,GAAA,CAAA,GAAAqH,gBAAA;IACfC,OAAO,CAACJ,cAAclH,aAAasH,KAAK,EAAEC,KAAK,GAAG;EAAC,GAClDvJ,cAAeU,EAAE,CAAA,CACnB;AACD,SAAOR;AACT;AAKA,SAAsBsJ,uBAAsBC,KAAAC,KAAAC,KAAA;AAAA,SAAAC,wBAAAhH,MAAA,MAAAxD,SAAA;AAAA;AAqB5C,SAAAwK,0BAAA;AAAAA,4BAAA/G,kBAAAC,oBAAAA,QAAAC,KArBO,SAAA8G,SACLC,gBACAzD,cACAC,UAAgB;AAAA,QAAArD,MAAAuF;AAAA,WAAA1F,oBAAAA,QAAAW,KAAA,SAAAsG,UAAAC,WAAA;AAAA,aAAA,EAAA,SAAAA,UAAApG,OAAAoG,UAAAnG,MAAA;QAAA,KAAA;AAGVZ,iBAAOmD,qBAAqBC,cAAcC,QAAQ;AACxD0D,oBAAAnG,OAAA;AAAA,iBAC4BrB,4BAA4B6D,aAAaE,SAAStD,IAAI;QAAC,KAAA;AAA7EuF,0BAAawB,UAAAtE;AACnBoE,yBAAe3D,aAAaqC,eAAesB,eAAe/C,eAAe;AAGzE8B,iBAAOC,eAAegB,eAAe/C,iBAAiB,cAAc;YAClEgC,YAAY;YACZC,cAAc;YACdjK,KAAK,SAALA,MAAG;AAAA,qBAAQsH,aAAazF;YAAQ;UAClC,CAAC;AAEDqI,4BAAkBa,gBAAgBzD,aAAaE,QAAQ7F,IAAI,IAAI;QAAE,KAAA;QAAA,KAAA;AAAA,iBAAAsJ,UAAArE,KAAA;MAAA;IAAA,GAAAkE,QAAA;EAAA,CAClE,CAAA;AAAA,SAAAD,wBAAAhH,MAAA,MAAAxD,SAAA;AAAA;AAKM,SAASuC,WAAWsI,MAA+B;AAExD,SAAOA,SAAI,QAAJA,SAAI,UAAJA,KAAM3E,YAAY;AACvB4E,0BAAsBrI,KAAKoI,MAAMA,KAAK3E,UAAU;EAClD;AACF;AAKO,SAAS6E,WAAWC,IAA0BC,SAA4B;AAC/E,MAAMhJ,YAAYC,aAAa8I,EAAE;AACjCzI,aAAWN,SAAS;AAEpB,MAAIiJ,kBAAkB;AACtB,MAAI;AACFA,sBAAkBxL,OAAOyL,iBAAiBlJ,SAAS;EACrD,SAAEmJ,SAAM;AACN;EACF;AACA,MAAIF,gBAAgBG,aAAa,UAAU;AACzCpJ,cAAUP,aAAa4J,8BAA8BJ,gBAAgBG,QAAQ;AAC7EpJ,cAAUP,aACR6J,8BACAL,gBAAgBM,aAAa,YAAY,KAAKN,gBAAgBM,QAChE;AACAvJ,cAAUiI,MAAMuB,YAAY,YAAY,UAAU;AAClDxJ,cAAUiI,MAAMuB,YAAY,YAAY,QAAQ;EAClD,WAAW,CAAC,YAAY,QAAQ,EAAEC,SAASR,gBAAgBG,QAAQ,GAAG;AACpEpJ,cAAUP,aACR6J,8BACAL,gBAAgBM,aAAa,YAAY,KAAKN,gBAAgBM,QAChE;AACAvJ,cAAUiI,MAAMuB,YAAY,YAAY,QAAQ;EAClD;AACA,MAAME,mBAAmBnK,SAASC,cAAc,KAAK;AACrDkK,mBAAiBjK,aAAaY,mBAAmB,EAAE;AACnDqJ,mBAAiBjK,aAAa,SAASkK,mBAAmB;AAC1D,MAAIX,QAASU,kBAAiB9F,YAAYoF,OAAO;MAC5CU,kBAAiB7D,YAAY+D;AAClC5J,YAAU4D,YAAY8F,gBAAgB;AACxC;AAIO,SAASG,cAAcd,IAAuB;AAEnD,MAAMe,eAAef,GAAGrK,aAAa2K,4BAA4B;AACjE,MAAMU,eAAehB,GAAGrK,aAAa4K,4BAA4B;AACjE,MAAIQ,aAAcf,IAAGd,MAAM+B,eAAe,UAAU;AACpD,MAAID,iBAAiB,MAAM;AACzBA,mBAAehB,GAAGd,MAAMuB,YAAY,YAAYO,YAAY,IAAIhB,GAAGd,MAAM+B,eAAe,UAAU;EACpG;AACAjB,KAAGkB,gBAAgBZ,4BAA4B;AAC/CN,KAAGkB,gBAAgBX,4BAA4B;AAC/C,MAAMI,mBAAmBX,GAAG5I,cAAa,OAAAC,OAAQC,mBAAiB,GAAA,CAAG;AACrEqJ,sBAAoBX,GAAGnE,YAAY8E,gBAAgB;AACrD;AAKO,SAASQ,sBAAsBC,iBAAuE;AAC3G,MAAMC,eAAe,CAAA;AACrB,MAAMC,eAAe,CAAA;AACrB,MAAMC,eAAe;AAGrB,WAASxE,IAAI,GAAGA,IAAIqE,gBAAgBvJ,QAAQkF,KAAK;AAAA,QAAAyE,uBAAAC;AAC/C,QAAMC,YAAQF,yBAAAC,qBAAGL,gBAAgBrE,CAAC,OAAC,QAAA0E,uBAAA,SAAA,SAAlBA,mBAAoBC,cAAQ,QAAAF,0BAAA,SAAAA,wBAAI,CAAA;AACjD,aAASG,IAAI,GAAGA,IAAID,SAAS7J,QAAQ8J,KAAK;AACxC,UAAMC,cAAcF,SAASC,CAAC,EAAEE;AAEhC,UAAIN,aAAaO,KAAKF,WAAW,GAAG;AAClCP,qBAAaU,KAAKH,YAAY7I,QAAQwI,cAAc,SAACS,OAAK;AAAA,iBAAKzN,eAAeyN,KAAK;QAAC,CAAA,CAAC;MACvF;AAEA,UAAIN,SAASC,CAAC,EAAEM,SAASC,QAAQC,gBAAgB;AAC/Cb,qBAAaS,KAAKH,WAAW;MAC/B;IACF;EACF;AAEA,MAAIQ,wBAAwB;AAC5B,MAAIC,wBAAwB;AAG5B,MAAIhB,aAAaxJ,QAAQ;AACvBuK,4BAAwB1N,OAAO8B,SAASC,cAAc,OAAO;AAC7D2L,0BAAsBtF,YAAYuE,aAAalC,KAAK,EAAE;EACxD;AAEA,MAAImC,aAAazJ,QAAQ;AACvBwK,4BAAwB3N,OAAO8B,SAASC,cAAc,OAAO;AAC7D4L,0BAAsBvF,YAAYwE,aAAanC,KAAK,EAAE;EACxD;AAEA,SAAO,CAACiD,uBAAuBC,qBAAqB;AACtD;;;ACtXO,SAASC,gBAAgBC,cAA4B;AAC1D,MAAAC,wBAA6BD,aAAaE,SAAlCC,OAAIF,sBAAJE,MAAMC,KAAEH,sBAAFG,IAAIC,SAAMJ,sBAANI;AAClB,MAAIC,gBAAgBC,uBAAuBC,OAAOC,SAASC,IAAI;AAC/D,MAAMC,WAAWC,yBAAyBN,aAAa;AAEvD,MAAI,CAACH,QAAQ,CAACQ,SAASP,EAAE,EAAG,QAAQE,gBAAgB;AACpD,MAAMO,SAASb,aAAaS,SAASK,WAAWd,aAAaS,SAASM,SAASf,aAAaS,SAASO;AACrG,MAAIC,iBAAiB;AAErB,MAAIZ,QAAQ;AACVa,WAAOC,KAAKd,MAAM,EAAEe,QAAQ,SAACC,WAAc;AACzC,UAAMC,WAAWjB,OAAOgB,SAAS;AAEjC,UAAIR,OAAOU,WAAWD,QAAQ,MAAM,CAACL,kBAAkBK,SAASE,SAASnB,OAAOY,cAAc,EAAEO,SAAS;AACvGP,yBAAiBI;MACnB;IACF,CAAC;EACH;AAEA,MAAIlB,MAAM;AACRQ,aAASP,EAAE,IAAII,OAAOiB,mBACpBR,iBAAiBJ,OAAOa,QAAQrB,OAAOY,cAAc,GAAC,IAAAU,OAAMV,gBAAc,GAAA,CAAG,IAAIJ,MACnF;EAEF,OAAO;AACL,WAAOF,SAASP,EAAE;EACpB;AACA,MAAMwB,WACJ,MACAV,OAAOC,KAAKR,QAAQ,EACjBkB,IAAI,SAACC,KAAG;AAAA,WAAKA,MAAM,MAAMnB,SAASmB,GAAG;EAAC,CAAA,EACtCC,KAAK,GAAG;AACbzB,gBAAcS,SAASa;AACvB,MAAItB,cAAcI,SAASF,OAAOC,SAASC,MAAM;AAC/CF,WAAOwB,QAAQC,aAAa,MAAM,IAAI3B,cAAcI,IAAI;EAC1D;AACAJ,kBAAgB;AAClB;AAKO,SAAS4B,gBAAgBlC,cAA4B;AAE1D,MAAAmC,wBAAmCnC,aAAaS,UAAxCK,WAAQqB,sBAARrB,UAAUC,SAAMoB,sBAANpB,QAAQC,OAAImB,sBAAJnB;AAC1B,MAAAoB,yBAAoDpC,aAAaE,SAAzDE,KAAEgC,uBAAFhC,IAAIiC,MAAGD,uBAAHC,KAAKlC,OAAIiC,uBAAJjC,MAAMmC,WAAQF,uBAARE,UAAUjC,SAAM+B,uBAAN/B,QAAQkC,SAAMH,uBAANG;AAGzC,MAAMC,QAAQrC,QAAQ,CAACmC,WAAWG,WAAWrC,IAAIC,MAAM,IAAIgC;AAE3D,MAAMK,WAAW,QAAQC,KAAKH,KAAK,IAAI,OAAOA,UAAUH;AACxD,MAAAO,iBAAyBC,cAAcH,OAAO,GAAtCI,eAAYF,eAAZE;AAER,MAAMC,kBAAkBjC,WAAWC,SAASC;AAC5C,MAAI+B,oBAAoBD,cAAc;AACpC9C,iBAAagC,QAAQC,aAAa,MAAM,IAAIM,OAAOS,eAAeF,YAAY;EAChF;AACF;AAMO,SAASG,sBAA4B;AAC1C,MAAI3C,gBAAgBC,uBAAuBC,OAAOC,SAASC,IAAI;AAC/D,MAAMC,WAAWC,yBAAyBN,aAAa;AACvDY,SAAOC,KAAKR,QAAQ,EAAES,QAAQ,SAAChB,IAAO;AACpC,QAAM8C,UAAUC,aAAa/C,EAAE;AAC/B,QAAI,CAAC8C,QAAS;AAEd,QAAIA,QAAQZ,YAAYY,QAAQ/C,QAAQ,CAAC+C,QAAQE,YAAY,CAACF,QAAQG,YAAY;AAChF,aAAO1C,SAASP,EAAE;IACpB;EACF,CAAC;AACD,MAAMwB,WACJ,MACAV,OAAOC,KAAKR,QAAQ,EACjBkB,IAAI,SAACC,KAAG;AAAA,WAAKA,MAAM,MAAMnB,SAASmB,GAAG;EAAC,CAAA,EACtCC,KAAK,GAAG;AACbzB,gBAAcS,SAASa;AACvB,MAAItB,cAAcI,SAASF,OAAOC,SAASC,MAAM;AAC/CF,WAAOwB,QAAQC,aAAa,MAAM,IAAI3B,cAAcI,IAAI;EAC1D;AACAJ,kBAAgB;AAClB;AAKO,SAASgD,gBAAgBlD,IAAYiC,KAAmB;AAC7D,MAAI/B,gBAAgBC,uBAAuBC,OAAOC,SAASC,IAAI;AAC/D,MAAMC,WAAWC,yBAAyBN,aAAa;AACvDK,WAASP,EAAE,IAAII,OAAOiB,mBAAmBY,GAAG;AAC5C,MAAMT,WACJ,MACAV,OAAOC,KAAKR,QAAQ,EACjBkB,IAAI,SAACC,KAAG;AAAA,WAAKA,MAAM,MAAMnB,SAASmB,GAAG;EAAC,CAAA,EACtCC,KAAK,GAAG;AACbzB,gBAAcS,SAASa;AACvBpB,SAAOwB,QAAQuB,UAAU,MAAM,IAAIjD,cAAcI,IAAI;AACrDJ,kBAAgB;AAClB;AAKO,SAASkD,wBAA8B;AAC5ChD,SAAOiD,iBAAiB,YAAY,WAAM;AACxC,QAAInD,gBAAgBC,uBAAuBC,OAAOC,SAASC,IAAI;AAC/D,QAAMC,WAAWC,yBAAyBN,aAAa;AACvDA,oBAAgB;AAChBY,WAAOC,KAAKR,QAAQ,EACjBkB,IAAI,SAACzB,IAAE;AAAA,aAAK+C,aAAa/C,EAAE;IAAC,CAAA,EAC5BsD,OAAO,SAACR,SAAO;AAAA,aAAKA;IAAO,CAAA,EAC3B9B,QAAQ,SAAC8B,SAAY;AACpB,UAAMb,MAAM1B,SAASuC,QAAQ9C,EAAE;AAC/B,UAAMuD,aAAaC,yBAAyBC,KAAKX,QAAQY,OAAOC,iBAAiB,MAAM;AAEvF,UAAI,OAAOpB,KAAKN,GAAG,GAAG;AACpB,YAAIa,QAAQc,SAAS;AACnBC,mCAAyBf,QAAQgB,SAASC,iBAAiBR,UAAU;AACrES,iCACE5D,OAAO6D,mBAAmBhC,GAAG,GAC7BiC,iBAAiBpB,QAAQ9C,EAAE,EAAEmE,eAC7BrB,QAAQsB,YACV;QACF,MACEJ,wBACE5D,OAAO6D,mBAAmBhC,GAAG,GAC7Ba,QAAQuB,WAAWC,KAAKH,eACxBrB,QAAQsB,YACV;AACFtB,gBAAQE,WAAW;MAErB,WAAWF,QAAQE,UAAU;AAC3B,YAAIF,QAAQc,SAAS;AAEnB,cAAAW,wBAAmBC,6BAA6B1B,QAAQ9C,IAAI8C,QAAQ2B,IAAI3B,QAAQsB,YAAY,GAApFV,SAAMa,sBAANb;AACRgB,8BAAoBhB,OAAOiB,eAAe7B,QAAQY,OAAOiB,aAAa;AACtEjB,iBAAOiB,cAAcC,WAAW,WAAM;AACpC9B,oBAAQ+B,QAAQ;UAClB;AACAnB,iBAAOC,gBAAgBmB,YAAYvB,WAAWwB,iBAAiB;AAC/DjC,kBAAQgB,WAAWJ,OAAOC;QAC5B,MAAOE,0BAAyBf,QAAQuB,WAAWC,MAAMxB,QAAQ2B,EAAE;AACnE3B,gBAAQE,WAAW;MACrB;IACF,CAAC;EACL,CAAC;AACH;;;;;;;;;;;;;;;;;;;;;;;;AC1CA,SAASgC,kBAAkBC,cAAsB;AAC/CA,eAAaC,0BAA0BD,aAAaC,2BAA2B,oBAAIC,IAAI;AACvFF,eAAaG,mBAAmB,SAASA,iBACvCC,MACAC,UACAC,SACA;AAEAC,cAAUP,aAAaQ,QAAQC,SAAS,8BAA8BT,cAAcI,MAAMC,UAAUC,OAAO;AAE3GN,iBAAaC,wBAAwBS,IAAI;MAAEN;MAAMC;MAAUC;IAAQ,CAAC;AACpE,QAAIK,gCAAgCC,SAASR,IAAI,KAAMS,QAAOP,OAAO,MAAK,YAAYA,QAAQQ,cAAe;AAC3G,UAAMA,eAAeD,QAAOP,OAAO,MAAK,YAAYA,QAAQQ,eAAeR,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASQ,eAAed;AACnG,aAAOe,0BAA0BC,KAAKF,cAAcV,MAAMC,UAAUC,OAAO;IAC7E;AAEAS,8BAA0BC,KAAKC,OAAOC,wBAAwBD,QAAQb,MAAMC,UAAUC,OAAO;EAC/F;AAEAN,eAAamB,sBAAsB,SAASA,qBAC1Cf,MACAC,UACAC,SACA;AAEAC,cAAUP,aAAaQ,QAAQC,SAAS,iCAAiCT,cAAcI,MAAMC,UAAUC,OAAO;AAC9GN,iBAAaC,wBAAwBmB,QAAQ,SAACC,GAAM;AAElD,UAAIA,EAAEhB,aAAaA,YAAYgB,EAAEjB,SAASA,QAAQE,WAAWe,EAAEf,SAAS;AACtEN,qBAAaC,wBAAuB,QAAA,EAAQoB,CAAC;MAC/C;IACF,CAAC;AACD,QAAIV,gCAAgCC,SAASR,IAAI,KAAMS,QAAOP,OAAO,MAAK,YAAYA,QAAQQ,cAAe;AAC3G,UAAMA,eAAeD,QAAOP,OAAO,MAAK,YAAYA,QAAQQ,eAAeR,YAAO,QAAPA,YAAO,SAAA,SAAPA,QAASQ,eAAed;AACnG,aAAOsB,6BAA6BN,KAAKF,cAAcV,MAAMC,UAAUC,OAAO;IAChF;AACAgB,iCAA6BN,KAAKC,OAAOC,wBAAwBD,QAAQb,MAAMC,UAAUC,OAAO;EAClG;AACF;AAEA,SAASiB,oBAAoBvB,cAAsBwB,OAAcC,aAA2B;AAC1FzB,eAAaQ,UAAUgB;AACvBxB,eAAa0B,wBAAwBD,cAAc;AACnDzB,eAAa2B,SAASH,MAAMI;AAC5B5B,eAAakB,uBAAuBlB;AACtC;AAUA,SAAS6B,mBAAmB7B,cAAsByB,aAAqBK,cAA4B;AACjG,MAAMC,UAAU/B,aAAa+B;AAC7B,MAAMC,sBAAsBD,QAAQE;AACpC,MAAMC,yBAAyBH,QAAQI;AACvCJ,UAAQE,YAAY,SAAUG,OAAWC,OAAeC,KAAoB;AAC1E,QAAMC,UACJT,eAAe9B,aAAawC,SAASC,WAAWzC,aAAawC,SAASE,SAAS1C,aAAawC,SAASG;AACvG,QAAMC,UAAUC,gBAAgBP,QAAG,QAAHA,QAAG,SAAA,SAAHA,IAAKQ,QAAQrB,aAAa,EAAE,GAAGc,OAAO;AACtE,QAAMQ,aAAaT,QAAQU;AAE3BhB,wBAAoBhB,KAAKe,SAASK,OAAMC,OAAOU,aAAaC,SAAYJ,OAAO;AAC/E,QAAIG,WAAY;AAChBE,eAAWjD,cAAcyB,aAAaK,YAAY;AAClDoB,oBAAgBlD,YAAY;EAC9B;AACA+B,UAAQI,eAAe,SAAUC,OAAWC,OAAeC,KAAoB;AAC7E,QAAMC,UACJT,eAAe9B,aAAawC,SAASC,WAAWzC,aAAawC,SAASE,SAAS1C,aAAawC,SAASG;AACvG,QAAMC,UAAUC,gBAAgBP,QAAG,QAAHA,QAAG,SAAA,SAAHA,IAAKQ,QAAQrB,aAAa,EAAE,GAAGc,OAAO;AACtE,QAAMQ,aAAaT,QAAQU;AAE3Bd,2BAAuBlB,KAAKe,SAASK,OAAMC,OAAOU,aAAaC,SAAYJ,OAAO;AAClF,QAAIG,WAAY;AAChBE,eAAWjD,cAAcyB,aAAaK,YAAY;AAClDoB,oBAAgBlD,YAAY;EAC9B;AACF;AAQA,SAASiD,WAAWjD,cAAsByB,aAAqBK,cAAsB;AAAA,MAAAqB;AACnF,MAAMZ,UAAU,IAAIa,KAAGD,wBAACnD,aAAawC,SAASa,UAAI,QAAAF,0BAAA,SAAA,SAA1BA,sBAA4BL,QAAQhB,cAAc,EAAE,GAAGL,WAAW;AAC1F,MAAM6B,cAAcC,yBAAyBvC,KAAKhB,aAAawD,UAAU,MAAM;AAC/E,MAAIF,YAAaA,aAAYG,aAAa,QAAQhC,cAAcc,QAAQE,QAAQ;AAClF;AAOA,SAASiB,kBAAkB1D,cAA4B;AAErD,WAAS2D,sBAAsBC,KAAsB;AACnD,QAAMC,QAAQ7D,aAAa4D,GAAG;AAC9B,QAAI;AACF,UAAI,OAAOC,UAAU,cAAc,CAACC,gBAAgBD,KAAK,GAAG;AAC1D7D,qBAAa4D,GAAG,IAAI3C,OAAO2C,GAAG,EAAEG,KAAK9C,MAAM;MAC7C,OAAO;AACLjB,qBAAa4D,GAAG,IAAI3C,OAAO2C,GAAG;MAChC;AACA,aAAO;IACT,SAASI,GAAG;AACVC,WAAKD,EAAEE,OAAO;AACd,aAAO;IACT;EACF;AACAC,SAAOC,oBAAoBpE,YAAY,EAAEoB,QAAQ,SAACwC,KAAQ;AAExD,QAAIA,QAAQ,gBAAgB;AAC1BO,aAAOE,eAAerE,cAAc4D,KAAK;QACvCU,KAAK,SAALA,MAAG;AAAA,iBAAQtE,aAAawD,SAASI,GAAG;QAAC;MACvC,CAAC;AACD;IACF;AAEA,QAAIW,sBAAsB3D,SAASgD,GAAG,GAAG;AACvCD,4BAAsBC,GAAG;AACzB;IACF;AAEAY,uBAAmBC,KAAK,SAACC,KAAQ;AAC/B,UAAIA,IAAIC,KAAKf,GAAG,KAAKA,OAAO5D,aAAa4E,QAAQ;AAC/C,eAAOjB,sBAAsBC,GAAG;MAClC;AACA,aAAO;IACT,CAAC;EACH,CAAC;AAED,MAAMiB,iBAAiBV,OAAOC,oBAAoBnD,MAAM,EACrD6D,OAAO,SAACC,GAAC;AAAA,WAAK,MAAMJ,KAAKI,CAAC;EAAC,CAAA,EAC3BD,OAAO,SAACd,GAAC;AAAA,WAAK,CAACgB,iBAAiBpE,SAASoD,CAAC;EAAC,CAAA;AAG9Ca,iBAAezD,QAAQ,SAAC4C,GAAM;AAC5B,QAAMiB,aAAad,OAAOe,yBAAyBlF,cAAcgE,CAAC,KAAK;MACrEmB,YAAY;MACZC,UAAU;IACZ;AACA,QAAI;AACFjB,aAAOE,eAAerE,cAAcgE,GAAG;QACrCmB,YAAYF,WAAWE;QACvBE,cAAc;QACdf,KAAK,SAALA,MAAG;AAAA,iBAAQrD,OAAO+C,CAAC;QAAC;QACpBsB,KACEL,WAAWG,YAAYH,WAAWK,MAC9B,SAACC,SAAY;AACXtE,iBAAO+C,CAAC,IAAI,OAAOuB,YAAY,aAAaA,QAAQxB,KAAK/D,YAAY,IAAIuF;QAC3E,IACAvC;MACR,CAAC;IACH,SAASgB,IAAG;AACVC,WAAKD,GAAEE,OAAO;IAChB;EACF,CAAC;AAED3D,YAAUP,aAAaQ,QAAQC,SAAS,0BAA0BT,YAAY;AAChF;AAKA,SAASwF,qBAAqBxF,cAAsB;AAClD,MAAMyF,UAAUzF,aAAaQ;AAC7BR,eAAa0F,KAAKC,UAAUxF,mBAAmB,SAC7CC,MACAmF,SACAjF,SACM;AAEN,QAAMsF,sBAAsBH,QAAQI,qBAAqBvB,IAAI,IAAI;AACjE,QAAIsB,qBAAqB;AACvB,UAAI,CAACA,oBAAoBE,KAAK,SAACzF,UAAQ;AAAA,eAAKA,SAASD,SAASA,QAAQC,SAASkF,YAAYA;MAAO,CAAA,GAAG;AACnGK,4BAAoBG,KAAK;UAAE3F;UAAMmF;UAASjF;QAAQ,CAAC;MACrD;IACF,MAAOmF,SAAQI,qBAAqBP,IAAI,MAAM,CAAC;MAAElF;MAAMmF;MAASjF;IAAQ,CAAC,CAAC;AAC1E,WAAO0F,oBAAoBhF,KAAK,MAAMZ,MAAMmF,SAASjF,OAAO;EAC9D;AAEAN,eAAa0F,KAAKC,UAAUxE,sBAAsB,SAChDf,MACAmF,SACAjF,SACM;AAEN,QAAMsF,sBAAsBH,QAAQI,qBAAqBvB,IAAI,IAAI;AACjE,QAAIsB,qBAAqB;AACvB,UAAMK,QAAQL,wBAAmB,QAAnBA,wBAAmB,SAAA,SAAnBA,oBAAqBM,UAAU,SAACC,KAAG;AAAA,eAAKA,IAAI/F,SAASA,QAAQ+F,IAAIZ,YAAYA;MAAO,CAAA;AAClGK,0BAAoBQ,OAAOH,OAAO,CAAC;IACrC;AACA,QAAI,EAACL,wBAAmB,QAAnBA,wBAAmB,UAAnBA,oBAAqBS,SAAQ;AAChCZ,cAAQI,qBAAoB,QAAA,EAAQ,IAAI;IAC1C;AACA,WAAOS,uBAAuBtF,KAAK,MAAMZ,MAAMmF,SAASjF,OAAO;EACjE;AACF;AAKO,SAASiG,sBAAsBC,aAAkCxG,cAAsB;AAC5F,MAAMyF,UAAUzF,aAAaQ;AAC7B,MAAMqF,uBAGF,oBAAIY,QAAQ;AAChB,MAAMC,kBAAkBlD,SAASmD,iBAAiBH,aAAaI,WAAWC,cAAc,MAAM,KAAK;AACnG,MAAIC,cAAcJ,gBAAgBK;AAClC,SAAOD,aAAa;AAClB,QAAMlB,sBAAsBH,QAAQI,qBAAqBvB,IAAIwC,WAAW;AACxE,QAAIlB,wBAAmB,QAAnBA,wBAAmB,UAAnBA,oBAAqBS,QAAQ;AAC/BR,2BAAqBP,IAAIwB,aAAalB,mBAAmB;AACzDA,0BAAoBxE,QAAQ,SAACf,UAAa;AACxCyG,oBAAY3G,iBAAiBE,SAASD,MAAMC,SAASkF,SAASlF,SAASC,OAAO;MAChF,CAAC;IACH;AACAwG,kBAAcJ,gBAAgBM,SAAS;EACzC;AACAvB,UAAQI,uBAAuBA;AACjC;AAKO,SAASoB,yBACdC,gBACAC,gBACAnH,cACA;AACA,MAAMyF,UAAUzF,aAAaQ;AAC7B,MAAMqF,uBAGF,oBAAIY,QAAQ;AAChB,MAAMb,sBAAsBH,QAAQI,qBAAqBvB,IAAI4C,cAAc;AAC3E,MAAItB,wBAAmB,QAAnBA,wBAAmB,UAAnBA,oBAAqBS,QAAQ;AAC/BR,yBAAqBP,IAAI6B,gBAAgBvB,mBAAmB;AAC5DA,wBAAoBxE,QAAQ,SAACf,UAAa;AACxC8G,qBAAehH,iBAAiBE,SAASD,MAAMC,SAASkF,SAASlF,SAASC,OAAO;IACnF,CAAC;EACH;AACAmF,UAAQI,uBAAuBA;AACjC;AAKO,SAASuB,oBAAoBtG,cAAsBd,cAAsB;AAC9EmE,SAAOE,eAAevD,aAAauG,MAAM1B,WAAW,aAAa;IAC/DrB,KAAK,SAALA,MAAW;AACT,aAAOtE,aAAawD,SAAS8D,YAAY,OAAO,EAAEC;IACpD;EACF,CAAC;AACH;AAOA,SAASC,oBAAoBxH,cAA4B;AACvD,MAAMyF,UAAUzF,aAAaQ;AAQ7B,MAAMiH,qBACJ,oBAAIhB,QAAQ;AACd,MAAMiB,iBAA6E,oBAAIjB,QAAQ;AAC/FzG,eAAa2H,SAAShC,UAAUxF,mBAAmB,SACjDC,MACAmF,SACAjF,SACM;AACN,QAAI,CAACiF,QAAS;AACd,QAAIqC,WAAWH,mBAAmBnD,IAAIiB,OAAO;AAC7C,QAAMsC,WAAWH,eAAepD,IAAIiB,OAAO;AAE3C,QAAI,CAACqC,UAAU;AACbA,iBAAW,OAAOrC,YAAY,aAAaA,QAAQxB,KAAK,IAAI,IAAIwB;AAChEkC,yBAAmBnC,IAAIC,SAASqC,QAAQ;IAC1C;AAEA,QAAIC,UAAU;AACZ,UAAI,CAACA,SAASjH,SAASR,IAAI,EAAGyH,UAAS9B,KAAK3F,IAAI;IAClD,OAAO;AACLsH,qBAAepC,IAAIC,SAAS,CAACnF,IAAI,CAAC;IACpC;AAGAG,cAAUP,aAAaQ,QAAQC,SAAS,gCAAgCT,cAAcI,MAAMwH,UAAUtH,OAAO;AAC7G,QAAIwH,kCAAkClH,SAASR,IAAI,GAAG;AACpD,aAAO4F,oBAAoBhF,KAAK,MAAMZ,MAAMwH,UAAUtH,OAAO;IAC/D;AAEA,QAAImF,QAAQsC,QAAS,QAAOtC,QAAQjC,SAASrD,iBAAiBC,MAAMwH,UAAUtH,OAAO;AACrF,QAAI0H,mCAAmCpH,SAASR,IAAI,EAClD,QAAOa,OAAOuC,SAASrD,iBAAiBC,MAAMwH,UAAUtH,OAAO;AACjE,QAAI2H,iCAAiCrH,SAASR,IAAI,GAAG;AACnDa,aAAOuC,SAASrD,iBAAiBC,MAAMwH,UAAUtH,OAAO;AACxDmF,cAAQyC,WAAW/H,iBAAiBC,MAAMwH,UAAUtH,OAAO;AAC3D;IACF;AACAmF,YAAQyC,WAAW/H,iBAAiBC,MAAMwH,UAAUtH,OAAO;EAC7D;AACAN,eAAa2H,SAAShC,UAAUxE,sBAAsB,SACpDf,MACAmF,SACAjF,SACM;AACN,QAAMsH,WAA+CH,mBAAmBnD,IAAIiB,OAAO;AACnF,QAAMsC,WAAWH,eAAepD,IAAIiB,OAAO;AAC3C,QAAIqC,UAAU;AACZ,UAAIC,aAAQ,QAARA,aAAQ,UAARA,SAAUjH,SAASR,IAAI,GAAG;AAC5ByH,iBAASzB,OAAOyB,SAASM,QAAQ/H,IAAI,GAAG,CAAC;AACzC,YAAI,CAACyH,SAASxB,QAAQ;AACpBoB,6BAAkB,QAAA,EAAQlC,OAAO;AACjCmC,yBAAc,QAAA,EAAQnC,OAAO;QAC/B;MACF;AAGAhF,gBAAUP,aAAaQ,QAAQC,SAAS,mCAAmCT,cAAcI,MAAMwH,UAAUtH,OAAO;AAChH,UAAIwH,kCAAkClH,SAASR,IAAI,GAAG;AACpD,eAAOkG,uBAAuBtF,KAAK,MAAMZ,MAAMwH,UAAUtH,OAAO;MAClE;AACA,UAAImF,QAAQsC,QAAS,QAAOtC,QAAQjC,SAASrC,oBAAoBf,MAAMwH,UAAUtH,OAAO;AACxF,UAAI0H,mCAAmCpH,SAASR,IAAI,GAAG;AACrD,eAAOa,OAAOuC,SAASrC,oBAAoBf,MAAMwH,UAAUtH,OAAO;MACpE;AACA,UAAI2H,iCAAiCrH,SAASR,IAAI,GAAG;AACnDa,eAAOuC,SAASrC,oBAAoBf,MAAMwH,UAAUtH,OAAO;AAC3DmF,gBAAQyC,WAAW/G,oBAAoBf,MAAMwH,UAAUtH,OAAO;AAC9D;MACF;AACAmF,cAAQyC,WAAW/G,oBAAoBf,MAAMwH,UAAUtH,OAAO;IAChE;EACF;AAEA,MAAM8H,kBAAkBjE,OAAOkE,KAAKrI,aAAasI,YAAY3C,SAAS,EAAEb,OAAO,SAACqB,KAAG;AAAA,WAAK,MAAMxB,KAAKwB,GAAG;EAAC,CAAA;AACvG,MAAMoC,kBAAkBpE,OAAOkE,KAAKrI,aAAa2H,SAAShC,SAAS,EAChEb,OAAO,SAACqB,KAAG;AAAA,WAAK,MAAMxB,KAAKwB,GAAG;EAAC,CAAA,EAC/BrB,OAAO,SAACqB,KAAG;AAAA,WAAK,CAACqC,oBAAoB5H,SAASuF,GAAG;EAAC,CAAA;AACrDiC,kBACGtD,OAAO,SAACd,GAAC;AAAA,WAAKuE,gBAAgB3H,SAASoD,CAAC;EAAC,CAAA,EACzC5C,QAAQ,SAAC4C,GAAM;AACd,QAAMiB,aAAad,OAAOe,yBAAyBlF,aAAa2H,SAAShC,WAAW3B,CAAC,KAAK;MACxFmB,YAAY;MACZC,UAAU;IACZ;AACA,QAAI;AACFjB,aAAOE,eAAerE,aAAa2H,SAAShC,WAAW3B,GAAG;QACxDmB,YAAYF,WAAWE;QACvBE,cAAc;QACdf,KAAK,SAALA,MAAG;AAAA,iBAASmB,QAAQsC,UAAUtC,QAAQjC,SAASQ,CAAC,IAAIyB,QAAQyC,WAAWO,kBAAkBzE,CAAC;QAAC;QAC3FsB,KACEL,WAAWG,YAAYH,WAAWK,MAC9B,SAACC,SAAY;AACX,cAAMmD,MAAM,OAAOnD,YAAY,aAAaA,QAAQxB,KAAK/D,aAAawD,QAAQ,IAAI+B;AAClFE,kBAAQsC,UAAWtC,QAAQjC,SAASQ,CAAC,IAAI0E,MAAQjD,QAAQyC,WAAWO,kBAAkBzE,CAAC,IAAI0E;QAC7F,IACA1F;MACR,CAAC;IACH,SAASgB,IAAG;AACVC,WAAKD,GAAEE,OAAO;IAChB;EACF,CAAC;AAEH,MACEyE,kBAOEC,wBAPFD,iBACAE,mBAMED,wBANFC,kBACAC,mBAKEF,wBALFE,kBACAC,gBAIEH,wBAJFG,eACAC,qBAGEJ,wBAHFI,oBACAC,kBAEEL,wBAFFK,iBACAC,iBACEN,wBADFM;AAEFL,mBAAiBM,OAAOL,kBAAkBC,eAAeC,oBAAoBC,eAAe,EAAE7H,QAAQ,SAACgI,SAAY;AACjH,QAAMnE,aAAad,OAAOe,yBAAyBlF,aAAa2H,SAAShC,WAAWyD,OAAO,KAAK;MAC9FjE,YAAY;MACZC,UAAU;IACZ;AACA,QAAI;AACFjB,aAAOE,eAAerE,aAAa2H,SAAShC,WAAWyD,SAAS;QAC9DjE,YAAYF,WAAWE;QACvBE,cAAc;QACdf,KAAK,SAALA,MAAG;AAAA,iBAAQmB,QAAQ4D,cAAcD,OAAO;QAAC;QACzC9D,KAAKtC;MACP,CAAC;IACH,SAASgB,GAAG;AACVC,WAAKD,EAAEE,OAAO;IAChB;EACF,CAAC;AAGDgF,iBAAe9H,QAAQ,SAACgI,SAAY;AAClC,QAAMnE,aAAad,OAAOe,yBAAyBlF,aAAa2H,SAAShC,WAAWyD,OAAO,KAAK;MAC9FjE,YAAY;MACZC,UAAU;IACZ;AAEA,QAAI;AACFjB,aAAOE,eAAerE,aAAa2H,SAAShC,WAAWyD,SAAS;QAC9DjE,YAAYF,WAAWE;QACvBE,cAAc;QACdf,KAAK,SAALA,MAAG;AAAA,kBAASmB,QAAQsC,UAAUtC,UAAUxE,QAAQuC,SAAS4F,OAAO;QAAC;;QAEjE9D,KACEL,WAAWG,YAAYH,WAAWK,MAC9B,SAACC,SAAY;AAGX,WAACE,QAAQsC,UAAUtC,UAAUxE,QAAQuC,SAASrC,oBAC5CiI,SACA3B,mBAAmBnD,IAAIiB,OAAO,CAChC;AAEA,WAACE,QAAQsC,UAAUtC,UAAUxE,QAAQuC,SAASrD,iBAC5CiJ,SACA,OAAO7D,YAAY,aAAaA,QAAQxB,KAAK/D,aAAawD,QAAQ,IAAI+B,OACxE;AAEAkC,6BAAmBnC,IAAIC,SAASA,QAAQxB,KAAK/D,aAAawD,QAAQ,CAAC;QACrE,IACAR;MACR,CAAC;IACH,SAASgB,GAAG;AACVC,WAAKD,EAAEE,OAAO;IAChB;EACF,CAAC;AAEDyE,kBAAgBvH,QAAQ,SAACgI,SAAY;AACnCjF,WAAOE,eAAerE,aAAawD,UAAU4F,SAAS;MACpDjE,YAAY;MACZE,cAAc;MACdf,KAAK,SAALA,MAAG;AAAA,eAAQmB,QAAQ4D,cAAcD,OAAO;MAAC;MACzC9D,KAAKtC;IACP,CAAC;EACH,CAAC;AAEDzC,YAAUP,aAAaQ,QAAQC,SAAS,4BAA4BT,YAAY;AAClF;AAQA,SAASsJ,gBAAgBtJ,cAA4B;AACnD,MAAMuJ,iBAAiBvJ,aAAa0F,KAAKC,UAAU6D;AACnD,MAAMC,kBAAiBzJ,aAAa0F,KAAKC,UAAU+D;AACnD,MAAMC,gBAAgB3J,aAAa0F,KAAKC,UAAUiE;AAClD,MAAMC,iBAAiB7J,aAAa0F,KAAKC,UAAUmE;AACnD9J,eAAa0F,KAAKC,UAAU6D,cAAc,SAAUlJ,SAAoC;AACtF,QAAMyJ,WAAWR,eAAevI,KAAK,MAAMV,OAAO;AAClD,QAAIyJ,aAAa/J,aAAaQ,QAAQ0H,WAAY,QAAOlI,aAAawD;QACjE,QAAOuG;EACd;AACA/J,eAAa0F,KAAKC,UAAU+D,cAAc,SAA0BM,MAAY;AAC9E,QAAMC,MAAMR,gBAAezI,KAAK,MAAMgJ,IAAI;AAC1CE,uBAAmBF,MAAMhK,YAAY;AACrC,WAAOiK;EACT;AACAjK,eAAa0F,KAAKC,UAAUiE,eAAe,SAA0BI,MAASG,OAAuB;AACnG,QAAMF,MAAMN,cAAc3I,KAAK,MAAMgJ,MAAMG,KAAK;AAChDD,uBAAmBF,MAAMhK,YAAY;AACrC,WAAOiK;EACT;AACAjK,eAAa0F,KAAKC,UAAUmE,cAAc,SAA0BE,MAAY;AAC9E,QAAIC;AACJ,QAAI;AACFA,YAAMJ,eAAe7I,KAAK,MAAMgJ,IAAI;IACtC,SAAShG,GAAG;AAAA,UAAAoG;AACVC,cAAQpG,KAAI,0BAAAkF,OACgBa,KAAKM,SAASC,YAAY,GAAC,qBAAA,EAAApB,OAAsB,KAAKmB,SAASC,YAAY,GAAC,yCAAA,CACxG;AACA,UAAIP,KAAKQ,eAAeC,YAAUL,mBAACJ,KAAKU,gBAAU,QAAAN,qBAAA,SAAA,SAAfA,iBAAiBN,WAAW,GAAG;AAChEE,aAAKU,WAAWZ,YAAYE,IAAI;MAClC;IACF;AACAE,uBAAmBF,MAAMhK,YAAY;AACrC,WAAOiK;EACT;AACF;AAMA,SAASU,uBAAuB3K,cAA4B;AAC1D4K,yBAAuB5K,cAAcA,aAAa6K,kBAAkB,KAAK;AACzED,yBAAuB5K,cAAcA,aAAa8K,mBAAmB,MAAM;AAC3EF,yBAAuB5K,cAAcA,aAAa+K,mBAAmB,KAAK;AAC1EH,yBAAuB5K,cAAcA,aAAagL,iBAAiB,MAAM;AACzEJ,yBAAuB5K,cAAcA,aAAaiL,mBAAmB,KAAK;AAC1EL,yBAAuB5K,cAAcA,aAAakL,kBAAkB,KAAK;AAC3E;AAKO,SAASC,SAASnL,cAAsBsC,KAAmB;AAChE,MAAM8I,iBAAiBpL,aAAawD;AACpC,MAAMF,cAAc8H,eAAeC,cAAc,MAAM;AACvD,MAAMC,mBAAmBC,uBAAuBvL,aAAawC,SAASa,IAAI;AAC1E,MAAMmI,gBAAgBD,uBAAuBjJ,GAAG;AAChDgB,cAAYG,aAAa,QAAQ+H,cAAcC,WAAW,OAAOD,cAAcE,OAAOJ,iBAAiB7I,QAAQ;AAC/G2I,iBAAeO,KAAKjC,YAAYpG,WAAW;AAC7C;AASA,SAASsI,cAAc5L,cAAsBwB,OAAcM,cAAsBL,aAA2B;AAC1G,MAAM2J,iBAAiBpL,aAAawD;AACpC,MAAMqI,SAAS5K,OAAOuC,SAASsI,eAAeC,mBAAmB,EAAE;AACnE,MAAMC,qBAAqBZ,eAAea,WAAWJ,OAAOK,iBAAiB,IAAI;AACjFd,iBAAec,kBACXd,eAAee,aAAaH,oBAAoBZ,eAAec,eAAe,IAC9Ed,eAAe1B,YAAYsC,kBAAkB;AACjDhM,eAAaoM,8BAA8BhB,eAAeO;AAC1D3L,eAAaqM,wCAAwCrM,aAAa2H,SAAShC,UAAU2G;AACrFtM,eAAauM,4CAA4CvM,aAAa2H,SAAShC,UAAU6G;AACzFxM,eAAayM,wCAAwCzM,aAAa2H,SAAShC,UAAU0F;AACrFrL,eAAa0M,0CAA0C1M,aAAa2H,SAAShC,UAAUgH;AACvFxB,WAASnL,cAAcwB,MAAMc,GAAG;AAChCT,qBAAmB7B,cAAcyB,aAAaK,YAAY;AAC1D/B,oBAAkBC,YAAY;AAC9B,MAAIwB,MAAMuG,QAASvC,sBAAqBxF,YAAY;AACpD4M,wBAAsB5M,YAAY;AAElC0D,oBAAkB1D,YAAY;AAC9BwH,sBAAoBxH,YAAY;AAChCsJ,kBAAgBtJ,YAAY;AAC5B2K,yBAAuB3K,YAAY;AACrC;AAMA,SAAS6M,kBAAkBC,QAA2BC,cAAgD;AACpG,MAAM/M,eAAe8M,OAAOE;AAC5B,MAAMC,SAASjN,aAAawD;AAC5B,SAAO,IAAI0J,QAAc,SAACC,SAAY;AACpC,aAASC,OAAO;AACdC,iBAAW,WAAM;AACf,YAAIxB;AACJ,YAAI;AACFA,mBAAS7L,aAAawD;QACxB,SAAS8J,KAAK;AACZzB,mBAAS;QACX;AAEA,YAAI,CAACA,UAAUA,UAAUoB,QAAQ;AAC/BG,eAAK;AACL;QACF;AAGA,YAAIL,cAAc;AAChB,cAAM1J,OAAOrD,aAAawC,SAASa;AACnCwI,iBAAO0B,KAAK;AACZ1B,iBAAO2B,MAAM;AAEb,cAAMC,WAAWC,KAAKC,IAAI,IAAI;AAC9B,cAAMC,QAAQ,SAARA,QAAoB;AACxB,gBAAIF,KAAKC,IAAI,IAAIF,UAAU;AAGzBI,yCAA2B;AAC3Bf,qBAAOgB,MAAMf,aAAajL;AAC1B+K,gCAAkBC,QAAQ,KAAK,EAAEiB,KAAKZ,OAAO;AAC7C;YACF;AAEA,gBAAInN,aAAawC,SAASa,SAASA,KAAMgK,YAAWO,OAAO,CAAC;gBACvDT,SAAQ;UACf;AACAS,gBAAM;AAEN;QACF;AAGA5N,qBAAagO,OAAOhO,aAAagO,KAAK,IAAInC,OAAOoC,YAAY,MAAM;AACnEd,gBAAQ;MACV,GAAG,CAAC;IACN;AACAC,SAAK;EACP,CAAC;AACH;AAEO,SAASlD,mBACdgE,SACAlO,cACM;AACN,MAAMmO,gBAAgBnO,aAAaQ,QAAQ2N;AAC3C,MAAID,QAAQE,UAAW;AACvB,MAAI;AACFjK,WAAOkK,iBAAiBH,SAAS;MAC/BI,SAAS;QACPjJ,cAAc;QACdf,KAAK,SAALA,MAAG;AAAA,iBAAQ6J,cAAc1C,WAAW,OAAO0C,cAAczC,OAAOyC,cAAc1L;QAAQ;QACtF6C,KAAKtC;MACP;MACAuL,eAAe;QACblJ,cAAc;QACdf,KAAK,SAALA,MAAG;AAAA,iBAAQtE,aAAawD;QAAQ;MAClC;MACA4K,WAAW;QAAE9J,KAAK,SAALA,MAAG;AAAA,iBAAQ;QAAI;MAAC;IAC/B,CAAC;EACH,SAASkK,QAAO;AACdnE,YAAQpG,KAAKuK,MAAK;EACpB;AACAjO,YAAUP,aAAaQ,QAAQC,SAAS,oBAAoByN,SAASlO,YAAY;AACnF;AAMO,SAAS4M,sBAAsB5M,cAA4B;AAChEA,eAAaG,iBAAiB,cAAc,WAAA;AAAA,WAAM+C,gBAAgBlD,YAAY;EAAC,CAAA;AAC/EA,eAAaG,iBAAiB,YAAY,WAAM;AAC9C+C,oBAAgBlD,YAAY;EAC9B,CAAC;AACH;AAQO,SAASyO,qBACdC,cACA1O,cACA2O,YACA;AACA,MAAAC,OACEF,cADMZ,MAAGc,KAAHd,KAAKe,SAAMD,KAANC,QAAQC,UAAOF,KAAPE,SAASC,cAAWH,KAAXG,aAAaC,kBAAeJ,KAAfI,iBAAiBC,QAAKL,KAALK,OAAOC,QAAKN,KAALM,OAAOtH,WAAQgH,KAARhH,UAAUuH,SAAMP,KAANO;AAEpF,MAAMC,gBAAgBpP,aAAawD,SAAS6H,cAAc,QAAQ;AAClE,MAAMgE,oBAAoBrP,aAAawD,SAAS6H,cAAc,QAAQ;AACtE,MAAAiE,wBAA4CtP,aAAaQ,SAAjDsC,UAAOwM,sBAAPxM,SAASrC,UAAO6O,sBAAP7O,SAAS0N,gBAAamB,sBAAbnB;AAC1B,MAAMoB,WAAWC,YAAY;IAAE/O;IAASqC;EAAQ,CAAC;AACjD,MAAI2M,OAAOF,SAAST,SAAShB,KAAK4B,UAAUvB,aAAa,CAAC;AAE1De,WACE/K,OAAOkE,KAAK6G,KAAK,EACdpK,OAAO,SAAClB,KAAG;AAAA,WAAK,CAACO,OAAOkE,KAAKqG,YAAY,EAAE9N,SAASgD,GAAG;EAAC,CAAA,EACxDxC,QAAQ,SAACwC,KAAG;AAAA,WAAKwL,cAAc3L,aAAaG,KAAK+L,OAAOT,MAAMtL,GAAG,CAAC,CAAC;EAAC,CAAA;AAGzE,MAAIkL,SAAS;AAEX,QAAI,CAAC9O,aAAaQ,QAAQuH,WAAW,CAAC8G,QAAQ;AAC5CY,aAAI,sDAAAtG,OACFsG,MAAI,kJAAA;IAOR;AACA,QAAMxK,aAAad,OAAOe,yBAAyBkK,eAAe,KAAK;AAEvE,QAAInK,eAAU,QAAVA,eAAU,UAAVA,WAAYI,gBAAgB,CAACJ,YAAY;AAE3C,UAAI;AACFd,eAAOE,eAAe+K,eAAe,OAAO;UAAE9K,KAAK,SAALA,MAAG;AAAA,mBAAQwJ,OAAO;UAAE;QAAC,CAAC;MACtE,SAASU,QAAO;AACdnE,gBAAQpG,KAAKuK,MAAK;MACpB;IACF;EACF,OAAO;AACLV,WAAOsB,cAAc3L,aAAa,OAAOqK,GAAG;AAC5CiB,mBAAeK,cAAc3L,aAAa,eAAeuL,eAAe;EAC1E;AACAH,YAAUO,cAAc3L,aAAa,QAAQ,QAAQ;AACrD2L,gBAAcQ,cAAcH,QAAQ;AACpCJ,oBAAkBO,cAChB;AAEF,MAAMC,YAAYtM,yBAAyBvC,KAAKhB,aAAawD,UAAU,MAAM;AAC7E,MAAMsM,iBAAiB,SAAjBA,kBAAc;AAAA,WAAS,CAACb,SAASY,UAAUnG,YAAY2F,iBAAiB;EAAC;AAC/E,MAAMU,kBAAkB,SAAlBA,mBAAwB;AAC5BZ,eAAM,QAANA,WAAM,UAANA,OAAS;AACTW,mBAAe;EACjB;AAGA,MAAI,mBAAmBnL,KAAK8K,IAAI,GAAG;AACjCjB,UAAMwB,mCAAmCtB,YAAY;AACrD,WAAOoB,eAAe;EACxB;AAGA,MAAInB,YAAY;AACdsB,mBAAeb,eAAec,iBAAiBvB,UAAU,CAAC;EAC5D;AAEA,MAAMwB,kBAAkB,CAACrB,WAAWhB;AACpC,MAAIqC,iBAAiB;AACnBf,kBAAcD,SAASY;AACvBX,kBAAcgB,UAAUL;EAC1B;AACAF,YAAUnG,YAAY0F,aAAa;AAGnCxH,eAAQ,QAARA,aAAQ,UAARA,SAAW5H,YAAY;AAEvBO,YAAUE,SAAS,6BAA6B2O,eAAepP,cAAc2O,UAAU;AAEvF,GAACwB,mBAAmBJ,gBAAgB;AACtC;AAQO,SAASM,uBACdvC,KACAI,SAEM;AAAA,MADNoC,eAAoCC,UAAAlK,SAAA,KAAAkK,UAAA,CAAA,MAAAvN,SAAAuN,UAAA,CAAA,IAAG,CAAC;AAExC,MAAMzD,SAAS7L,OAAOuC,SAAS6H,cAAc,QAAQ;AACrD,MAAMmF,eAAe;AACrBC,oBAAkB3D,QAAM4D,eAAAA,eAAA,CAAA,GAAOJ,YAAY,GAAA,CAAA,GAAA;IAAExC;IAAK6C,OAAO,CAACH,cAAcF,aAAaK,KAAK,EAAEC,KAAK,GAAG;EAAC,CAAA,CAAE;AACvGC,2BAAyB/D,QAAQoB,OAAO;AAC1C;AAEA,IAAA4C,QAA8D,WAAM;AAClE,MAAMC,kBAAkB;AACxB,MAAIC,WAAW;AACf,MAAI;AACFA,eAAWC,aAAaC,QAAQH,eAAe,MAAM;EACvD,SAAS/M,GAAG;EACV;AAGF,MAAIgN,YAAY,OAAO5N,QAAQ,eAAe,OAAOA,IAAI+N,oBAAoB,WAC3E,QAAO,CAAC,WAAA;AAAA,WAAM;EAAE,GAAE,WAAA;AAAA,WAAM;EAAM,CAAA;AAEhC,MAAIC,UAAU;AACd,MAAMC,0BAAyB,SAAzBA,0BAA+B;AACnC,QAAIL,SAAU,QAAO;AACrB,QAAII,QAAS,QAAOA;AAEpB,QAAME,OAAO,IAAIC,KAAK,CAAC,wDAAwD,GAAG;MAAEnR,MAAM;IAAY,CAAC;AACvGgR,cAAUhO,IAAI+N,gBAAgBG,IAAI;AAClC,WAAOF;EACT;AAEA,MAAMvD,8BAA6B,SAA7BA,8BAAmC;AACvCmD,eAAW;AACX,QAAI;AAEFC,mBAAaO,QAAQT,iBAAiB,MAAM;IAC9C,SAAS/M,GAAG;IAAC;EACf;AAEA,SAAO,CAACqN,yBAAwBxD,2BAA0B;AAC5D,EAAG;AA/BH,IA+BI4D,QAAAC,eAAAZ,OAAA,CAAA;AA/BJ,IAAOO,yBAAsBI,MAAA,CAAA;AAA7B,IAA+B5D,6BAA0B4D,MAAA,CAAA;AAsClD,SAASE,gBACdlM,SACAyJ,OACApN,cACAL,aACAmQ,cACmB;AACnB,MAAI9D,MAAMoB,SAASA,MAAMpB;AACzB,MAAIf,eAAe;AACnB,MAAI,CAACe,KAAK;AACRA,UAAMuD,uBAAuB;AAC7BtE,mBAAe,CAAC,CAACe;AACjB,QAAI,CAACA,IAAKA,OAAMhM;EAClB;AAEA,MAAMgL,SAAS7L,OAAOuC,SAAS6H,cAAc,QAAQ;AACrD,MAAMwG,aAAUnB,eAAAA,eAAA;IACdC,OAAO;EAAe,GACnBzB,KAAK,GAAA,CAAA,GAAA4C,gBAAA;IACRhE;IACAiE,MAAMtM,QAAQuM;EAAE,GACfC,iBAAkB,EAAE,CAAA;AAEvBxB,oBAAkB3D,QAAQ+E,UAAU;AACpC5Q,SAAOuC,SAAS0O,KAAKxI,YAAYoD,MAAM;AAEvC,MAAM9M,eAAe8M,OAAOE;AAE5BzL,sBAAoBvB,cAAcyF,SAAShE,WAAW;AACtDgE,UAAQ0M,cAActF,kBAAkBC,QAAQC,gBAAgB;IAAEjL;EAAa,CAAC,EAAEiM,KAAK,WAAM;AAC3F,QAAI,CAAC/N,aAAaQ,SAAS;AACzBe,0BAAoBvB,cAAcyF,SAAShE,WAAW;IACxD;AACAmK,kBAAc5L,cAAcyF,SAAS3D,cAAcL,WAAW;AAI9D,QAAI,CAAC2Q,qBAAqBpS,aAAaQ,QAAQwR,EAAE,GAAG;AAClDhS,mBAAa+B,QAAQI,aAAa,MAAM,IAAIL,eAAe8P,YAAY;IACzE;EACF,CAAC;AACD,SAAO9E;AACT;;;ACj6BA,SAASuF,gBAAgBC,QAA2BC,OAAeC,aAA8B;AAC/F,MAAAC,wBAA4DH,OAAOI,cAAcC,SAAzEC,aAAUH,sBAAVG,YAAYC,KAAEJ,sBAAFI,IAAIC,UAAOL,sBAAPK,SAASC,YAAQN,sBAARM,UAAUC,eAAYP,sBAAZO;AAC3C,MAAIC,MAAMV;AACV,MAAI,CAAC,QAAQW,KAAKD,GAAG,GAAG;AACtB,QAAIE,cAAcC,uBAAuBH,GAAG;AAC5CA,UAAMT,cAAcW,YAAYE,WAAWF,YAAYG,SAASH,YAAYI;AAC5EJ,kBAAc;EAChB;AACAb,SAAOI,cAAcC,QAAQa,WAAW;AACxC,MAAIV,SAAS;AACX,QAAMW,aAAaC,yBAAyBC,KAAKrB,OAAOsB,iBAAiB,MAAM;AAC/EC,6BAAyBd,UAASe,iBAAiBL,UAAU;AAC7DM,2BAAuBC,OAAOC,mBAAmBhB,GAAG,GAAGiB,iBAAiBrB,EAAE,EAAEsB,eAAenB,YAAY;EACzG,MAAOe,wBAAuBd,KAAKL,WAAWwB,KAAKD,eAAenB,YAAY;AAC9EqB,kBAAgBxB,IAAII,GAAG;AACvB,SAAO;AACT;AAKO,SAASqB,eACdhC,QACAiC,YACAC,cACAhC,aAKA;AACA,MAAMiC,cAAc,IAAIC,MAAMpC,OAAOI,eAAe;IAClDiC,KAAK,SAALA,IAAMC,QAAgBC,GAAwB;AAE5C,UAAIA,MAAM,YAAY;AACpB,eAAOD,OAAOjC,QAAQmC;MACxB;AAEA,UAAID,MAAM,UAAWA,MAAM,YAAYE,OAAOC,yBAAyBhB,QAAQ,QAAQ,EAAEW,KAAM;AAC7F,eAAOC,OAAOjC,QAAQsC;MACxB;AAEA,UAAIJ,MAAM,2CAA2CA,MAAM,6CAA6C;AACtG,eAAOD,OAAOC,CAAC;MACjB;AAEA,UAAMK,aAAaH,OAAOC,yBAAyBJ,QAAQC,CAAC;AAC5D,WAAIK,eAAU,QAAVA,eAAU,SAAA,SAAVA,WAAYC,kBAAiB,UAASD,eAAU,QAAVA,eAAU,SAAA,SAAVA,WAAYE,cAAa,OAAO;AACxE,eAAOR,OAAOC,CAAC;MACjB;AAEA,aAAOQ,eAAeT,QAAQC,CAAC;IACjC;IAEAS,KAAK,SAALA,IAAMV,QAAgBC,GAAgBtC,OAAe;AACnDgD,yBAAmBX,QAAQrC,KAAK;AAChCqC,aAAOC,CAAC,IAAItC;AACZ,aAAO;IACT;IAEAiD,KAAK,SAALA,IAAMZ,QAAgBC,GAAc;AAAA,aAAKA,KAAKD;IAAM;EACtD,CAAC;AAGD,MAAMa,gBAAgB,IAAIf,MACxB,CAAC,GACD;IACEC,KAAK,SAALA,IAAee,eAAeC,SAAS;AACrC,UAAM5C,YAAWiB,OAAOjB;AACxB,UAAA6C,yBAAsCtD,OAAOI,cAAcC,SAAnDC,aAAUgD,uBAAVhD,YAAYkC,iBAAac,uBAAbd;AAEpB,UAAI,CAAClC,WAAYiD,gBAAe;AAChC,UAAMC,mBAAmBxD,OAAOI,cAAcqD;AAC9C,UAAMC,oBAAoB1D,OAAOI,cAAcuD;AAE/C,UAAIN,YAAY,mBAAmBA,YAAY,kBAAkB;AAC/D,eAAO,IAAIjB,MAAM3B,UAAS4C,OAAO,GAAG;UAClCO,OAAK,SAALA,MAAMC,gBAAgBC,MAAMC,MAAM;AAChC,gBAAMC,kBAAkBX,YAAY,kBAAkBG,mBAAmBE;AACzE,gBAAMO,UAAUD,gBAAgBJ,MAAM5D,OAAOsB,iBAAiByC,IAAI;AAClEG,+BAAmBD,SAASjE,OAAOI,aAAa;AAChD,mBAAO6D;UACT;QACF,CAAC;MACH;AACA,UAAIZ,YAAY,iBAAiBA,YAAY,OAAO;AAClD,eAAQb,eAA2B2B;MACrC;AAGA,UACEd,YAAY,0BACZA,YAAY,4BACZA,YAAY,qBACZ;AACA,eAAO,IAAIjB,MAAM9B,WAAW8D,kBAAkB;UAC5CR,OAAK,SAALA,MAAMQ,kBAAkBN,MAAMC,MAAM;AAClC,gBAAIM,MAAMN,KAAK,CAAC;AAChB,gBAAID,SAAS9D,OAAOsB,iBAAiB;AACnC,qBAAOwC,KAAKT,OAAO,EAAEO,MAAME,MAAMC,IAAI;YACvC;AAEA,gBAAIV,YAAY,0BAA0BgB,QAAQ,UAAU;AAC1D,qBAAOrE,OAAOsB,gBAAgBgD;YAChC;AACA,gBAAIjB,YAAY,yBAA0BgB,OAAM,MAAMA;AACtD,gBAAIhB,YAAY,oBAAqBgB,OAAG,UAAAE,OAAaF,KAAG,IAAA;AAMxD,gBAAIG;AACJ,gBAAI;AACFA,oBAAMJ,iBAAiB/C,KAAKf,YAAY+D,GAAG;YAC7C,SAASI,QAAO;AACdD,oBAAM,CAAA;YACR;AAEA,mBAAOA;UACT;QACF,CAAC;MACH;AACA,UAAInB,YAAY,kBAAkB;AAChC,eAAO,IAAIjB,MAAM9B,WAAWoE,eAAe;;UAEzCd,OAAK,SAALA,MAAMtB,QAAQqC,KAAKZ,MAAM;AACvB,gBAAIY,QAAQ3E,OAAOsB,iBAAiB;AAAA,kBAAAsD;AAClC,sBAAAA,eAAOD,IAAItB,OAAO,OAAC,QAAAuB,iBAAA,SAAA,SAAZA,aAAchB,MAAMe,KAAKZ,IAAI;YACtC;AACA,gBAAI;AACF,qBACEzB,OAAOjB,KAAKf,YAAU,QAAAiE,OAAUR,KAAK,CAAC,GAAC,IAAA,CAAI,KAC3C/D,OAAOI,cAAcyE,sCAAsCxD,KACzDrB,OAAOI,cAAcK,UAAQ,IAAA8D,OACzBR,KAAK,CAAC,CAAC,CACb;YAEJ,SAASU,QAAO;AACdK,mBAAKC,4BAA4B;AACjC,qBAAO;YACT;UACF;QACF,CAAC;MACH;AACA,UAAI1B,YAAY,mBAAmBA,YAAY,oBAAoB;AACjE,YAAM2B,aAAa;UACjBN,eAAe;UACfN,kBAAkB;QACpB;AACA,eAAO,IAAIhC,MAAM9B,WAAW+C,OAAO,GAAG;UACpCO,OAAK,SAALA,MAAMtB,QAAQqC,KAAKZ,MAAM;AACvB,gBAAIY,QAAQ3E,OAAOsB,iBAAiB;AAAA,kBAAA2D;AAClC,sBAAAA,gBAAON,IAAItB,OAAO,OAAC,QAAA4B,kBAAA,SAAA,SAAZA,cAAcrB,MAAMe,KAAKZ,IAAI;YACtC;AAEA,mBACEzB,OAAOsB,MAAMtD,YAAYyD,IAAI,MAC5BA,KAAK,CAAC,MAAM,SACT,OACA/D,OAAOI,cAAc4E,WAAW3B,OAAO,CAAC,EAAEhC,KAAKrB,OAAOI,cAAcK,UAAUsD,KAAK,CAAC,CAAC;UAE7F;QACF,CAAC;MACH;AACA,UAAIV,YAAY,qBAAqBA,YAAY,mBAAoB,QAAO/C,WAAW4E;AACvF,UAAI7B,YAAY,QAAS,QAAO/C,WAAW8D,iBAAiB,MAAM;AAClE,UAAIf,YAAY,SAAU,QAAO/C,WAAW8D,iBAAiB,KAAK;AAClE,UAAIf,YAAY,QAAS,QAAO/C,WAAW8D,iBAAiB,GAAG;AAC/D,UAAQe,kBACNC,wBADMD,iBAAiBE,mBACvBD,wBADuBC,kBAAkBC,gBACzCF,wBADyCE,eAAeC,qBACxDH,wBADwDG,oBAAoBC,kBAC5EJ,wBAD4EI;AAE9E,UAAIL,gBAAgBZ,OAAOc,gBAAgB,EAAEI,SAASpC,QAAQqC,SAAS,CAAC,GAAG;AACzE,YAAIrC,YAAY,mBAAmB/C,WAAWqF,kBAAkB,KAAM,QAAOrF,WAAWsF;AACxF,eAAOtF,WAAW+C,OAAO;MAC3B;AACA,UAAIiC,cAAcG,SAASpC,QAAQqC,SAAS,CAAC,GAAG;AAAA,YAAAG;AAC9C,gBAAAA,kBAAO9C,eAAezC,YAAY+C,OAAO,OAAC,QAAAwC,oBAAA,SAAAA,kBAAI9C,eAAetC,WAAU4C,OAAO;MAChF;AAEA,UAAIkC,mBAAmBE,SAASpC,QAAQqC,SAAS,CAAC,GAAG;AACnD,eAAOjF,UAAS4C,OAAO;MACzB;AACA,UAAImC,gBAAgBC,SAASpC,QAAQqC,SAAS,CAAC,GAAG;AAChD,eAAO3C,eAAetC,WAAU4C,OAAO;MACzC;IACF;EACF,CACF;AAGA,MAAMb,gBAAgB,IAAIJ,MACxB,CAAC,GACD;IACEC,KAAK,SAALA,IAAeyD,eAAezC,SAAS;AACrC,UAAM0C,YAAW/F,OAAOI,cAAc2F;AACtC,UACE1C,YAAY,UACZA,YAAY,cACZA,YAAY,cACZA,YAAY,UACZA,YAAY,UACZ;AACA,eAAOpB,WAAWoB,OAAO;MAC3B;AACA,UAAIA,YAAY,QAAQ;AACtB,eAAO0C,UAAS1C,OAAO,EAAE2C,QAAQ9D,cAAchC,WAAW;MAC5D;AACA,UAAImD,YAAY,UAAU;AACxByB,aAAKmB,0BAA0B;AAC/B,eAAO,WAAA;AAAA,iBAAM;QAAI;MACnB;AACA,UAAI5C,YAAY,WAAW;AACzB,eAAO,IAAIjB,MAAM2D,UAAS1C,OAAO,GAAG;UAClCO,OAAK,SAALA,MAAMoC,SAASlC,MAAMC,MAAM;AAAA,gBAAAmC;AACzB,mBAAOF,QAAQ3E,KAAK0E,YAAQG,SAAEnC,KAAK,CAAC,OAAC,QAAAmC,WAAA,SAAA,SAAPA,OAASF,QAAQ9F,aAAagC,YAAY,CAAC;UAC3E;QACF,CAAC;MACH;AACA,aAAOa,eAAegD,WAAU1C,OAAO;IACzC;IACAL,KAAK,SAALA,IAAe8C,eAAezC,SAASpD,OAAO;AAE5C,UAAIoD,YAAY,QAAQ;AACtB,eAAOtD,gBAAgBC,QAAQC,OAAOC,WAAW;MACnD;AACAF,aAAOI,cAAc2F,SAAS1C,OAAO,IAAIpD;AACzC,aAAO;IACT;IACAkG,SAAS,SAATA,WAAqB;AACnB,aAAO1D,OAAO2D,KAAKpG,OAAOI,cAAc2F,QAAQ,EAAEM,OAAO,SAACC,KAAG;AAAA,eAAKA,QAAQ;MAAQ,CAAA;IACpF;IACA5D,0BAA0B,SAA1BA,yBAAoC6D,SAASD,KAAK;AAChD,aAAO;QAAEE,YAAY;QAAM3D,cAAc;QAAM5C,OAAO,KAAKqG,GAAG;MAAE;IAClE;EACF,CACF;AACA,SAAO;IAAEnE;IAAagB;IAAeX;EAAc;AACrD;AAKO,SAASiE,eACdzG,QACAiC,YACAC,cACAhC,aAIA;AAEA,MAAMiD,gBAAgB,CAAC;AACvB,MAAMuD,UAAU1G,OAAOI,cAAcC;AAErCoC,SAAOkE,iBAAiBxD,eAAe;IACrCyD,eAAe;MACbvE,KAAK,SAALA,MAAW;AACT,eAAO,WAAmB;AAAA,mBAAAwE,OAAAC,UAAAC,QAANhD,OAAI,IAAAiD,MAAAH,IAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAAJlD,iBAAIkD,IAAA,IAAAH,UAAAG,IAAA;UAAA;AACtB,cAAMhD,UAAUjE,OAAOI,cAAcqD,sCAAsCG,MACzE5D,OAAOsB,iBACPyC,IACF;AACAG,6BAAmBD,SAASjE,OAAOI,aAAa;AAChD,iBAAO6D;QACT;MACF;IACF;IACAiD,gBAAgB;MACd7E,KAAK,SAALA,MAAW;AACT,eAAO,WAAmB;AAAA,mBAAA8E,QAAAL,UAAAC,QAANhD,OAAI,IAAAiD,MAAAG,KAAA,GAAAC,QAAA,GAAAA,QAAAD,OAAAC,SAAA;AAAJrD,iBAAIqD,KAAA,IAAAN,UAAAM,KAAA;UAAA;AACtB,cAAMnD,UAAUjE,OAAOI,cAAcuD,wCAAwCC,MAC3E5D,OAAOsB,iBACPyC,IACF;AACAG,6BAAmBD,SAASjE,OAAOI,aAAa;AAChD,iBAAO6D;QACT;MACF;IACF;IACAoD,aAAa;MACXhF,KAAK,SAALA,MAAG;AAAA,eAASqE,QAAQlE,cAA2B2B;MAAI;IACrD;IACAmD,KAAK;MACHjF,KAAK,SAALA,MAAG;AAAA,eAASqE,QAAQlE,cAA2B2B;MAAI;IACrD;IACAoD,sBAAsB;MACpBlF,KAAG,SAAHA,MAAM;AACJ,eAAO,WAAmB;AACxB,cAAMmF,UAAOV,UAAAC,UAAA,IAAAU,SAAAX,UAAA,CAAA;AACb,cAAIU,YAAY,UAAU;AACxB,mBAAOxH,OAAOsB,gBAAgBgD;UAChC;AACA,iBAAOoC,QAAQjG,SAAS8G,qBAAqBC,OAAO;QACtD;MACF;IACF;IACAE,gBAAgB;MACdrF,KAAG,SAAHA,MAAM;AACJ,eAAO,WAAmB;AACxB,cAAM9B,KAAEuG,UAAAC,UAAA,IAAAU,SAAAX,UAAA,CAAA;AACR,iBAAQJ,QAAQjG,SAASiH,eAAenH,EAAE,KAAaP,OAAOsB,gBAAgBoG,eAAenH,EAAE;QACjG;MACF;IACF;EACF,CAAC;AAED,MACEoH,wBAOEvC,wBAPFuC,uBACAC,mBAMExC,wBANFwC,kBACAzC,kBAKEC,wBALFD,iBACAE,mBAIED,wBAJFC,kBACAC,gBAGEF,wBAHFE,eACAC,qBAEEH,wBAFFG,oBACAC,kBACEJ,wBADFI;AAEFoC,mBACGvB,OAAO,SAACC,KAAG;AAAA,WAAK,CAACqB,sBAAsBlC,SAASa,GAAG;EAAC,CAAA,EACpD/B,OAAOY,iBAAiBE,kBAAkBC,eAAeC,oBAAoBC,eAAe,EAC5FqC,QAAQ,SAACvB,KAAQ;AAChB7D,WAAOqF,eAAe3E,eAAemD,KAAK;MACxCjE,KAAK,SAALA,MAAW;AAAA,YAAA0F;AACT,YAAM9H,SAAK8H,oBAAGrB,QAAQjG,cAAQ,QAAAsH,sBAAA,SAAA,SAAhBA,kBAAmBzB,GAAG;AACpC,eAAO0B,WAAW/H,KAAK,IAAIA,MAAMgI,KAAKvB,QAAQjG,QAAQ,IAAIR;MAC5D;IACF,CAAC;EACH,CAAC;AAGH,MAAMuC,gBAAgB,CAAC;AACvB,MAAMuD,YAAW/F,OAAOI,cAAc2F;AACtC,MAAMmC,eAAezF,OAAO2D,KAAKL,SAAQ;AACzC,MAAMoC,cAAc,CAAC,QAAQ,YAAY,QAAQ,YAAY,MAAM;AACnEA,cAAYN,QAAQ,SAACvB,KAAQ;AAC3B9D,kBAAc8D,GAAG,IAAIrE,WAAWqE,GAAG;EACrC,CAAC;AACD7D,SAAOkE,iBAAiBnE,eAAe;IACrC2B,MAAM;MACJ9B,KAAK,SAALA,MAAG;AAAA,eAAQ0D,UAAS5B,KAAK6B,QAAQ9D,cAAchC,WAAW;MAAC;MAC3D8C,KAAK,SAALA,IAAM/C,OAAU;AACdF,wBAAgBC,QAAQC,OAAOC,WAAW;MAC5C;IACF;IACAkI,QAAQ;MACN/F,KAAG,SAAHA,MAAM;AACJyC,aAAKmB,0BAA0B;AAC/B,eAAO,WAAA;AAAA,iBAAM;QAAI;MACnB;IACF;EACF,CAAC;AACDiC,eACG7B,OAAO,SAACC,KAAG;AAAA,WAAK,CAAC6B,YAAY5D,OAAO,CAAC,QAAQ,QAAQ,CAAC,EAAEkB,SAASa,GAAG;EAAC,CAAA,EACrEuB,QAAQ,SAACvB,KAAQ;AAChB7D,WAAOqF,eAAetF,eAAe8D,KAAK;MACxCjE,KAAK,SAALA,MAAG;AAAA,eAAS2F,WAAWjC,UAASO,GAAG,CAAC,IAAIP,UAASO,GAAG,EAAE2B,KAAKlC,SAAQ,IAAIA,UAASO,GAAG;MAAC;IACtF,CAAC;EACH,CAAC;AACH,SAAO;IAAEnD;IAAeX;EAAc;AACxC;;;AClXO,IAAM6F,iBAAiBC,OAAOC,uBACjCD,OAAOE,QAAQC,OAAOJ,iBACtB,oBAAIK,IAAsB;AAG9B,IAAaC,WAAQ,WAAA;AAInB,WAAAA,UAAYC,IAAY;AAAAC,oBAAA,MAAAF,SAAA;AACtB,SAAKC,KAAKA;AACV,SAAKE,OAAO;AACZ,QAAI,CAACT,eAAeU,IAAI,KAAKH,EAAE,GAAG;AAChCP,qBAAeW,IAAI,KAAKJ,IAAI,CAAC,CAAC;IAChC;AACA,SAAKK,WAAWZ,eAAeU,IAAI,KAAKH,EAAE;EAC5C;AAEA,SAAAM,aAAAP,WAAA,CAAA;IAAAQ,KAAA;IAAAC,OACA,SAAOC,IAAIC,OAAeC,IAAwB;AAChD,UAAMC,MAAM,KAAKP,SAASK,KAAK;AAC/B,UAAI,CAACE,KAAK;AACR,aAAKP,SAASK,KAAK,IAAI,CAACC,EAAE;AAC1B,eAAO;MACT;AACA,UAAI,CAACC,IAAIC,SAASF,EAAE,EAAGC,KAAIE,KAAKH,EAAE;AAClC,aAAO;IACT;;EAEA,GAAA;IAAAJ,KAAA;IAAAC,OACA,SAAOO,OAAOJ,IAA2D;AACvE,aAAO,KAAKF,IAAIO,iBAAiBL,EAAE;IACrC;;EAEA,GAAA;IAAAJ,KAAA;IAAAC,OACA,SAAOS,MAAMP,OAAeC,IAAoB;AAC9C,UAAMO,MAAK,WAA+B;AACxC,aAAKC,KAAKT,OAAOQ,EAAE;AACnBP,WAAES,MAAA,QAAAC,SAAQ;MACZ,GAAEC,KAAK,IAAI;AACX,WAAKb,IAAIC,OAAOQ,EAAE;IACpB;;EAEA,GAAA;IAAAX,KAAA;IAAAC,OACA,SAAOW,KAAKT,OAAeC,IAAwB;AACjD,UAAMC,MAAM,KAAKP,SAASK,KAAK;AAC/B,UAAI,CAACA,SAAS,CAACE,OAAO,CAACA,IAAIW,QAAQ;AACjCC,aAAI,GAAAC,OAAIf,OAAK,GAAA,EAAAe,OAAIC,qBAAqB,CAAE;AACxC,eAAO;MACT;AAEA,UAAIC;AACJ,UAAIC,IAAIhB,IAAIW;AACZ,aAAOK,KAAK;AACVD,aAAKf,IAAIgB,CAAC;AACV,YAAID,OAAOhB,IAAI;AACbC,cAAIiB,OAAOD,GAAG,CAAC;AACf;QACF;MACF;AACA,aAAO;IACT;;EAEA,GAAA;IAAArB,KAAA;IAAAC,OACA,SAAOsB,QAAQnB,IAAwB;AACrC,aAAO,KAAKQ,KAAKH,iBAAiBL,EAAE;IACtC;;EAEA,GAAA;IAAAJ,KAAA;IAAAC,OACA,SAAOuB,MAAMrB,OAA8C;AACzD,UAAIE,MAAM,CAAA;AACV,UAAIoB,SAAS,CAAA;AAEbvC,qBAAewC,QAAQ,SAAC5B,UAAa;AACnC,YAAIA,SAASK,KAAK,EAAGE,OAAMA,IAAIa,OAAOpB,SAASK,KAAK,CAAC;AACrD,YAAIL,SAASW,eAAe,EAAGgB,UAASA,OAAOP,OAAOpB,SAASW,eAAe,CAAC;MACjF,CAAC;AAED,UAAI,CAACN,SAAUE,IAAIW,WAAW,KAAKS,OAAOT,WAAW,GAAI;AACvDC,aAAI,GAAAC,OAAIf,OAAK,GAAA,EAAAe,OAAIC,qBAAqB,CAAE;MAC1C,OAAO;AACL,YAAI;AAAA,mBAAAQ,OAAAb,UAAAE,QAZuBY,OAAI,IAAAC,MAAAF,OAAA,IAAAA,OAAA,IAAA,CAAA,GAAAG,OAAA,GAAAA,OAAAH,MAAAG,QAAA;AAAJF,iBAAIE,OAAA,CAAA,IAAAhB,UAAAgB,IAAA;UAAA;AAa7B,mBAAST,IAAI,GAAGU,IAAI1B,IAAIW,QAAQK,IAAIU,GAAGV,KAAG;AAAA,gBAAAW;AAAE,aAAAA,OAAA3B,KAAIgB,CAAC,EAACR,MAAAmB,MAAIJ,IAAI;UAAE;AAC5D,mBAASP,KAAI,GAAGU,KAAIN,OAAOT,QAAQK,KAAIU,IAAGV,MAAG;AAAA,gBAAAY;AAAE,aAAAA,UAAAR,QAAOJ,EAAC,EAACR,MAAAoB,SAAA,CAAC9B,KAAK,EAAAe,OAAKU,IAAI,CAAA;UAAE;QAC3E,SAASM,GAAG;AACVC,gBAAMD,CAAC;QACT;MACF;AACA,aAAO;IACT;;EAEA,GAAA;IAAAlC,KAAA;IAAAC,OACA,SAAON,SAAmB;AAAA,UAAAyC;AACxB,UAAMtC,YAAQsC,sBAAGlD,eAAeU,IAAI,KAAKH,EAAE,OAAC,QAAA2C,wBAAA,SAAAA,sBAAI,CAAC;AACjD,UAAMC,SAASC,OAAOC,KAAKzC,QAAQ;AACnCuC,aAAOX,QAAQ,SAACvB,OAAK;AAAA,eAAK,OAAOL,SAASK,KAAK;MAAC,CAAA;AAChD,aAAO;IACT;EAAC,CAAA,CAAA;AAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;ACzDH,IAGqBqC,QAAK,WAAA;AA2axB,WAAAA,OAAYC,SAST;AAAAC,oBAAA,MAAAF,MAAA;AAtXHG,oBAAA,MAAA,wBAII,oBAAIC,QAAQ,CAAC;AAoXf,QAAIC,OAAOC,qBAAsB,MAAKC,SAASF,OAAOG,QAAQD;SACzD;AACH,WAAKA,SAAS;QACZE,gBAAgBC;QAChBC;QACAC,cAAcP,OAAOQ,SAASC,WAAW,OAAOT,OAAOQ,SAASE;MAClE;IACF;AACA,QAAQC,OAAwEf,QAAxEe,MAAMC,MAAkEhB,QAAlEgB,KAAKC,QAA6DjB,QAA7DiB,OAAOC,QAAsDlB,QAAtDkB,OAAOC,eAA+CnB,QAA/CmB,cAAcC,UAAiCpB,QAAjCoB,SAASC,aAAwBrB,QAAxBqB,YAAYC,UAAYtB,QAAZsB;AACpE,SAAKC,KAAKR;AACV,SAAKG,QAAQA;AACb,SAAKE,UAAUA,WAAW,CAACI;AAC3B,SAAKC,MAAM,IAAIC,SAAS,KAAKH,EAAE;AAC/B,SAAKP,MAAMA;AACX,SAAKG,eAAeA;AACpB,SAAKQ,UAAU;MAAEF,KAAK,KAAKA;IAAI;AAC/B,SAAKG,qBAAqB,CAAA;AAC1B,SAAKC,YAAY,CAAA;AACjB,SAAKR,aAAaA;AAClB,SAAKC,UAAUQ,WAAWR,OAAO;AAGjC,QAAAS,iBAAkDC,cAAchB,GAAG,GAA3DiB,aAAUF,eAAVE,YAAYC,cAAWH,eAAXG,aAAaC,eAAYJ,eAAZI;AACjC,QAAQxB,eAAiB,KAAKL,OAAtBK;AAER,SAAKyB,SAASC,gBAAgB,MAAMpB,OAAON,cAAcuB,aAAaC,YAAY;AAElF,QAAI,KAAKf,SAAS;AAChB,UAAAkB,kBAAyCC,eAAe,KAAKH,QAAQH,YAAYtB,cAAcuB,WAAW,GAAlGM,gBAAaF,gBAAbE,eAAeC,gBAAaH,gBAAbG;AACvB,WAAKD,gBAAgBA;AACrB,WAAKC,gBAAgBA;IACvB,OAAO;AACL,UAAAC,kBAAsDC,eACpD,KAAKP,QACLH,YACAtB,cACAuB,WACF,GALQU,cAAWF,gBAAXE,aAAaJ,iBAAaE,gBAAbF,eAAeC,iBAAaC,gBAAbD;AAMpC,WAAKI,QAAQD;AACb,WAAKJ,gBAAgBA;AACrB,WAAKC,gBAAgBA;IACvB;AACA,SAAKd,QAAQf,WAAW,KAAK6B;AAE7BK,6BAAyB,KAAKvB,IAAI,IAAI;EACxC;AAAC,SAAAwB,aAAAhD,QAAA,CAAA;IAAAiD,KAAA;IAAAC;;;;;;;;;MAhZD,WAAA;AAAA,YAAAC,UAAAC,kBAAAC,oBAAAA,QAAAC,KAMA,SAAAC,QAAoBtD,SAUnB;AAAA,cAAAuD,QAAA;AAAA,cAAAC,MAAAxC,KAAAyC,IAAAC,UAAAC,OAAAC,OAAAC,QAAAC,OAAAC,SAAAC,cAAAC,aAAAC,YAAAC,uBAAA/B,QAAAgC,WAAAC;AAAA,iBAAAjB,oBAAAA,QAAAkB,KAAA,SAAAC,SAAAC,UAAA;AAAA,mBAAA,EAAA,SAAAA,SAAAC,OAAAD,SAAAE,MAAA;cAAA,KAAA;AACSlB,uBAAkExD,QAAlEwD,MAAMxC,MAA4DhB,QAA5DgB,KAAKyC,KAAuDzD,QAAvDyD,IAAIC,WAAmD1D,QAAnD0D,UAAUC,QAAyC3D,QAAzC2D,OAAOC,QAAkC5D,QAAlC4D,OAAOC,SAA2B7D,QAA3B6D,QAAQC,QAAmB9D,QAAnB8D,OAAOC,UAAY/D,QAAZ+D;AAC9D,qBAAK/C,MAAMA;AACX,qBAAKwC,OAAOA;AACZ,qBAAKI,QAAQA;AACb,qBAAKe,WAAW;AAChB,qBAAKd,SAASA,WAAM,QAANA,WAAM,SAANA,SAAU,KAAKA;AAC7B,qBAAKE,UAAUA,YAAO,QAAPA,YAAO,SAAPA,UAAW,KAAKA;AAC/B,qBAAKpC,QAAQgC,QAAQA,UAAK,QAALA,UAAK,SAALA,QAAS,KAAKhC,QAAQgC;AAC3C,qBAAKiB,aAAa;AAClBJ,yBAAAE,OAAA;AAAA,uBACM,KAAKG;cAAW,KAAA;AAIhBb,+BAAe,KAAK5B,OAAO0C;AAC3Bb,8BAAcH,QAChB,SAACiB,OAAoBC,MAAkB;AAAA,yBACrClB,MAAM,OAAOiB,UAAU,WAAWE,gBAAgBF,OAAQxB,MAAKd,cAA2ByC,IAAI,IAAIH,OAAOC,IAAI;gBAAC,IAChH,KAAKlB;AACT,oBAAIG,aAAa;AACfD,+BAAaF,QAAQG;AACrB,uBAAKH,QAAQG;gBACf;AAGA,oBAAI,KAAKkB,YAAY,KAAKvB,OAAO;AAE/BwB,kCAAgBpB,YAAY;gBAC9B,OAAO;AAELqB,kCAAgBrB,YAAY;AAC5BoB,kCAAgBpB,YAAY;gBAC9B;AAGA,qBAAKN,WAAWA,aAAQ,QAARA,aAAQ,SAARA,WAAY,KAAKA;AAEjC,oBAAA,CACI,KAAKtC,SAAO;AAAAoD,2BAAAE,OAAA;AAAA;gBAAA;AACRR,6BAAaoB,yBAAyBC,KAAKvB,aAAawB,UAAU,MAAM;AAACrB,wCACjDsB,6BAA6B,KAAKlE,IAAIkC,OAAE,QAAFA,OAAE,SAAFA,KAAMS,YAAY,KAAK/C,YAAY,GAA/FiB,SAAM+B,sBAAN/B,QAAQgC,YAASD,sBAATC;AAChB,qBAAKX,KAAKW;AAEV,oBAAIX,GAAIiC,YAAWxB,UAAU;AAE7ByB,oCAAoBvD,OAAO0C,eAAed,YAAY;AAEtD5B,uBAAO0C,cAAcc,WAAW,WAAM;AACpCrC,wBAAKsC,QAAQ;gBACf;AAAE,oBAAA,CACE,KAAKL,UAAQ;AAAAhB,2BAAAE,OAAA;AAAA;gBAAA;AAAA,oBAAA,CACX,KAAKd,OAAK;AAAAY,2BAAAE,OAAA;AAAA;gBAAA;AACZtC,uBAAO0D,gBAAgBC,aAAa,KAAKP,SAASQ,iBAAiB5D,OAAO0D,gBAAgBE,eAAe;AAEzGC,sCAAsB7D,OAAO0D,gBAAgBE,iBAAiBhC,YAAY;AAAEQ,yBAAAE,OAAA;AAAA;cAAA,KAAA;AAAAF,yBAAAE,OAAA;AAAA,uBAEtEwB,uBAAuB9D,OAAO0D,iBAAiB,KAAK1D,OAAO0C,eAAe,KAAKpB,QAAQ;cAAC,KAAA;AAE9FyC,yCAAyB,KAAKX,SAASQ,iBAAiB5D,OAAO0D,gBAAgBE,iBAAiBhC,YAAY;cAAE,KAAA;AAAAQ,yBAAAE,OAAA;AAAA;cAAA,KAAA;AAAAF,yBAAAE,OAAA;AAAA,uBAG1GwB,uBAAuB9D,OAAO0D,iBAAiB,KAAK1D,OAAO0C,eAAe,KAAKpB,QAAQ;cAAC,KAAA;AAEhG,qBAAK8B,WAAWpD,OAAO0D;AAAgB,uBAAAtB,SAAA4B,OAAA,QAAA;cAAA,KAAA;AAAA,oBAAA,CAIrC,KAAKC,YAAU;AAAA7B,2BAAAE,OAAA;AAAA;gBAAA;AAOjB,qBAAKjB,KAAK6C,yBAAyB,KAAKD,WAAWvF,MAAM2C,EAAE;AAAE,oBAAA,CACzD,KAAKG,OAAK;AAAAY,2BAAAE,OAAA;AAAA;gBAAA;AAAA,uBAAAF,SAAA4B,OAAA,QAAA;cAAA,KAAA;AAAA5B,yBAAAE,OAAA;AAAA;cAAA,KAAA;AAGRR,8BAAaoB,yBAAyBC,KAAKvB,aAAawB,UAAU,MAAM;AAC9E,qBAAK/B,KAAK6C,yBAAyBC,wBAAwB,KAAKhF,EAAE,GAAGkC,OAAE,QAAFA,OAAE,SAAFA,KAAMS,WAAU;cAAE,KAAA;AAAAM,yBAAAE,OAAA;AAAA,uBAGnF8B,2BAA2B,KAAKH,YAAYrC,cAAc,KAAKN,QAAQ;cAAC,KAAA;AAC9E,qBAAK+C,cAAc;AAGnB,qBAAK9E,QAAQ0E,aAAa,KAAKA;cAAW,KAAA;cAAA,KAAA;AAAA,uBAAA7B,SAAAkC,KAAA;YAAA;UAAA,GAAApD,SAAA,IAAA;QAAA,CAC3C,CAAA;AAAA,iBAlGYqD,OAAMC,IAAA;AAAA,iBAAA1D,QAAA2D,MAAA,MAAAC,SAAA;QAAA;AAAA,eAANH;MAAM,EAAA;;EAoGnB,GAAA;IAAA3D,KAAA;IAAAC,OACA,SAAO8D,qBAAoBC,UAAU;AAAA,UAAAC,SAAA;AACnC,aAAOF,oBAAoB,WAAM;AAE/B,YAAI,CAACE,OAAK7E,OAAQ;AAClB4E,iBAASH,MAAMI,MAAI;MACrB,CAAC;IACH;;;;;EACA,GAAA;IAAAjE,KAAA;IAAAC,OAAA,WAAA;AAAA,UAAAiE,SAAA/D,kBAAAC,oBAAAA,QAAAC,KAIA,SAAA8D,SAAmBC,oBAA0C;AAAA,YAAAC,SAAA;AAAA,YAAAC,kBAAAtD,cAAAuD,wBAAAC,uBAAAC,sBAAAC,uBAAAC,uBAAAC,yBAAAC;AAAA,eAAAzE,oBAAAA,QAAAkB,KAAA,SAAAwD,UAAAC,WAAA;AAAA,iBAAA,EAAA,SAAAA,UAAAtD,OAAAsD,UAAArD,MAAA;YAAA,KAAA;AAC3D,mBAAKS,WAAW;AAChB4C,wBAAArD,OAAA;AAAA,qBAC+B0C,mBAAmB;YAAC,KAAA;AAA7CE,iCAAgBS,UAAAC;AAAA,kBAEjB,KAAK5F,QAAM;AAAA2F,0BAAArD,OAAA;AAAA;cAAA;AAAA,qBAAAqD,UAAA3B,OAAA,QAAA;YAAA,KAAA;AACVpC,6BAAe,KAAK5B,OAAO0C;AAEjCd,2BAAa3D,uBAAuB;AAE9BkH,uCAA+CU,iBAAiB,mBAAmB,KAAK3G,OAAO;AAE/FkG,sCAA8CS,iBAAiB,kBAAkB,KAAK3G,OAAO;AAE7FmG,qCAAyC,CAAA;AAEzCC,sCAA0C,CAAA;AAE1CC,sCAA0C,CAAA;AAChDL,+BAAiBY,QAAQ,SAACC,cAAiB;AACzC,oBAAIA,aAAaC,MAAOT,uBAAsBU,KAAKF,YAAY;yBACtDA,aAAaG,MAAOZ,uBAAsBW,KAAKF,YAAY;oBAC/DV,sBAAqBY,KAAKF,YAAY;cAC7C,CAAC;AAGDZ,qCAAuBW,QAAQ,SAACK,oBAAuB;AACrDlB,uBAAKxF,UAAUwG,KAAK,WAAA;AAAA,yBAClBhB,OAAKnG,QACDmG,OAAKN,oBAAoB,WAAA;AAAA,2BAAMyB,qBAAqBD,oBAAoBvE,YAAY;kBAAC,CAAA,IACrFwE,qBAAqBD,oBAAoBvE,YAAY;gBAAC,CAC5D;cACF,CAAC;AAGDyD,mCAAqBgB,OAAOd,qBAAqB,EAAEO,QAAQ,SAACC,cAAiB;AAC3Ed,uBAAKxF,UAAUwG,KAAK,WAAA;AAAA,yBAClBF,aAAaO,eAAeC,KAAK,SAACC,SAAO;AAAA,2BACvCvB,OAAKnG,QACDmG,OAAKN,oBAAoB,WAAA;AAAA,6BAAMyB,qBAAoBK,eAAAA,eAAA,CAAA,GAAMV,YAAY,GAAA,CAAA,GAAA;wBAAES;sBAAO,CAAA,GAAI5E,YAAY;oBAAC,CAAA,IAC/FwE,qBAAoBK,eAAAA,eAAA,CAAA,GAAMV,YAAY,GAAA,CAAA,GAAA;sBAAES;oBAAO,CAAA,GAAI5E,YAAY;kBAAC,CACtE;gBAAC,CACH;cACF,CAAC;AAGD0D,oCAAsBQ,QAAQ,SAACC,cAAiB;AAC9CA,6BAAaO,eAAeC,KAAK,SAACC,SAAY;AAC5CvB,yBAAKnG,QACDmG,OAAKN,oBAAoB,WAAA;AAAA,2BAAMyB,qBAAoBK,eAAAA,eAAA,CAAA,GAAMV,YAAY,GAAA,CAAA,GAAA;sBAAES;oBAAO,CAAA,GAAI5E,YAAY;kBAAC,CAAA,IAC/FwE,qBAAoBK,eAAAA,eAAA,CAAA,GAAMV,YAAY,GAAA,CAAA,GAAA;oBAAES;kBAAO,CAAA,GAAI5E,YAAY;gBACrE,CAAC;cACH,CAAC;AAGD,mBAAKnC,UAAUwG,KAAK,KAAKnH,QAAQ,WAAA;AAAA,uBAAMmG,OAAKN,oBAAoB,WAAA;AAAA,yBAAMM,OAAKyB,MAAM;gBAAC,CAAA;cAAC,IAAG,WAAA;AAAA,uBAAMzB,OAAKyB,MAAM;cAAC,CAAA;AAGlGlB,wCAA0B,SAA1BA,2BAAgC;AAAA,oBAAAmB;AACpCC,6BAAahF,aAAawB,UAAU,kBAAkB;AACtDwD,6BAAahF,cAAc,kBAAkB;AAC7C,iBAAA+E,wBAAA1B,OAAKxF,UAAUoH,MAAM,OAAC,QAAAF,0BAAA,UAAtBA,sBAAyB;cAC3B;AACA,mBAAKlH,UAAUwG,KAAK,KAAKnH,QAAQ,WAAA;AAAA,uBAAMmG,OAAKN,oBAAoBa,uBAAuB;cAAC,IAAGA,uBAAuB;AAGlHJ,oCAAsBU,QAAQ,SAACgB,mBAAsB;AACnD7B,uBAAKxF,UAAUwG,KAAK,WAAA;AAAA,yBAClBhB,OAAKnG,QACDmG,OAAKN,oBAAoB,WAAA;AAAA,2BAAMyB,qBAAqBU,mBAAmBlF,YAAY;kBAAC,CAAA,IACpFwE,qBAAqBU,mBAAmBlF,YAAY;gBAAC,CAC3D;cACF,CAAC;AAGK6D,iCAAmB,SAAnBA,oBAAyB;AAAA,oBAAAsB;AAC7BH,6BAAahF,aAAawB,UAAU,kBAAkB;AACtDwD,6BAAahF,cAAc,MAAM;AACjC,iBAAAmF,yBAAA9B,OAAKxF,UAAUoH,MAAM,OAAC,QAAAE,2BAAA,UAAtBA,uBAAyB;cAC3B;AACA,mBAAKtH,UAAUwG,KAAK,KAAKnH,QAAQ,WAAA;AAAA,uBAAMmG,OAAKN,oBAAoBc,gBAAgB;cAAC,IAAGA,gBAAgB;AAEpG,kBAAI,KAAKjE,SAAS,CAACwF,WAAW,KAAKhH,OAAO0C,cAAcuE,eAAe,EAAGC,eAAc,KAAK7F,EAAE;AAC/F,mBAAK5B,UAAUoH,MAAM,EAAE;AAEvB,qBAAAlB,UAAA3B,OAAA,UACO,IAAImD,QAAQ,SAACC,SAAY;AAC9BnC,uBAAKxF,UAAUwG,KAAK,WAAM;AAAA,sBAAAoB;AACxBD,0BAAQ;AACR,mBAAAC,yBAAApC,OAAKxF,UAAUoH,MAAM,OAAC,QAAAQ,2BAAA,UAAtBA,uBAAyB;gBAC3B,CAAC;cACH,CAAC,CAAC;YAAA,KAAA;YAAA,KAAA;AAAA,qBAAA1B,UAAArB,KAAA;UAAA;QAAA,GAAAS,UAAA,IAAA;MAAA,CACH,CAAA;AAAA,eA5FYuC,MAAKC,KAAA;AAAA,eAAAzC,OAAAL,MAAA,MAAAC,SAAA;MAAA;AAAA,aAAL4C;IAAK,EAAA;EA8FlB,GAAA;IAAA1G,KAAA;IAAAC,OAMA,SAAO6F,QAAc;AAAA,UAAAc;AACnB,UAAI,KAAKC,UAAW;AACpB,UAAIT,WAAW,KAAKhH,OAAO0C,cAAcgF,aAAa,GAAG;AAAA,YAAAC,kBAAAC,uBAAAC,mBAAAC;AACvDZ,sBAAc,KAAK7F,EAAE;AACrB,SAAAsG,mBAAA,KAAK1I,gBAAU,QAAA0I,qBAAA,WAAAC,wBAAfD,iBAAiBI,iBAAW,QAAAH,0BAAA,UAA5BA,sBAAAzE,KAAAwE,kBAA+B,KAAK3H,OAAO0C,aAAa;AACxD,aAAK1C,OAAO0C,cAAcgF,cAAc;AACxC,SAAAG,oBAAA,KAAK5I,gBAAU,QAAA4I,sBAAA,WAAAC,wBAAfD,kBAAiBG,gBAAU,QAAAF,0BAAA,UAA3BA,sBAAA3E,KAAA0E,mBAA8B,KAAK7H,OAAO0C,aAAa;AACvD,aAAK+E,YAAY;MACnB;AACA,UAAI,KAAKjG,OAAO;AAAA,YAAAyG,mBAAAC;AACd,SAAAD,oBAAA,KAAKhJ,gBAAU,QAAAgJ,sBAAA,WAAAC,wBAAfD,kBAAiBE,eAAS,QAAAD,0BAAA,UAA1BA,sBAAA/E,KAAA8E,mBAA6B,KAAKjI,OAAO0C,aAAa;MACxD;AACA,OAAA8E,wBAAA,KAAK/H,UAAUoH,MAAM,OAAC,QAAAW,0BAAA,UAAtBA,sBAAyB;IAC3B;;EAEA,GAAA;IAAA5G,KAAA;IAAAC,OAAA,WAAA;AAAA,UAAAuH,WAAArH,kBAAAC,oBAAAA,QAAAC,KACA,SAAAoH,WAAA;AAAA,YAAAC,mBAAAC,uBAAAC,mBAAAC,uBAAAC,mBAAAC,uBAAAC;AAAA,eAAA5H,oBAAAA,QAAAkB,KAAA,SAAA2G,UAAAC,WAAA;AAAA,iBAAA,EAAA,SAAAA,UAAAzG,OAAAyG,UAAAxG,MAAA;YAAA,KAAA;AACE,mBAAKE,aAAa;AAElBuG,kCAAoB;AACpB,kBAAI,KAAKvH,OAAO;AACd,iBAAA8G,oBAAA,KAAKrJ,gBAAU,QAAAqJ,sBAAA,WAAAC,wBAAfD,kBAAiBU,iBAAW,QAAAT,0BAAA,UAA5BA,sBAAApF,KAAAmF,mBAA+B,KAAKtI,OAAO0C,aAAa;cAC1D;AAAC,kBACI,KAAK+E,WAAS;AAAAqB,0BAAAxG,OAAA;AAAA;cAAA;AAAA,qBAAAwG,UAAA9E,OAAA,QAAA;YAAA,KAAA;AAAA,kBAAA,EACfgD,WAAW,KAAKhH,OAAO0C,cAAcuE,eAAe,KAAK,CAAC,KAAKzF,SAAS,CAAC,KAAKe,WAAQ;AAAAuG,0BAAAxG,OAAA;AAAA;cAAA;AACxF,eAAAkG,oBAAA,KAAKvJ,gBAAU,QAAAuJ,sBAAA,WAAAC,wBAAfD,kBAAiBS,mBAAa,QAAAR,0BAAA,UAA9BA,sBAAAtF,KAAAqF,mBAAiC,KAAKxI,OAAO0C,aAAa;AAAEoG,wBAAAxG,OAAA;AAAA,qBACtD,KAAKtC,OAAO0C,cAAcuE,gBAAgB;YAAC,KAAA;AACjD,eAAAyB,oBAAA,KAAKzJ,gBAAU,QAAAyJ,sBAAA,WAAAC,wBAAfD,kBAAiBQ,kBAAY,QAAAP,0BAAA,UAA7BA,sBAAAxF,KAAAuF,mBAAgC,KAAK1I,OAAO0C,aAAa;AACzD,mBAAK+E,YAAY;AACjB,eAAAmB,YAAA,KAAKvJ,SAAG,QAAAuJ,cAAA,UAARA,UAAUO,OAAO;AACjB,kBAAI,CAAC,KAAKnK,SAAS;AACjBsE,2BAAW,KAAKW,UAAU;AAE1BmF,oCAAoB,KAAKC,IAAI;AAC7BD,oCAAoB,KAAKE,IAAI;cAC/B;AACAhG,yBAAW,KAAK+F,IAAI;AACpB/F,yBAAW,KAAKgG,IAAI;YAAE,KAAA;YAAA,KAAA;AAAA,qBAAAR,UAAAxE,KAAA;UAAA;QAAA,GAAA+D,UAAA,IAAA;MAAA,CAEzB,CAAA;AAAA,eAvBY5E,UAAO;AAAA,eAAA2E,SAAA3D,MAAA,MAAAC,SAAA;MAAA;AAAA,aAAPjB;IAAO,EAAA;EAyBpB,GAAA;IAAA7C,KAAA;IAAAC,OACA,SAAO0I,WAAU;AACf,WAAK9F,QAAQ;AACb,WAAKpE,IAAI8J,OAAO;AAChB,WAAKlF,aAAa;AAClB,WAAKxD,QAAQ;AACb,WAAKL,gBAAgB;AACrB,WAAKC,gBAAgB;AACrB,WAAKZ,YAAY;AACjB,WAAKF,UAAU;AACf,WAAKR,eAAe;AACpB,WAAKS,qBAAqB;AAC1B,WAAKH,MAAM;AACX,WAAKsC,UAAU;AACf,WAAKD,QAAQ;AACb,WAAKqB,WAAW;AAChB,WAAK0E,YAAY;AACjB,WAAKlF,WAAW;AAChB,WAAKa,WAAW;AAChB,WAAKiG,OAAO;AACZ,WAAKC,OAAO;AACZ,WAAKE,uBAAuB;AAC5B,WAAKvK,aAAa;AAClB,WAAKC,UAAU;AACf,WAAKK,UAAU;AACf,WAAKrB,SAAS;AACd,WAAKuB,YAAY;AACjB,WAAKgC,SAAS;AAEd,UAAI,KAAKJ,IAAI;AACXiC,mBAAW,KAAKjC,EAAE;AAClB,aAAKA,KAAK;MACZ;AAEA,UAAI,KAAKrB,QAAQ;AAAA,YAAAyJ;AACf,YAAM7H,gBAAe,KAAK5B,OAAO0C;AACjC,YAAId,kBAAY,QAAZA,kBAAY,UAAZA,cAAc8H,yBAAyB;AACzC9H,wBAAa8H,wBAAwB5D,QAAQ,SAAC6D,GAAM;AAClD/H,0BAAawH,oBAAoBO,EAAEC,MAAMD,EAAEE,UAAUF,EAAE/L,OAAO;UAChE,CAAC;QACH;AACA,SAAA6L,wBAAA,KAAKzJ,OAAO8J,gBAAU,QAAAL,0BAAA,UAAtBA,sBAAwBM,YAAY,KAAK/J,MAAM;AAC/C,aAAKA,SAAS;MAChB;AACAgK,sBAAgB,KAAK7K,EAAE;IACzB;;EAEA,GAAA;IAAAyB,KAAA;IAAAC,OACA,SAAOoJ,qBAA2B;AAAA,UAAAC,SAAA;AAChC,UAAI,KAAK1K,sBAAsB,KAAKA,mBAAmB2K,QAAQ;AAC7D,aAAK3K,mBAAmBsG,QAAQ,SAACsE,mBAAsB;AACrDC,gCAAsBlH,KAAK+G,OAAKlL,UAAUkL,OAAK9G,SAASiG,OAAOa,OAAKjG,WAAWoF,MAAMe,iBAAiB;QACxG,CAAC;MACH;AACA,WAAK/F,cAAc;IACrB;;;;;;EAEA,GAAA;IAAAzD,KAAA;IAAAC,OAKA,SAAOwD,gBAAsB;AAC3B,UAAI,KAAKrF,QAAS;AAClB,UAAI,KAAKiF,WAAWvF,KAAK4L,aAAaC,0BAA0B,EAAG;AACnE,UAAAC,wBAAuDC,sBACrDC,MAAMC,KAAK,KAAK3K,OAAO0D,gBAAgBkH,iBAAiB,OAAO,CAAC,EAAEC,IAChE,SAACT,mBAAiB;AAAA,eAAKA,kBAAkBU;MAAK,CAChD,CACF,GAACC,yBAAAC,eAAAR,uBAAA,CAAA,GAJMS,wBAAqBF,uBAAA,CAAA,GAAEG,wBAAqBH,uBAAA,CAAA;AAKnD,UAAIE,uBAAuB;AACzB,aAAKhH,WAAWoF,KAAK8B,YAAYF,qBAAqB;AACtD,aAAKzL,mBAAmByG,KAAKgF,qBAAqB;MACpD;AACA,UAAIC,uBAAuB;AACzB,aAAKjH,WAAWvF,KAAKyM,YAAYD,qBAAqB;MACxD;AACA,OAACD,yBAAyBC,0BACxB,KAAKjH,WAAWvF,KAAK0M,aAAab,4BAA4B,EAAE;IACpE;EAAC,CAAA,CAAA;AAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;ACncI,IAAMc,MAAM,IAAIC,SAASC,KAAKC,IAAI,EAAEC,SAAS,CAAC;AA8IrD,IAAIC,OAAOC,WAAW,CAACD,OAAOE,sBAAsB;AAClDC,iBAAe;AACjB;AAGAC,sBAAsB;AAGtBC,wBAAwB;AAGxB,IAAI,CAACC,aAAcC,MAAKC,wBAAwB;AAKzC,SAASC,SAASC,SAA6B;AACpD,MAAIA,QAAQC,KAAMC,4BAA2BF,QAAQC,MAAMD,OAAO;AACpE;AAKA,SAAsBG,SAAQC,IAAA;AAAA,SAAAC,UAAAC,MAAA,MAAAC,SAAA;AAAA;AA8F9B,SAAAF,YAAA;AAAAA,cAAAG,kBAAAC,oBAAAA,QAAAC,KA9FO,SAAAC,SAAwBC,cAA0B;AAAA,QAAAC,uBAAAC;AAAA,QAAAC,SAAAC,cAAAhB,SAAAC,MAAAgB,KAAAC,MAAAC,SAAAC,OAAAC,OAAAC,OAAAC,cAAAC,OAAAC,OAAAC,SAAAC,MAAAC,QAAAC,IAAAC,SAAAC,SAAAC,YAAAC,eAAAC,sBAAAC,uBAAAC,sBAAAC,uBAAAC,oBAAAC,sBAAAC,sBAAAC,uBAAAC,sBAAAC,uBAAAC,YAAAC,oBAAAC,UAAAC,oBAAAC,wBAAAC;AAAA,WAAAxC,oBAAAA,QAAAyC,KAAA,SAAAC,UAAAC,WAAA;AAAA,aAAA,EAAA,SAAAA,UAAAC,OAAAD,UAAAE,MAAA;QAAA,KAAA;AACjDvC,oBAAUwC,aAAa3C,aAAaX,IAAI;AACxCe,yBAAewC,eAAe5C,aAAaX,IAAI;AAE/CD,oBAAUyD,aAAa7C,cAAcI,YAAY;AAErDf,iBAiBED,QAjBFC,MACAgB,MAgBEjB,QAhBFiB,KACAC,OAeElB,QAfFkB,MACAC,UAcEnB,QAdFmB,SACAC,QAaEpB,QAbFoB,OACAC,QAYErB,QAZFqB,OACAC,QAWEtB,QAXFsB,OACAC,eAUEvB,QAVFuB,cACAC,QASExB,QATFwB,OACAC,QAQEzB,QARFyB,OACAC,UAOE1B,QAPF0B,SACAC,OAME3B,QANF2B,MACAC,SAKE5B,QALF4B,QACAC,KAIE7B,QAJF6B,IACAC,UAGE9B,QAHF8B,SACAC,UAEE/B,QAFF+B,SACAC,aACEhC,QADFgC;AAEF,cAAA,CACIjB,SAAO;AAAAqC,sBAAAE,OAAA;AAAA;UAAA;AACTvC,kBAAQgB,UAAU2B,WAAW3B,OAAO;AACpChB,kBAAQiB,aAAaA;AACf2B,0BAAe5C,QAAQ6C,OAAOC;AAAa,cAAA,CAC7C9C,QAAQ+C,SAAO;AAAAV,sBAAAE,OAAA;AAAA;UAAA;AAAAF,oBAAAE,OAAA;AAAA,iBACXvC,QAAQ+C;QAAO,KAAA;AAAA,cAAA,CAEnBrC,OAAK;AAAA2B,sBAAAE,OAAA;AAAA;UAAA;AAAAF,oBAAAE,OAAA;AAAA,iBAEDvC,QAAQgD,OAAO;YAAE9C;YAAKU;YAAMC;YAAQC;YAAIR;YAAOI;YAAOL;YAAOD;UAAQ,CAAC;QAAC,KAAA;AAAA,cAExEJ,QAAQiD,UAAQ;AAAAZ,sBAAAE,OAAA;AAAA;UAAA;AACnB,WAAAlB,uBAAArB,QAAQiB,gBAAU,QAAAI,yBAAA,WAAAC,wBAAlBD,qBAAoB6B,gBAAU,QAAA5B,0BAAA,UAA9BA,sBAAA6B,KAAA9B,sBAAiCrB,QAAQ6C,OAAOC,aAAa;AAAET,oBAAAE,OAAA;AAAA,iBAC1Ba,WAAW;YAC9ClD;YACAC;YACAkD,MAAM;cACJhD,OAAOA,SAAS9B,OAAO8B;cACvBW,SAAShB,QAAQgB;cACjBsC,WAAWtD,QAAQiB,WAAWqC;cAC9B7C;YACF;UACF,CAAC;QAAC,KAAA;AAAAc,+BAAAc,UAAAkB;AATMvB,UAAAA,uBAAkBT,mBAAlBS;AAAkBK,oBAAAE,OAAA;AAAA,iBAUpBvC,QAAQwD,MAAMxB,oBAAkB;QAAC,KAAA;AAEzC,WAAAb,uBAAAnB,QAAQiB,gBAAU,QAAAE,yBAAA,WAAAC,wBAAlBD,qBAAoBsC,eAAS,QAAArC,0BAAA,UAA7BA,sBAAA+B,KAAAhC,sBAAgCnB,QAAQ6C,OAAOC,aAAa;AAAE,iBAAAT,UAAAqB,OAAA,UACvD1D,QAAQ2D,OAAO;QAAA,KAAA;AAAA,cAAA,CACbC,WAAWhB,cAAaiB,aAAa,GAAC;AAAAxB,sBAAAE,OAAA;AAAA;UAAA;AAK/CvC,kBAAQ8D,QAAQ;AAAEzB,oBAAAE,OAAA;AAAA,iBACZvC,QAAQgD,OAAO;YAAE9C;YAAKU;YAAMC;YAAQC;YAAIR;YAAOI;YAAOL;YAAOD;UAAQ,CAAC;QAAC,KAAA;AAE7EJ,kBAAQ+D,mBAAmB;AAE3B,WAAAtC,uBAAAzB,QAAQiB,gBAAU,QAAAQ,yBAAA,WAAAC,wBAAlBD,qBAAoBuC,iBAAW,QAAAtC,0BAAA,UAA/BA,sBAAAyB,KAAA1B,sBAAkCzB,QAAQ6C,OAAOC,aAAa;AAC9DF,wBAAaiB,cAAc;AAC3B,WAAAlC,uBAAA3B,QAAQiB,gBAAU,QAAAU,yBAAA,WAAAC,wBAAlBD,qBAAoBsC,gBAAU,QAAArC,0BAAA,UAA9BA,sBAAAuB,KAAAxB,sBAAiC3B,QAAQ6C,OAAOC,aAAa;AAC7D9C,kBAAQkE,YAAY;AAAK,iBAAA7B,UAAAqB,OAAA,UAClB1D,QAAQ2D,OAAO;QAAA,KAAA;AAGtB3D,kBAAQ2D,QAAQ;QAAE,KAAA;AAKtBQ,qBAAWrD,IAAIC,OAAO;AAChBc,uBAAa,IAAIuC,MAAM;YAAElF;YAAMgB;YAAKK;YAAOC;YAAcC;YAAOE;YAASK;YAASC;UAAW,CAAC;AACpG,WAAAnB,wBAAA+B,WAAWZ,gBAAU,QAAAnB,0BAAA,WAAAC,yBAArBD,sBAAuBoD,gBAAU,QAAAnD,2BAAA,UAAjCA,uBAAAoD,KAAArD,uBAAoC+B,WAAWgB,OAAOC,aAAa;AAAET,oBAAAE,OAAA;AAAA,iBACEa,WAAW;YAChFlD;YACAC;YACAkD,MAAM;cACJhD,OAAOA,SAAS9B,OAAO8B;cACvBW,SAASa,WAAWb;cACpBsC,WAAWzB,WAAWZ,WAAWqC;cACjC7C;YACF;UACF,CAAC;QAAC,KAAA;AAAAqB,+BAAAO,UAAAkB;AATMxB,qBAAQD,mBAARC;AAAUC,+BAAkBF,mBAAlBE;AAAoBC,mCAAsBH,mBAAtBG;AAAsBI,oBAAAE,OAAA;AAAA,iBAWhC8B,iBAAiBxC,YAAYE,UAAUE,sBAAsB;QAAC,KAAA;AAApFC,0BAAaG,UAAAkB;AAAAlB,oBAAAE,OAAA;AAAA,iBACbV,WAAWmB,OAAO;YAAE9C;YAAKU;YAAMC;YAAQkB,UAAUG;YAAepB;YAAIR;YAAOI;YAAOL;YAAOD;UAAQ,CAAC;QAAC,KAAA;AAAAiC,oBAAAE,OAAA;AAAA,iBACnGV,WAAW2B,MAAMxB,kBAAkB;QAAC,KAAA;AAAA,iBAAAK,UAAAqB,OAAA,UACnC7B,WAAW8B,OAAO;QAAA,KAAA;QAAA,KAAA;AAAA,iBAAAtB,UAAAiC,KAAA;MAAA;IAAA,GAAA1E,QAAA;EAAA,CAC1B,CAAA;AAAA,SAAAN,UAAAC,MAAA,MAAAC,SAAA;AAAA;AAKM,SAAS+E,WAAWC,YAA8B;AACvDC,sBAAoB,WAA4B;AAK9C,QAAIjC,aAAagC,WAAWtF,IAAI,KAAKwF,qBAAqBF,WAAWtF,IAAI,EAAG;AAC5E,QAAMe,eAAewC,eAAe+B,WAAWtF,IAAI;AAEnD,QAAMD,UAAUyD,aAAYiC,eAAA,CAAA,GAAMH,UAAU,GAAIvE,YAAY;AAC5D,QACEf,OAeED,QAfFC,MACAgB,MAcEjB,QAdFiB,KACAC,OAaElB,QAbFkB,MACAG,QAYErB,QAZFqB,OACAI,QAWEzB,QAXFyB,OACAN,UAUEnB,QAVFmB,SACAC,QASEpB,QATFoB,OACAuE,OAQE3F,QARF2F,MACArE,QAOEtB,QAPFsB,OACAC,eAMEvB,QANFuB,cACAC,QAKExB,QALFwB,OACAE,UAIE1B,QAJF0B,SACAE,SAGE5B,QAHF4B,QACAG,UAEE/B,QAFF+B,SACAC,aACEhC,QADFgC;AAGF,QAAMjB,UAAU,IAAIoE,MAAM;MAAElF;MAAMgB;MAAKK;MAAOC;MAAcC;MAAOE;MAASK;MAASC;IAAW,CAAC;AACjG,QAAIjB,QAAQ+C,QAAS,QAAO/C,QAAQ+C;AACpC,QAAM8B,aAAU,WAAA;AAAA,UAAAC,OAAArF,kBAAAC,oBAAAA,QAAAC,KAAG,SAAAoF,UAAA;AAAA,YAAAC,qBAAAC;AAAA,YAAAC,mBAAAnD,UAAAC,oBAAAC,wBAAAC;AAAA,eAAAxC,oBAAAA,QAAAyC,KAAA,SAAAgD,SAAAC,UAAA;AAAA,iBAAA,EAAA,SAAAA,SAAA9C,OAAA8C,SAAA7C,MAAA;YAAA,KAAA;AACjB,eAAAyC,sBAAAhF,QAAQiB,gBAAU,QAAA+D,wBAAA,WAAAC,wBAAlBD,oBAAoB9B,gBAAU,QAAA+B,0BAAA,UAA9BA,sBAAA9B,KAAA6B,qBAAiChF,QAAQ6C,OAAOC,aAAa;AAAEsC,uBAAA7C,OAAA;AAAA,qBACQa,WAAW;gBAChFlD;gBACAC;gBACAkD,MAAM;kBACJhD,OAAOA,SAAS9B,OAAO8B;kBACvBW,SAAShB,QAAQgB;kBACjBsC,WAAWtD,QAAQiB,WAAWqC;kBAC9B7C;gBACF;cACF,CAAC;YAAC,KAAA;AAAAyE,kCAAAE,SAAA7B;AATMxB,yBAAQmD,kBAARnD;AAAUC,mCAAkBkD,kBAAlBlD;AAAoBC,uCAAsBiD,kBAAtBjD;AAAsBmD,uBAAA7C,OAAA;AAAA,qBAUhC8B,iBAAiBrE,SAAS+B,UAAUE,sBAAsB;YAAC,KAAA;AAAjFC,8BAAakD,SAAA7B;AAAA6B,uBAAA7C,OAAA;AAAA,qBACbvC,QAAQgD,OAAO;gBAAE9C;gBAAKI;gBAAOO;gBAAQH;gBAAOqB,UAAUG;gBAAe7B;gBAAOD;cAAQ,CAAC;YAAC,KAAA;AAAA,kBAAA,CACxFwE,MAAI;AAAAQ,yBAAA7C,OAAA;AAAA;cAAA;AAAA6C,uBAAA7C,OAAA;AAAA,qBACAvC,QAAQwD,MAAMxB,kBAAkB;YAAC,KAAA;AAAAoD,uBAAA7C,OAAA;AAAA;YAAA,KAAA;AAAA6C,uBAAA7C,OAAA;AAAA,qBAEjCP,mBAAmB;YAAC,KAAA;YAAA,KAAA;AAAA,qBAAAoD,SAAAd,KAAA;UAAA;QAAA,GAAAS,OAAA;MAAA,CAE7B,CAAA;AAAA,aAAA,SAnBKF,cAAU;AAAA,eAAAC,KAAAvF,MAAA,MAAAC,SAAA;MAAA;IAAA,EAAA;AAoBhBQ,YAAQ+C,UAAU8B,WAAW;EAC/B,CAAC;AACH;AAKO,SAASQ,WAAWC,IAAkB;AAC3C,MAAMtF,UAAUwC,aAAa8C,EAAE;AAC/B,MAAItF,SAAS;AACXA,YAAQ2D,QAAQ;EAClB;AACF;;;;;;;;;;;;;ACtVA4B,EAAAA,uBAAA,SAAAA,uBAAA;AAAA,WAAAC;EAAA;AAAA,MAAAC,GAAAD,IAAA,CAAA,GAAAE,IAAAC,OAAAC,WAAAC,IAAAH,EAAAI,gBAAAC,IAAAJ,OAAAK,kBAAA,SAAAP,IAAAD,IAAAE,IAAA;AAAAD,IAAAA,GAAAD,EAAA,IAAAE,GAAAO;EAAA,GAAAC,IAAA,cAAA,OAAAC,SAAAA,SAAA,CAAA,GAAAC,IAAAF,EAAAG,YAAA,cAAAC,IAAAJ,EAAAK,iBAAA,mBAAAC,IAAAN,EAAAO,eAAA;AAAA,WAAAC,OAAAjB,IAAAD,IAAAE,IAAA;AAAA,WAAAC,OAAAK,eAAAP,IAAAD,IAAA,EAAAS,OAAAP,IAAAiB,YAAA,MAAAC,cAAA,MAAAC,UAAA,KAAA,CAAA,GAAApB,GAAAD,EAAA;EAAA;AAAA,MAAA;AAAAkB,WAAA,CAAA,GAAA,EAAA;EAAA,SAAAjB,IAAA;AAAAiB,aAAA,SAAAA,QAAAjB,IAAAD,IAAAE,IAAA;AAAA,aAAAD,GAAAD,EAAA,IAAAE;IAAA;EAAA;AAAA,WAAAoB,KAAArB,IAAAD,IAAAE,IAAAG,IAAA;AAAA,QAAAK,KAAAV,MAAAA,GAAAI,qBAAAmB,YAAAvB,KAAAuB,WAAAX,KAAAT,OAAAqB,OAAAd,GAAAN,SAAA,GAAAU,KAAA,IAAAW,QAAApB,MAAA,CAAA,CAAA;AAAA,WAAAE,EAAAK,IAAA,WAAA,EAAAH,OAAAiB,iBAAAzB,IAAAC,IAAAY,EAAA,EAAA,CAAA,GAAAF;EAAA;AAAA,WAAAe,SAAA1B,IAAAD,IAAAE,IAAA;AAAA,QAAA;AAAA,aAAA,EAAA0B,MAAA,UAAAC,KAAA5B,GAAA6B,KAAA9B,IAAAE,EAAA,EAAA;IAAA,SAAAD,IAAA;AAAA,aAAA,EAAA2B,MAAA,SAAAC,KAAA5B,GAAA;IAAA;EAAA;AAAAD,IAAAsB,OAAAA;AAAA,MAAAS,KAAA,kBAAAC,IAAA,kBAAAC,IAAA,aAAAC,IAAA,aAAAC,IAAA,CAAA;AAAA,WAAAZ,YAAA;EAAA;AAAA,WAAAa,oBAAA;EAAA;AAAA,WAAAC,6BAAA;EAAA;AAAA,MAAAC,IAAA,CAAA;AAAApB,SAAAoB,GAAA1B,GAAA,WAAA;AAAA,WAAA;EAAA,CAAA;AAAA,MAAA2B,IAAApC,OAAAqC,gBAAAC,IAAAF,KAAAA,EAAAA,EAAAG,OAAA,CAAA,CAAA,CAAA,CAAA;AAAAD,OAAAA,MAAAvC,KAAAG,EAAAyB,KAAAW,GAAA7B,CAAA,MAAA0B,IAAAG;AAAA,MAAAE,IAAAN,2BAAAjC,YAAAmB,UAAAnB,YAAAD,OAAAqB,OAAAc,CAAA;AAAA,WAAAM,sBAAA3C,IAAA;AAAA,KAAA,QAAA,SAAA,QAAA,EAAA4C,QAAA,SAAA7C,IAAA;AAAAkB,aAAAjB,IAAAD,IAAA,SAAAC,IAAA;AAAA,eAAA,KAAA6C,QAAA9C,IAAAC,EAAA;MAAA,CAAA;IAAA,CAAA;EAAA;AAAA,WAAA8C,cAAA9C,IAAAD,IAAA;AAAA,aAAAgD,OAAA9C,IAAAK,IAAAG,IAAAE,IAAA;AAAA,UAAAE,KAAAa,SAAA1B,GAAAC,EAAA,GAAAD,IAAAM,EAAA;AAAA,UAAA,YAAAO,GAAAc,MAAA;AAAA,YAAAZ,KAAAF,GAAAe,KAAAE,KAAAf,GAAAP;AAAA,eAAAsB,MAAA,YAAAkB,SAAAlB,EAAA,KAAA1B,EAAAyB,KAAAC,IAAA,SAAA,IAAA/B,GAAAkD,QAAAnB,GAAAoB,OAAA,EAAAC,KAAA,SAAAnD,IAAA;AAAA+C,iBAAA,QAAA/C,IAAAS,IAAAE,EAAA;QAAA,GAAA,SAAAX,IAAA;AAAA+C,iBAAA,SAAA/C,IAAAS,IAAAE,EAAA;QAAA,CAAA,IAAAZ,GAAAkD,QAAAnB,EAAA,EAAAqB,KAAA,SAAAnD,IAAA;AAAAe,UAAAA,GAAAP,QAAAR,IAAAS,GAAAM,EAAA;QAAA,GAAA,SAAAf,IAAA;AAAA,iBAAA+C,OAAA,SAAA/C,IAAAS,IAAAE,EAAA;QAAA,CAAA;MAAA;AAAAA,MAAAA,GAAAE,GAAAe,GAAA;IAAA;AAAA,QAAA3B;AAAAK,MAAA,MAAA,WAAA,EAAAE,OAAA,SAAAA,MAAAR,IAAAI,IAAA;AAAA,eAAAgD,6BAAA;AAAA,eAAA,IAAArD,GAAA,SAAAA,IAAAE,IAAA;AAAA8C,iBAAA/C,IAAAI,IAAAL,IAAAE,EAAA;QAAA,CAAA;MAAA;AAAA,aAAAA,KAAAA,KAAAA,GAAAkD,KAAAC,4BAAAA,0BAAA,IAAAA,2BAAA;IAAA,EAAA,CAAA;EAAA;AAAA,WAAA3B,iBAAA1B,IAAAE,IAAAG,IAAA;AAAA,QAAAE,KAAAwB;AAAA,WAAA,SAAArB,IAAAE,IAAA;AAAA,UAAAL,OAAA0B,EAAA,OAAAqB,MAAA,8BAAA;AAAA,UAAA/C,OAAA2B,GAAA;AAAA,YAAA,YAAAxB,GAAA,OAAAE;AAAA,eAAA,EAAAH,OAAAR,GAAAsD,MAAA,KAAA;MAAA;AAAA,WAAAlD,GAAAmD,SAAA9C,IAAAL,GAAAwB,MAAAjB,QAAA;AAAA,YAAAE,KAAAT,GAAAoD;AAAA,YAAA3C,IAAA;AAAA,cAAAE,KAAA0C,oBAAA5C,IAAAT,EAAA;AAAA,cAAAW,IAAA;AAAA,gBAAAA,OAAAmB,EAAA;AAAA,mBAAAnB;UAAA;QAAA;AAAA,YAAA,WAAAX,GAAAmD,OAAAnD,CAAAA,GAAAsD,OAAAtD,GAAAuD,QAAAvD,GAAAwB;iBAAA,YAAAxB,GAAAmD,QAAA;AAAA,cAAAjD,OAAAwB,GAAA,OAAAxB,KAAA2B,GAAA7B,GAAAwB;AAAAxB,UAAAA,GAAAwD,kBAAAxD,GAAAwB,GAAA;QAAA,MAAA,cAAAxB,GAAAmD,UAAAnD,GAAAyD,OAAA,UAAAzD,GAAAwB,GAAA;AAAAtB,QAAAA,KAAA0B;AAAA,YAAAK,KAAAX,SAAA3B,IAAAE,IAAAG,EAAA;AAAA,YAAA,aAAAiC,GAAAV,MAAA;AAAA,cAAArB,KAAAF,GAAAkD,OAAArB,IAAAF,GAAAM,GAAAT,QAAAM,EAAA;AAAA,iBAAA,EAAA1B,OAAA6B,GAAAT,KAAA0B,MAAAlD,GAAAkD,KAAA;QAAA;AAAA,oBAAAjB,GAAAV,SAAArB,KAAA2B,GAAA7B,GAAAmD,SAAA,SAAAnD,GAAAwB,MAAAS,GAAAT;MAAA;IAAA;EAAA;AAAA,WAAA6B,oBAAA1D,IAAAE,IAAA;AAAA,QAAAG,KAAAH,GAAAsD,QAAAjD,KAAAP,GAAAa,SAAAR,EAAA;AAAA,QAAAE,OAAAN,EAAA,QAAAC,GAAAuD,WAAA,MAAA,YAAApD,MAAAL,GAAAa,SAAA,QAAA,MAAAX,GAAAsD,SAAA,UAAAtD,GAAA2B,MAAA5B,GAAAyD,oBAAA1D,IAAAE,EAAA,GAAA,YAAAA,GAAAsD,WAAA,aAAAnD,OAAAH,GAAAsD,SAAA,SAAAtD,GAAA2B,MAAA,IAAAkC,UAAA,sCAAA1D,KAAA,UAAA,IAAA8B;AAAA,QAAAzB,KAAAiB,SAAApB,IAAAP,GAAAa,UAAAX,GAAA2B,GAAA;AAAA,QAAA,YAAAnB,GAAAkB,KAAA,QAAA1B,GAAAsD,SAAA,SAAAtD,GAAA2B,MAAAnB,GAAAmB,KAAA3B,GAAAuD,WAAA,MAAAtB;AAAA,QAAAvB,KAAAF,GAAAmB;AAAA,WAAAjB,KAAAA,GAAA2C,QAAArD,GAAAF,GAAAgE,UAAA,IAAApD,GAAAH,OAAAP,GAAA+D,OAAAjE,GAAAkE,SAAA,aAAAhE,GAAAsD,WAAAtD,GAAAsD,SAAA,QAAAtD,GAAA2B,MAAA5B,IAAAC,GAAAuD,WAAA,MAAAtB,KAAAvB,MAAAV,GAAAsD,SAAA,SAAAtD,GAAA2B,MAAA,IAAAkC,UAAA,kCAAA,GAAA7D,GAAAuD,WAAA,MAAAtB;EAAA;AAAA,WAAAgC,aAAAlE,IAAA;AAAA,QAAAD,KAAA,EAAAoE,QAAAnE,GAAA,CAAA,EAAA;AAAA,SAAAA,OAAAD,GAAAqE,WAAApE,GAAA,CAAA,IAAA,KAAAA,OAAAD,GAAAsE,aAAArE,GAAA,CAAA,GAAAD,GAAAuE,WAAAtE,GAAA,CAAA,IAAA,KAAAuE,WAAAC,KAAAzE,EAAA;EAAA;AAAA,WAAA0E,cAAAzE,IAAA;AAAA,QAAAD,KAAAC,GAAA0E,cAAA,CAAA;AAAA3E,IAAAA,GAAA4B,OAAA,UAAA,OAAA5B,GAAA6B,KAAA5B,GAAA0E,aAAA3E;EAAA;AAAA,WAAAyB,QAAAxB,IAAA;AAAA,SAAAuE,aAAA,CAAA,EAAAJ,QAAA,OAAA,CAAA,GAAAnE,GAAA4C,QAAAsB,cAAA,IAAA,GAAA,KAAAS,MAAA,IAAA;EAAA;AAAA,WAAAlC,OAAA1C,IAAA;AAAA,QAAAA,MAAA,OAAAA,IAAA;AAAA,UAAAE,KAAAF,GAAAY,CAAA;AAAA,UAAAV,GAAA,QAAAA,GAAA4B,KAAA9B,EAAA;AAAA,UAAA,cAAA,OAAAA,GAAAiE,KAAA,QAAAjE;AAAA,UAAA,CAAA6E,MAAA7E,GAAA8E,MAAA,GAAA;AAAA,YAAAvE,KAAA,IAAAG,KAAA,SAAAuD,OAAA;AAAA,iBAAA,EAAA1D,KAAAP,GAAA8E,SAAA,KAAAzE,EAAAyB,KAAA9B,IAAAO,EAAA,EAAA,QAAA0D,KAAAxD,QAAAT,GAAAO,EAAA,GAAA0D,KAAAV,OAAA,OAAAU;AAAA,iBAAAA,KAAAxD,QAAAR,GAAAgE,KAAAV,OAAA,MAAAU;QAAA;AAAA,eAAAvD,GAAAuD,OAAAvD;MAAA;IAAA;AAAA,UAAA,IAAAqD,UAAAd,SAAAjD,EAAA,IAAA,kBAAA;EAAA;AAAA,SAAAoC,kBAAAhC,YAAAiC,4BAAA9B,EAAAoC,GAAA,eAAA,EAAAlC,OAAA4B,4BAAAjB,cAAA,KAAA,CAAA,GAAAb,EAAA8B,4BAAA,eAAA,EAAA5B,OAAA2B,mBAAAhB,cAAA,KAAA,CAAA,GAAAgB,kBAAA2C,cAAA7D,OAAAmB,4BAAArB,GAAA,mBAAA,GAAAhB,EAAAgF,sBAAA,SAAA/E,IAAA;AAAA,QAAAD,KAAA,cAAA,OAAAC,MAAAA,GAAAgF;AAAA,WAAA,CAAA,CAAAjF,OAAAA,OAAAoC,qBAAA,yBAAApC,GAAA+E,eAAA/E,GAAAkF;EAAA,GAAAlF,EAAAmF,OAAA,SAAAlF,IAAA;AAAA,WAAAE,OAAAiF,iBAAAjF,OAAAiF,eAAAnF,IAAAoC,0BAAA,KAAApC,GAAAoF,YAAAhD,4BAAAnB,OAAAjB,IAAAe,GAAA,mBAAA,IAAAf,GAAAG,YAAAD,OAAAqB,OAAAmB,CAAA,GAAA1C;EAAA,GAAAD,EAAAsF,QAAA,SAAArF,IAAA;AAAA,WAAA,EAAAkD,SAAAlD,GAAA;EAAA,GAAA2C,sBAAAG,cAAA3C,SAAA,GAAAc,OAAA6B,cAAA3C,WAAAU,GAAA,WAAA;AAAA,WAAA;EAAA,CAAA,GAAAd,EAAA+C,gBAAAA,eAAA/C,EAAAuF,QAAA,SAAAtF,IAAAC,IAAAG,IAAAE,IAAAG,IAAA;AAAA,eAAAA,OAAAA,KAAA8E;AAAA,QAAA5E,KAAA,IAAAmC,cAAAzB,KAAArB,IAAAC,IAAAG,IAAAE,EAAA,GAAAG,EAAA;AAAA,WAAAV,EAAAgF,oBAAA9E,EAAA,IAAAU,KAAAA,GAAAqD,KAAA,EAAAb,KAAA,SAAAnD,IAAA;AAAA,aAAAA,GAAAsD,OAAAtD,GAAAQ,QAAAG,GAAAqD,KAAA;IAAA,CAAA;EAAA,GAAArB,sBAAAD,CAAA,GAAAzB,OAAAyB,GAAA3B,GAAA,WAAA,GAAAE,OAAAyB,GAAA/B,GAAA,WAAA;AAAA,WAAA;EAAA,CAAA,GAAAM,OAAAyB,GAAA,YAAA,WAAA;AAAA,WAAA;EAAA,CAAA,GAAA3C,EAAAyF,OAAA,SAAAxF,IAAA;AAAA,QAAAD,KAAAG,OAAAF,EAAA,GAAAC,KAAA,CAAA;AAAA,aAAAG,MAAAL,GAAAE,CAAAA,GAAAuE,KAAApE,EAAA;AAAA,WAAAH,GAAAwF,QAAA,GAAA,SAAAzB,OAAA;AAAA,aAAA/D,GAAA4E,UAAA;AAAA,YAAA7E,KAAAC,GAAAyF,IAAA;AAAA,YAAA1F,MAAAD,GAAA,QAAAiE,KAAAxD,QAAAR,IAAAgE,KAAAV,OAAA,OAAAU;MAAA;AAAA,aAAAA,KAAAV,OAAA,MAAAU;IAAA;EAAA,GAAAjE,EAAA0C,SAAAA,QAAAjB,QAAArB,YAAA,EAAA6E,aAAAxD,SAAAmD,OAAA,SAAAA,MAAA5E,IAAA;AAAA,QAAA,KAAA4F,OAAA,GAAA,KAAA3B,OAAA,GAAA,KAAAN,OAAA,KAAAC,QAAA3D,GAAA,KAAAsD,OAAA,OAAA,KAAAE,WAAA,MAAA,KAAAD,SAAA,QAAA,KAAA3B,MAAA5B,GAAA,KAAAuE,WAAA3B,QAAA6B,aAAA,GAAA,CAAA1E,GAAA,UAAAE,MAAA,KAAA,SAAAA,GAAA2F,OAAA,CAAA,KAAAxF,EAAAyB,KAAA,MAAA5B,EAAA,KAAA,CAAA2E,MAAA,CAAA3E,GAAA4F,MAAA,CAAA,CAAA,MAAA,KAAA5F,EAAA,IAAAD;EAAA,GAAA8F,MAAA,SAAAA,OAAA;AAAA,SAAAxC,OAAA;AAAA,QAAAtD,KAAA,KAAAuE,WAAA,CAAA,EAAAG;AAAA,QAAA,YAAA1E,GAAA2B,KAAA,OAAA3B,GAAA4B;AAAA,WAAA,KAAAmE;EAAA,GAAAnC,mBAAA,SAAAA,kBAAA7D,IAAA;AAAA,QAAA,KAAAuD,KAAA,OAAAvD;AAAA,QAAAE,KAAA;AAAA,aAAA+F,OAAA5F,IAAAE,IAAA;AAAA,aAAAK,GAAAgB,OAAA,SAAAhB,GAAAiB,MAAA7B,IAAAE,GAAA+D,OAAA5D,IAAAE,OAAAL,GAAAsD,SAAA,QAAAtD,GAAA2B,MAAA5B,IAAA,CAAA,CAAAM;IAAA;AAAA,aAAAA,KAAA,KAAAiE,WAAAM,SAAA,GAAAvE,MAAA,GAAA,EAAAA,IAAA;AAAA,UAAAG,KAAA,KAAA8D,WAAAjE,EAAA,GAAAK,KAAAF,GAAAiE;AAAA,UAAA,WAAAjE,GAAA0D,OAAA,QAAA6B,OAAA,KAAA;AAAA,UAAAvF,GAAA0D,UAAA,KAAAwB,MAAA;AAAA,YAAA9E,KAAAT,EAAAyB,KAAApB,IAAA,UAAA,GAAAM,KAAAX,EAAAyB,KAAApB,IAAA,YAAA;AAAA,YAAAI,MAAAE,IAAA;AAAA,cAAA,KAAA4E,OAAAlF,GAAA2D,SAAA,QAAA4B,OAAAvF,GAAA2D,UAAA,IAAA;AAAA,cAAA,KAAAuB,OAAAlF,GAAA4D,WAAA,QAAA2B,OAAAvF,GAAA4D,UAAA;QAAA,WAAAxD,IAAA;AAAA,cAAA,KAAA8E,OAAAlF,GAAA2D,SAAA,QAAA4B,OAAAvF,GAAA2D,UAAA,IAAA;QAAA,OAAA;AAAA,cAAA,CAAArD,GAAA,OAAAsC,MAAA,wCAAA;AAAA,cAAA,KAAAsC,OAAAlF,GAAA4D,WAAA,QAAA2B,OAAAvF,GAAA4D,UAAA;QAAA;MAAA;IAAA;EAAA,GAAAR,QAAA,SAAAA,OAAA7D,IAAAD,IAAA;AAAA,aAAAE,KAAA,KAAAsE,WAAAM,SAAA,GAAA5E,MAAA,GAAA,EAAAA,IAAA;AAAA,UAAAK,KAAA,KAAAiE,WAAAtE,EAAA;AAAA,UAAAK,GAAA6D,UAAA,KAAAwB,QAAAvF,EAAAyB,KAAAvB,IAAA,YAAA,KAAA,KAAAqF,OAAArF,GAAA+D,YAAA;AAAA,YAAA5D,KAAAH;AAAA;MAAA;IAAA;AAAAG,IAAAA,OAAA,YAAAT,MAAA,eAAAA,OAAAS,GAAA0D,UAAApE,MAAAA,MAAAU,GAAA4D,eAAA5D,KAAA;AAAA,QAAAE,KAAAF,KAAAA,GAAAiE,aAAA,CAAA;AAAA,WAAA/D,GAAAgB,OAAA3B,IAAAW,GAAAiB,MAAA7B,IAAAU,MAAA,KAAA8C,SAAA,QAAA,KAAAS,OAAAvD,GAAA4D,YAAAnC,KAAA,KAAA+D,SAAAtF,EAAA;EAAA,GAAAsF,UAAA,SAAAA,SAAAjG,IAAAD,IAAA;AAAA,QAAA,YAAAC,GAAA2B,KAAA,OAAA3B,GAAA4B;AAAA,WAAA,YAAA5B,GAAA2B,QAAA,eAAA3B,GAAA2B,OAAA,KAAAqC,OAAAhE,GAAA4B,MAAA,aAAA5B,GAAA2B,QAAA,KAAAoE,OAAA,KAAAnE,MAAA5B,GAAA4B,KAAA,KAAA2B,SAAA,UAAA,KAAAS,OAAA,SAAA,aAAAhE,GAAA2B,QAAA5B,OAAA,KAAAiE,OAAAjE,KAAAmC;EAAA,GAAAgE,QAAA,SAAAA,OAAAlG,IAAA;AAAA,aAAAD,KAAA,KAAAwE,WAAAM,SAAA,GAAA9E,MAAA,GAAA,EAAAA,IAAA;AAAA,UAAAE,KAAA,KAAAsE,WAAAxE,EAAA;AAAA,UAAAE,GAAAoE,eAAArE,GAAA,QAAA,KAAAiG,SAAAhG,GAAAyE,YAAAzE,GAAAqE,QAAA,GAAAG,cAAAxE,EAAA,GAAAiC;IAAA;EAAA,GAAA,SAAA,SAAAiE,OAAAnG,IAAA;AAAA,aAAAD,KAAA,KAAAwE,WAAAM,SAAA,GAAA9E,MAAA,GAAA,EAAAA,IAAA;AAAA,UAAAE,KAAA,KAAAsE,WAAAxE,EAAA;AAAA,UAAAE,GAAAkE,WAAAnE,IAAA;AAAA,YAAAI,KAAAH,GAAAyE;AAAA,YAAA,YAAAtE,GAAAuB,MAAA;AAAA,cAAArB,KAAAF,GAAAwB;AAAA6C,wBAAAxE,EAAA;QAAA;AAAA,eAAAK;MAAA;IAAA;AAAA,UAAA+C,MAAA,uBAAA;EAAA,GAAA+C,eAAA,SAAAA,cAAArG,IAAAE,IAAAG,IAAA;AAAA,WAAA,KAAAoD,WAAA,EAAA5C,UAAA6B,OAAA1C,EAAA,GAAAgE,YAAA9D,IAAAgE,SAAA7D,GAAA,GAAA,WAAA,KAAAmD,WAAA,KAAA3B,MAAA5B,IAAAkC;EAAA,EAAA,GAAAnC;AAAA;AAAA,SAAAsG,oBAAAjG,GAAAJ,GAAAD,GAAAE,GAAAK,GAAAK,GAAAE,GAAA;AAAA,MAAA;AAAA,QAAAJ,IAAAL,EAAAO,CAAA,EAAAE,CAAA,GAAAE,IAAAN,EAAAD;EAAA,SAAAJ,IAAA;AAAA,WAAA,KAAAL,EAAAK,EAAA;EAAA;AAAAK,IAAA6C,OAAAtD,EAAAe,CAAA,IAAAwE,QAAAtC,QAAAlC,CAAA,EAAAoC,KAAAlD,GAAAK,CAAA;AAAA;AAAA,SAAAgG,mBAAAlG,GAAA;AAAA,SAAA,WAAA;AAAA,QAAAJ,IAAA,MAAAD,IAAAwG;AAAA,WAAA,IAAAhB,QAAA,SAAAtF,GAAAK,GAAA;AAAA,UAAAK,IAAAP,EAAAoG,MAAAxG,GAAAD,CAAA;AAAA,eAAA0G,MAAArG,IAAA;AAAAiG,QAAAA,oBAAA1F,GAAAV,GAAAK,GAAAmG,OAAAC,QAAA,QAAAtG,EAAA;MAAA;AAAA,eAAAsG,OAAAtG,IAAA;AAAAiG,QAAAA,oBAAA1F,GAAAV,GAAAK,GAAAmG,OAAAC,QAAA,SAAAtG,EAAA;MAAA;AAAAqG,YAAA,MAAA;IAAA,CAAA;EAAA;AAAA;AAEA,IAAME,kBAAkB;EACtB1B,MAAM;EACN2B,OAAO;IACLC,OAAO;MAAElF,MAAMmF;MAAQ,WAAS;IAAG;IACnCC,QAAQ;MAAEpF,MAAMmF;MAAQ,WAAS;IAAG;IACpC7B,MAAM;MAAEtD,MAAMmF;MAAQ,WAAS;IAAG;IAClCE,SAAS;MAAErF,MAAMsF;MAAa,WAASC;IAAU;IACjDC,KAAK;MAAExF,MAAMmF;MAAQ,WAAS;IAAG;IACjCM,MAAM;MAAEzF,MAAM0F;MAAS,WAASH;IAAU;IAC1CI,QAAQ;MAAE3F,MAAMzB;MAAQ,WAASgH;IAAU;IAC3CK,OAAO;MAAE5F,MAAM0F;MAAS,WAASH;IAAU;IAC3CN,OAAO;MAAEjF,MAAMzB;MAAQ,WAASgH;IAAU;IAC1CM,OAAO;MAAE7F,MAAMzB;MAAQ,WAASgH;IAAU;IAC1CO,SAAS;MAAE9F,MAAM+F;MAAU,WAASR;IAAU;IAC9CS,OAAO;MAAEhG,MAAM+F;MAAU,WAASR;IAAU;IAC5CU,OAAO;MAAEjG,MAAM0F;MAAS,WAASH;IAAU;IAC3CW,SAAS;MAAElG,MAAM0F;MAAS,WAASH;IAAU;IAC7CY,SAAS;MAAEnG,MAAMoG;MAAO,WAAS;IAAK;IACtCC,YAAY;MAAErG,MAAM+F;MAAU,WAAS;IAAK;IAC5CO,aAAa;MAAEtG,MAAM+F;MAAU,WAAS;IAAK;IAC7CQ,YAAY;MAAEvG,MAAM+F;MAAU,WAAS;IAAK;IAC5CS,eAAe;MAAExG,MAAM+F;MAAU,WAAS;IAAK;IAC/CU,cAAc;MAAEzG,MAAM+F;MAAU,WAAS;IAAK;IAC9CW,WAAW;MAAE1G,MAAM+F;MAAU,WAAS;IAAK;IAC3CY,aAAa;MAAE3G,MAAM+F;MAAU,WAAS;IAAK;IAC7Ca,WAAW;MAAE5G,MAAM+F;MAAU,WAAS;IAAK;EAC7C;EACAc,MAAI,SAAJA,OAAO;AACL,WAAO;MACLC,eAAelD,QAAQtC,QAAQ;IACjC;EACF;EACAyF,SAAO,SAAPA,UAAU;AAAA,QAAAC,QAAA;AACRC,QAAIC,OAAO,KAAKC,UAAU;AAC1B,SAAKC,aAAa;AAClB,SAAKC,OACH,WAAA;AAAA,aAAML,MAAK1D,OAAO0D,MAAKxB;IAAG,GAC1B,WAAA;AAAA,aAAMwB,MAAKI,aAAa;IAAC,CAC3B;EACF;EACAE,SAAS;IACPH,YAAU,SAAVA,WAAWI,OAAgB;AAAA,eAAAC,OAAA5C,UAAA1B,QAANuE,OAAI,IAAArB,MAAAoB,OAAA,IAAAA,OAAA,IAAA,CAAA,GAAAE,OAAA,GAAAA,OAAAF,MAAAE,QAAA;AAAJD,aAAIC,OAAA,CAAA,IAAA9C,UAAA8C,IAAA;MAAA;AACvB,WAAKC,MAAK9C,MAAV,MAAI,CAAO0C,KAAK,EAAAK,OAAKH,IAAI,CAAA;IAC3B;IACMI,UAAQ,SAARA,YAAW;AAAA,UAAAC,SAAA;AAAA,aAAAnD,mBAAAxG,qBAAA,EAAAoF,KAAA,SAAAwE,UAAA;AAAA,eAAA5J,qBAAA,EAAAuB,KAAA,SAAAsI,SAAAC,UAAA;AAAA,iBAAA,EAAA,SAAAA,SAAAjE,OAAAiE,SAAA5F,MAAA;YAAA,KAAA;AAAA4F,uBAAAjE,OAAA;AAAAiE,uBAAA5F,OAAA;AAAA,qBAEP6F,SAAY;gBAChB5E,MAAMwE,OAAKxE;gBACXkC,KAAKsC,OAAKtC;gBACV2C,IAAIL,OAAKM,MAAMC;gBACfhD,SAASyC,OAAKzC;gBACdO,OAAOkC,OAAKlC;gBACZI,OAAO8B,OAAK9B;gBACZf,OAAO6C,OAAK7C;gBACZY,OAAOiC,OAAKjC;gBACZC,SAASgC,OAAKhC;gBACdL,MAAMqC,OAAKrC;gBACXE,QAAQmC,OAAKnC;gBACbM,OAAO6B,OAAK7B;gBACZC,SAAS4B,OAAK5B;gBACdC,SAAS2B,OAAK3B;gBACdE,YAAYyB,OAAKzB;gBACjBC,aAAawB,OAAKxB;gBAClBC,YAAYuB,OAAKvB;gBACjBC,eAAesB,OAAKtB;gBACpBC,cAAcqB,OAAKrB;gBACnBC,WAAWoB,OAAKpB;gBAChBC,aAAamB,OAAKnB;gBAClBC,WAAWkB,OAAKlB;cAClB,CAAC;YAAC,KAAA;AAAAqB,uBAAA5F,OAAA;AAAA;YAAA,KAAA;AAAA4F,uBAAAjE,OAAA;AAAAiE,uBAAAK,KAAAL,SAAA,OAAA,EAAA,CAAA;AAEFM,sBAAQC,IAAGP,SAAAK,EAAM;YAAE,KAAA;YAAA,KAAA;AAAA,qBAAAL,SAAA9D,KAAA;UAAA;QAAA,GAAA4D,SAAA,MAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA;MAAA,CAAA,CAAA,EAAA;IAEvB;IACAX,cAAY,SAAZA,eAAe;AACb,WAAKN,gBAAgB,KAAKA,cAActF,KAAK,KAAKqG,QAAQ;IAC5D;IACAY,SAAO,SAAPA,UAAU;AACRC,iBAAW,KAAKpF,IAAI;IACtB;EACF;EACAqF,eAAa,SAAbA,gBAAgB;AACd1B,QAAI2B,QAAQ,KAAKzB,UAAU;EAC7B;EACA0B,QAAM,SAANA,SAAS;AACP,WAAO1I,EAAE,OAAO;MACd2I,OAAO;QACL5D,OAAO,KAAKA;QACZE,QAAQ,KAAKA;MACf;MACA2D,KAAK;IACP,CAAC;EACH;AACF;AAEA,IAAMC,WAAWC,gBAAgBjE,eAAe;AAEhDgE,SAASE,WAAWA;AACpBF,SAASG,aAAaA;AACtBH,SAAS/B,MAAMA;AACf+B,SAASN,aAAaA;AACtBM,SAASI,UAAU,SAAUC,KAAK;AAChCA,MAAIC,UAAU,YAAYN,QAAQ;AACpC;AAEA,IAAA,cAAeA;", "names": ["_typeof", "o", "_typeof", "_regeneratorRuntime", "t", "e", "r", "define", "n", "i", "a", "c", "h", "o", "u", "p", "n", "r", "WUJIE_APP_ID", "WUJIE_SCRIPT_ID", "WUJIE_DATA_FLAG", "CONTAINER_POSITION_DATA_FLAG", "CONTAINER_OVERFLOW_DATA_FLAG", "LOADING_DATA_FLAG", "WUJIE_DATA_ATTACH_CSS_FLAG", "WUJIE_IFRAME_CLASS", "WUJIE_ALL_EVENT", "WUJIE_SHADE_STYLE", "WUJIE_LOADING_STYLE", "WUJIE_LOADING_SVG", "WUJIE_TIPS_NO_URL", "WUJIE_TIPS_RELOAD_DISABLED", "WUJIE_TIPS_STOP_APP", "WUJIE_TIPS_STOP_APP_DETAIL", "WUJIE_TIPS_NO_SUBJECT", "WUJIE_TIPS_NO_FETCH", "WUJIE_TIPS_NOT_SUPPORTED", "WUJIE_TIPS_SCRIPT_ERROR_REQUESTED", "WUJIE_TIPS_CSS_ERROR_REQUESTED", "WUJIE_TIPS_HTML_ERROR_REQUESTED", "WUJIE_TIPS_REPEAT_RENDER", "WUJIE_TIPS_NO_SCRIPT", "WUJIE_TIPS_GET_ELEMENT_BY_ID", "isFunction", "value", "isHijackingTag", "tagName", "toUpperCase", "wujieSupport", "window", "Proxy", "CustomElementRegistry", "naughty<PERSON><PERSON><PERSON>", "document", "all", "callableFnCacheMap", "WeakMap", "isCallable", "fn", "has", "callable", "set", "boundedMap", "isBoundedFunction", "get", "bounded", "name", "indexOf", "hasOwnProperty", "fnRegexCheckCacheMap", "isConstructable", "hasPrototypeMethods", "prototype", "constructor", "Object", "getOwnPropertyNames", "length", "constructable", "fnString", "toString", "constructableFunctionRegex", "classRegex", "test", "setFnCacheMap", "checkProxyFunction", "target", "getTargetValue", "p", "boundValue", "Function", "bind", "call", "key", "defineProperty", "enumerable", "writable", "getDegradeIframe", "id", "querySelector", "concat", "WUJIE_APP_ID", "setAttrsToElement", "element", "attrs", "keys", "for<PERSON>ach", "setAttribute", "appRouteParse", "url", "error", "WUJIE_TIPS_NO_URL", "Error", "urlElement", "anchorElementGenerator", "appHostPath", "protocol", "host", "appRoutePath", "pathname", "search", "hash", "startsWith", "createElement", "href", "getAnchorElementQueryMap", "anchorElement", "queryString", "_toConsumableArray", "URLSearchParams", "entries", "reduce", "c", "isMatchSyncQueryById", "queryMap", "location", "includes", "fixElementCtrSrcOrHref", "iframeWindow", "elementCtr", "attr", "rawElementSetAttribute", "Element", "targetValue", "getAbsolutePath", "baseURI", "rawAnchorElementHrefDescriptor", "getOwnPropertyDescriptor", "configurable", "getCurUrl", "proxyLocation", "base", "URL", "_unused", "getSyncUrl", "prefix", "_syncUrl$match", "winUrlElement", "syncUrl", "decodeURIComponent", "validShortPath", "match", "replace", "requestIdleCallback", "cb", "setTimeout", "getContainer", "container", "warn", "msg", "data", "_console", "console", "_console2", "getInlineCode", "start", "end", "lastIndexOf", "substring", "defaultGetPublicPath", "entry", "_typeof", "_URL", "origin", "paths", "split", "pop", "join", "e", "compose", "fnList", "code", "_len", "arguments", "args", "Array", "_key", "newCode", "apply", "nextTick", "Promise", "resolve", "then", "execHooks", "plugins", "<PERSON><PERSON><PERSON>", "_len2", "_key2", "map", "plugin", "filter", "hook", "isScriptElement", "_element$tagName", "count", "setTagToScript", "tag", "scriptTag", "String", "WUJIE_SCRIPT_ID", "getTagFromScript", "getAttribute", "mergeOptions", "options", "cacheOptions", "el", "html", "exec", "undefined", "fetch", "props", "sync", "loading", "degradeAttrs", "fiber", "alive", "degrade", "lifecycles", "beforeLoad", "beforeMount", "afterMount", "beforeUnmount", "afterUnmount", "activated", "deactivated", "loadError", "eventTrigger", "eventName", "detail", "event", "CustomEvent", "createEvent", "initCustomEvent", "dispatchEvent", "stopMainAppRun", "WUJIE_TIPS_STOP_APP_DETAIL", "WUJIE_TIPS_STOP_APP", "ALL_SCRIPT_REGEX", "SCRIPT_TAG_REGEX", "SCRIPT_SRC_REGEX", "SCRIPT_TYPE_REGEX", "SCRIPT_ENTRY_REGEX", "SCRIPT_ASYNC_REGEX", "DEFER_ASYNC_REGEX", "SCRIPT_NO_MODULE_REGEX", "SCRIPT_MODULE_REGEX", "LINK_TAG_REGEX", "LINK_PRELOAD_OR_PREFETCH_REGEX", "LINK_HREF_REGEX", "LINK_AS_FONT", "STYLE_TAG_REGEX", "STYLE_TYPE_REGEX", "STYLE_HREF_REGEX", "HTML_COMMENT_REGEX", "LINK_IGNORE_REGEX", "STYLE_IGNORE_REGEX", "SCRIPT_IGNORE_REGEX", "CROSS_ORIGIN_REGEX", "hasProtocol", "url", "startsWith", "getEntirePath", "path", "baseURI", "URL", "toString", "isValidJavaScriptType", "type", "handleTypes", "indexOf", "parseTagAttributes", "TagOuterHTML", "pattern", "matches", "exec", "attributesString", "attributesPattern", "attributesObject", "attributeMatches", "attributeName", "attributeValue", "isModuleScriptSupported", "s", "window", "document", "createElement", "genLinkReplaceSymbol", "linkHref", "preloadOrPrefetch", "arguments", "length", "undefined", "concat", "getInlineStyleReplaceSymbol", "index", "genScriptReplaceSymbol", "scriptSrc", "inlineScriptReplaceSymbol", "genIgnoreAssetReplaceSymbol", "genModuleScriptReplaceSymbol", "moduleSupport", "tpl", "postProcessTemplate", "scripts", "styles", "entry", "template", "replace", "match", "styleType", "styleHref", "styleIgnore", "href", "newHref", "push", "src", "preloadOrPrefetchType", "_match$match", "_match$match2", "_slicedToArray", "test", "code", "getInlineCode", "content", "scriptTag", "scriptIgnore", "isModuleScript", "isCrossOriginScript", "crossOriginType", "moduleScriptIgnore", "matchedScriptTypeMatch", "matchedScriptType", "matchedScriptEntry", "matchedScriptSrcMatch", "matchedScriptSrc", "SyntaxError", "isAsyncScript", "isDeferScript", "async", "defer", "module", "crossorigin", "crossoriginType", "attrs", "isPureCommentBlock", "split", "every", "line", "trim", "tplResult", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "plugins", "replace", "code", "src", "arguments", "length", "undefined", "base", "compose", "map", "plugin", "cssL<PERSON>der", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref2", "j<PERSON><PERSON><PERSON><PERSON>", "getPresetLoaders", "loaderType", "loaders", "filter", "res", "reduce", "preLoaders", "curLoa<PERSON>", "concat", "reverse", "getEffectLoaders", "isMatchUrl", "url", "effectLoaders", "some", "loader", "test", "cssRelativePathResolve", "baseUrl", "getAbsolutePath", "urlReg", "_m", "pre", "post", "base64Regx", "isBase64", "defaultPlugin", "cssBeforeLoaders", "content", "getPlugins", "Array", "isArray", "_toConsumableArray", "styleCache", "scriptCache", "embedHTMLCache", "window", "fetch", "error", "WUJIE_TIPS_NO_FETCH", "Error", "defaultFetch", "bind", "defaultGetTemplate", "tpl", "processCssLoader", "_x", "_x2", "_x3", "_processCssLoader", "apply", "arguments", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "sandbox", "template", "getExternalStyleSheets", "curUrl", "composeCssLoader", "processedCssList", "embedHTML", "wrap", "_callee$", "_context", "prev", "next", "getCurUrl", "proxyLocation", "compose", "plugins", "map", "plugin", "cssL<PERSON>der", "_ref2", "src", "ignore", "contentPromise", "then", "content", "getEmbedHTML", "sent", "abrupt", "replace", "stop", "_x4", "_x5", "_getEmbedHTML", "_callee2", "styleResultList", "_callee2$", "_context2", "Promise", "all", "styleResult", "index", "genLinkReplaceSymbol", "concat", "getInlineStyleReplaceSymbol", "isInlineCode", "code", "startsWith", "fetchAssets", "cache", "cssFlag", "loadError", "response", "status", "WUJIE_TIPS_CSS_ERROR_REQUESTED", "WUJIE_TIPS_SCRIPT_ERROR_REQUESTED", "text", "e", "styles", "length", "undefined", "_ref", "resolve", "getInlineCode", "getExternalScripts", "scripts", "fetch", "arguments", "length", "undefined", "defaultFetch", "loadError", "fiber", "map", "script", "src", "async", "defer", "module", "ignore", "contentPromise", "Promise", "resolve", "reject", "requestIdleCallback", "fetchAssets", "scriptCache", "then", "content", "_objectSpread", "params", "_opts$fetch", "_opts$fiber", "url", "opts", "html", "fetch", "defaultFetch", "fiber", "plugins", "loadError", "htmlLoader", "compose", "map", "plugin", "defaultGetTemplate", "jsExcludes", "getEffectLoaders", "cssExcludes", "jsIgnores", "cssIgnores", "getPublicPath", "defaultGetPublicPath", "getHtmlParseResult", "Promise", "resolve", "then", "response", "status", "error", "WUJIE_TIPS_HTML_ERROR_REQUESTED", "Error", "text", "e", "embedHTMLCache", "reject", "assetPublicPath", "_processTpl", "processTpl", "template", "scripts", "styles", "getExternalScripts", "filter", "script", "src", "isMatchUrl", "_objectSpread", "ignore", "getExternalStyleSheets", "style", "some", "t", "t", "e", "t", "_isNativeReflectConstruct", "_wrapNativeSuper", "t", "idToSandboxCacheMap", "window", "__POWERED_BY_WUJIE__", "__WU<PERSON><PERSON>", "inject", "idToSandboxMap", "Map", "getWujieById", "id", "_idToSandboxCacheMap$", "get", "wujie", "getOptionsById", "_idToSandboxCacheMap$2", "options", "addSandboxCacheWithWujie", "sandbox", "w<PERSON><PERSON><PERSON><PERSON><PERSON>", "set", "_objectSpread", "deleteWujieById", "addSandboxCacheWithOptions", "documentProxyProperties", "modifyLocalProperties", "modifyProperties", "shadowProperties", "shadowMethods", "documentProperties", "documentMethods", "documentEvents", "ownerProperties", "appDocumentAddEventListenerEvents", "appDocumentOnEvents", "mainDocumentAddEventListenerEvents", "mainAndAppAddEventListenerEvents", "appWindowAddEventListenerEvents", "appWindowOnEvent", "relativeElementTagAttrMap", "IMG", "A", "SOURCE", "windowProxyProperties", "windowRegWhiteList", "rawElementAppendChild", "HTMLElement", "prototype", "append<PERSON><PERSON><PERSON>", "rawElementRemoveChild", "<PERSON><PERSON><PERSON><PERSON>", "rawElementContains", "contains", "rawHeadInsertBefore", "HTMLHeadElement", "insertBefore", "rawBodyInsertBefore", "HTMLBodyElement", "rawAddEventListener", "Node", "addEventListener", "rawRemoveEventListener", "removeEventListener", "rawWindowAddEventListener", "rawWindowRemoveEventListener", "rawAppendChild", "rawDocumentQuerySelector", "__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__", "Document", "querySelector", "patchCustomEvent", "e", "elementGetter", "Object", "defineProperties", "srcElement", "get", "target", "manualInvokeElementEvent", "element", "event", "customEvent", "CustomEvent", "patchedEvent", "isFunction", "concat", "dispatchEvent", "handleStylesheetElementPatch", "stylesheetElement", "sandbox", "innerHTML", "degrade", "patcher", "_getPatchStyleElement", "getPatchStyleElements", "sheet", "_getPatchStyleElement2", "_slicedToArray", "hostStyleSheetElement", "fontStyleSheetElement", "shadowRoot", "head", "append<PERSON><PERSON><PERSON>", "host", "_patcher", "undefined", "clearTimeout", "setTimeout", "patchStylesheetElement", "cssL<PERSON>der", "curUrl", "_stylesheetElement$sh", "_hasPatchStyle", "innerHTMLDesc", "getOwnPropertyDescriptor", "Element", "prototype", "innerTextDesc", "HTMLElement", "textContentDesc", "Node", "RawInsertRule", "insertRule", "patchSheetInsertRule", "rule", "index", "innerText", "call", "set", "code", "_this", "nextTick", "_this2", "textContent", "_this3", "value", "node", "_this4", "nodeType", "TEXT_NODE", "res", "rawAppendChild", "ownerDocument", "createTextNode", "dynamicScriptExecStack", "Promise", "resolve", "rewriteAppendOrInsertChild", "opts", "appendChildOrInsertBefore", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "_this5", "rawDOMAppendOrInsertBefore", "wujieId", "getWujieById", "styleSheetElements", "replace", "fetch", "plugins", "iframe", "lifecycles", "proxyLocation", "fiber", "isHijackingTag", "tagName", "patchElementEffect", "contentWindow", "execHooks", "iframeDocument", "contentDocument", "getCurUrl", "_element$tagName", "toUpperCase", "_ref", "href", "rel", "type", "styleFlag", "endsWith", "isMatchUrl", "getEffectLoaders", "getExternalStyleSheets", "src", "ignore", "loadError", "for<PERSON>ach", "_ref2", "contentPromise", "then", "content", "rawAttrs", "parseTagAttributes", "outerHTML", "createElement", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "push", "setAttrsToElement", "comment", "createComment", "setTagToScript", "_ref3", "text", "crossOrigin", "execScript", "scriptResult", "warn", "WUJIE_TIPS_REPEAT_RENDER", "onload", "insertScriptToIframe", "_objectSpread", "scriptOptions", "module", "crossorigin", "crossoriginType", "attrs", "getExternalScripts", "_sandbox$execQueue", "execQueue", "exec<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "requestIdleCallback", "shift", "_sandbox$execQueue2", "getAttribute", "WUJIE_DATA_FLAG", "rawDocumentQuerySelector", "findScriptElementFromIframe", "rawElement", "wujie<PERSON>ag", "getTagFromScript", "targetScript", "__WUJIE_RAW_DOCUMENT_HEAD__", "querySelector", "WUJIE_SCRIPT_ID", "WUJIE_TIPS_NO_SCRIPT", "rewriteContains", "contains", "other", "rawElementContains", "isScriptElement", "_findScriptElementFro", "rewriteRemoveChild", "<PERSON><PERSON><PERSON><PERSON>", "child", "rawElementRemoveChild", "_findScriptElementFro2", "patchEventListener", "listenerMap", "Map", "_cacheListeners", "addEventListener", "listener", "options", "listeners", "_toConsumableArray", "rawAddEventListener", "removeEventListener", "typeListeners", "indexOf", "splice", "rawRemoveEventListener", "entries", "_ref4", "_ref5", "patchRenderEffect", "render", "id", "body", "insertBefore", "rawHeadInsertBefore", "bind", "rawBodyInsertBefore", "cssSelectorMap", "defineWujieWebComponent", "customElements", "window", "get", "WujieApp", "_HTMLElement", "_classCallCheck", "_callSuper", "arguments", "_inherits", "_createClass", "key", "value", "connectedCallback", "shadowRoot", "attachShadow", "mode", "sandbox", "getWujieById", "getAttribute", "WUJIE_APP_ID", "patchElementEffect", "iframe", "contentWindow", "disconnectedCallback", "unmount", "_wrapNativeSuper", "HTMLElement", "define", "createWujieWebComponent", "id", "contentElement", "document", "createElement", "setAttribute", "classList", "add", "WUJIE_IFRAME_CLASS", "renderElementToContainer", "element", "selectorOrElement", "container", "getContainer", "contains", "querySelector", "concat", "LOADING_DATA_FLAG", "<PERSON><PERSON><PERSON><PERSON>", "rawElementAppendChild", "call", "initRenderIframeAndContainer", "parent", "degradeAttrs", "length", "undefined", "createIframeContainer", "contentDocument", "open", "write", "close", "processCssLoaderForTemplate", "_x", "_x2", "_processCssLoaderForTemplate", "apply", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "html", "plugins", "replace", "proxyLocation", "cssL<PERSON>der", "cssBeforeLoaders", "cssAfterLoaders", "curUrl", "wrap", "_callee$", "_context", "prev", "next", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "getPresetLoaders", "getCurUrl", "Promise", "all", "getExternalStyleSheets", "fetch", "lifecycles", "loadError", "map", "_ref", "src", "contentPromise", "then", "content", "contentList", "for<PERSON>ach", "_ref2", "styleElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "body", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "_ref3", "_ref4", "abrupt", "sent", "stop", "replaceHeadAndBody", "headElement", "bodyElement", "rawAppendChild", "cloneNode", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "renderTemplateToHtml", "iframeWindow", "template", "__WU<PERSON><PERSON>", "alive", "execFlag", "parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedDocument", "parseFromString", "parsedHtml", "documentElement", "sourceAttributes", "attributes", "innerHTML", "i", "name", "ElementIterator", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "nextElement", "currentNode", "relativeAttr", "relativeElementTagAttrMap", "tagName", "url", "getAbsolutePath", "baseURI", "nextNode", "renderTemplateToShadowRoot", "_x3", "_x4", "_x5", "_renderTemplateToShadowRoot", "_callee2", "processedHtml", "shade", "_callee2$", "_context2", "WUJIE_SHADE_STYLE", "Object", "defineProperty", "enumerable", "configurable", "patchRenderEffect", "defaultStyle", "setAttrsToElement", "_objectSpread", "_defineProperty", "style", "join", "renderTemplateToIframe", "_x6", "_x7", "_x8", "_renderTemplateToIframe", "_callee3", "renderDocument", "_callee3$", "_context3", "root", "rawElementRemoveChild", "addLoading", "el", "loading", "containerStyles", "getComputedStyle", "_unused", "position", "CONTAINER_POSITION_DATA_FLAG", "CONTAINER_OVERFLOW_DATA_FLAG", "overflow", "setProperty", "includes", "loadingContainer", "WUJIE_LOADING_STYLE", "WUJIE_LOADING_SVG", "removeLoading", "positionFlag", "overflowFlag", "removeProperty", "removeAttribute", "getPatchStyleElements", "rootStyleSheets", "rootCssRules", "fontCssRules", "rootStyleReg", "_rootStyleSheets$i$cs", "_rootStyleSheets$i", "cssRules", "j", "cssRuleText", "cssText", "test", "push", "match", "type", "CSSRule", "FONT_FACE_RULE", "rootStyleSheetElement", "fontStyleSheetElement", "syncUrlToWindow", "iframeWindow", "_iframeWindow$__WUJIE", "__WU<PERSON><PERSON>", "sync", "id", "prefix", "winUrlElement", "anchorElementGenerator", "window", "location", "href", "queryMap", "getAnchorElementQueryMap", "curUrl", "pathname", "search", "hash", "validShortPath", "Object", "keys", "for<PERSON>ach", "shortPath", "<PERSON><PERSON><PERSON>", "startsWith", "length", "encodeURIComponent", "replace", "concat", "<PERSON><PERSON><PERSON><PERSON>", "map", "key", "join", "history", "replaceState", "syncUrlToIframe", "_iframeWindow$locatio", "_iframeWindow$__WUJIE2", "url", "execFlag", "inject", "idUrl", "getSyncUrl", "syncUrl", "test", "_appRouteParse", "appRouteParse", "appRoutePath", "preAppRoutePath", "mainHostPath", "clearInactiveAppUrl", "sandbox", "getWujieById", "hrefFlag", "activeFlag", "pushUrlToWindow", "pushState", "processAppForHrefJump", "addEventListener", "filter", "iframeBody", "rawDocumentQuerySelector", "call", "iframe", "contentDocument", "degrade", "renderElementToContainer", "document", "documentElement", "renderIframeReplaceApp", "decodeURIComponent", "getDegradeIframe", "parentElement", "degradeAttrs", "shadowRoot", "host", "_initRenderIframeAndC", "initRenderIframeAndContainer", "el", "patchEventTimeStamp", "contentWindow", "onunload", "unmount", "append<PERSON><PERSON><PERSON>", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "patchIframeEvents", "iframeWindow", "__WUJIE_EVENTLISTENER__", "Set", "addEventListener", "type", "listener", "options", "execHooks", "__WU<PERSON><PERSON>", "plugins", "add", "appWindowAddEventListenerEvents", "includes", "_typeof", "targetWindow", "rawWindowAddEventListener", "call", "window", "__WUJIE_RAW_WINDOW__", "removeEventListener", "for<PERSON>ach", "o", "rawWindowRemoveEventListener", "patchIframeVariable", "wujie", "appHostPath", "__WUJIE_PUBLIC_PATH__", "$wujie", "provide", "patchIframeHistory", "mainHostPath", "history", "rawHistoryPushState", "pushState", "rawHistoryReplaceState", "replaceState", "data", "title", "url", "baseUrl", "location", "pathname", "search", "hash", "mainUrl", "getAbsolutePath", "replace", "ignoreFlag", "undefined", "updateBase", "syncUrlToWindow", "_iframeWindow$locatio", "URL", "href", "baseElement", "rawDocumentQuerySelector", "document", "setAttribute", "patchWindowEffect", "processWindowProperty", "key", "value", "isConstructable", "bind", "e", "warn", "message", "Object", "getOwnPropertyNames", "defineProperty", "get", "windowProxyProperties", "windowRegWhiteList", "some", "reg", "test", "parent", "windowOnEvents", "filter", "p", "appWindowOnEvent", "descriptor", "getOwnPropertyDescriptor", "enumerable", "writable", "configurable", "set", "handler", "recordEventListeners", "sandbox", "Node", "prototype", "elementListenerList", "elementEventCacheMap", "find", "push", "rawAddEventListener", "index", "findIndex", "ele", "splice", "length", "rawRemoveEventListener", "recoverEventListeners", "rootElement", "WeakMap", "ElementIterator", "createTreeWalker", "Node<PERSON><PERSON><PERSON>", "SHOW_ELEMENT", "nextElement", "currentNode", "nextNode", "recoverDocumentListeners", "oldRootElement", "newRootElement", "patchEventTimeStamp", "Event", "createEvent", "timeStamp", "patchDocumentEffect", "handlerCallbackMap", "handlerTypeMap", "Document", "callback", "typeList", "appDocumentAddEventListenerEvents", "degrade", "mainDocumentAddEventListenerEvents", "mainAndAppAddEventListenerEvents", "shadowRoot", "indexOf", "elementOnEvents", "keys", "HTMLElement", "documentOnEvent", "appDocumentOnEvents", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "val", "ownerProperties", "documentProxyProperties", "modifyProperties", "shadowProperties", "shadowMethods", "documentProperties", "documentMethods", "documentEvents", "concat", "<PERSON><PERSON><PERSON>", "proxyDocument", "patchNodeEffect", "rawGetRootNode", "getRootNode", "rawAppendChild", "append<PERSON><PERSON><PERSON>", "rawInsertRule", "insertBefore", "rawRemoveChild", "<PERSON><PERSON><PERSON><PERSON>", "rootNode", "node", "res", "patchElementEffect", "child", "_node$parentNode", "console", "nodeName", "toLowerCase", "isConnected", "isFunction", "parentNode", "patchRelativeUrlEffect", "fixElementCtrSrcOrHref", "HTMLImageElement", "HTMLAnchorElement", "HTMLSourceElement", "HTMLLinkElement", "HTMLScriptElement", "HTMLMediaElement", "initBase", "iframeDocument", "createElement", "iframeUrlElement", "anchorElementGenerator", "appUrlElement", "protocol", "host", "head", "initIframeDom", "newDoc", "implementation", "createHTMLDocument", "newDocumentElement", "importNode", "documentElement", "<PERSON><PERSON><PERSON><PERSON>", "__WUJIE_RAW_DOCUMENT_HEAD__", "__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__", "querySelector", "__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR_ALL__", "querySelectorAll", "__WUJIE_RAW_DOCUMENT_CREATE_ELEMENT__", "__WUJIE_RAW_DOCUMENT_CREATE_TEXT_NODE__", "createTextNode", "syncIframeUrlToWindow", "stopIframeLoading", "iframe", "useObjectURL", "contentWindow", "oldDoc", "Promise", "resolve", "loop", "setTimeout", "err", "open", "close", "deadline", "Date", "now", "loop2", "disableSandboxEmptyPageURL", "src", "then", "stop", "execCommand", "element", "proxyLocation", "_hasPatch", "defineProperties", "baseURI", "ownerDocument", "error", "insertScriptToIframe", "scriptResult", "rawElement", "_ref", "module", "content", "crossorigin", "crossoriginType", "async", "attrs", "onload", "scriptElement", "nextScriptElement", "_iframeWindow$__WUJIE", "j<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "code", "getCurUrl", "String", "textContent", "container", "execNextScript", "afterExecScript", "WUJIE_TIPS_SCRIPT_ERROR_REQUESTED", "setTagToScript", "getTagFromScript", "isOutlineScript", "onerror", "renderIframeReplaceApp", "degradeAttrs", "arguments", "defaultStyle", "setAttrsToElement", "_objectSpread", "style", "join", "renderElementToContainer", "_ref2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "localStorage", "getItem", "createObjectURL", "prevURL", "getSandboxEmptyPageURL", "blob", "Blob", "setItem", "_ref3", "_slicedToArray", "iframeGenerator", "appRoutePath", "attrsMerge", "_defineProperty", "name", "id", "WUJIE_DATA_FLAG", "body", "iframeReady", "isMatchSyncQueryById", "locationHrefSet", "iframe", "value", "appHostPath", "_iframe$contentWindow", "contentWindow", "__WU<PERSON><PERSON>", "shadowRoot", "id", "degrade", "document", "degradeAttrs", "url", "test", "hrefElement", "anchorElementGenerator", "pathname", "search", "hash", "hrefFlag", "iframeBody", "rawDocumentQuerySelector", "call", "contentDocument", "renderElementToContainer", "documentElement", "renderIframeReplaceApp", "window", "decodeURIComponent", "getDegradeIframe", "parentElement", "host", "pushUrlToWindow", "proxyGenerator", "urlElement", "mainHostPath", "proxyWindow", "Proxy", "get", "target", "p", "proxyLocation", "Object", "getOwnPropertyDescriptor", "proxy", "descriptor", "configurable", "writable", "getTargetValue", "set", "checkProxyFunction", "has", "proxyDocument", "_fakeDocument", "<PERSON><PERSON><PERSON>", "_iframe$contentWindow2", "stopMainAppRun", "rawCreateElement", "__WUJIE_RAW_DOCUMENT_CREATE_ELEMENT__", "rawCreateTextNode", "__WUJIE_RAW_DOCUMENT_CREATE_TEXT_NODE__", "apply", "_createElement", "_ctx", "args", "rawCreateMethod", "element", "patchElementEffect", "href", "querySelectorAll", "arg", "scripts", "concat", "res", "error", "querySelector", "ctx", "_ctx$propKey", "__WUJIE_RAW_DOCUMENT_QUERY_SELECTOR__", "warn", "WUJIE_TIPS_GET_ELEMENT_BY_ID", "rawPropMap", "_ctx$propKey2", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "ownerProperties", "documentProxyProperties", "shadowProperties", "shadowMethods", "documentProperties", "documentMethods", "includes", "toString", "activeElement", "body", "_getTargetValue", "_fakeLocation", "location", "replace", "WUJIE_TIPS_RELOAD_DISABLED", "_args$", "ownKeys", "keys", "filter", "key", "_target", "enumerable", "localGenerator", "sandbox", "defineProperties", "createElement", "_len", "arguments", "length", "Array", "_key", "createTextNode", "_len2", "_key2", "documentURI", "URL", "getElementsByTagName", "tagName", "undefined", "getElementById", "modifyLocalProperties", "modifyProperties", "for<PERSON>ach", "defineProperty", "_sandbox$document", "isCallable", "bind", "locationKeys", "<PERSON><PERSON><PERSON>", "reload", "appEventObjMap", "window", "__POWERED_BY_WUJIE__", "__WU<PERSON><PERSON>", "inject", "Map", "EventBus", "id", "_classCallCheck", "$clear", "get", "set", "eventObj", "_createClass", "key", "value", "$on", "event", "fn", "cbs", "includes", "push", "$onAll", "WUJIE_ALL_EVENT", "$once", "on", "$off", "apply", "arguments", "bind", "length", "warn", "concat", "WUJIE_TIPS_NO_SUBJECT", "cb", "i", "splice", "$offAll", "$emit", "allCbs", "for<PERSON>ach", "_len", "args", "Array", "_key", "l", "_cbs", "_allCbs", "e", "error", "_appEventObjMap$get", "events", "Object", "keys", "<PERSON><PERSON><PERSON>", "options", "_classCallCheck", "_defineProperty", "WeakMap", "window", "__POWERED_BY_WUJIE__", "inject", "__WU<PERSON><PERSON>", "idToSandboxMap", "idToSandboxCacheMap", "appEventObjMap", "mainHostPath", "location", "protocol", "host", "name", "url", "attrs", "fiber", "degradeAttrs", "degrade", "lifecycles", "plugins", "id", "wujieSupport", "bus", "EventBus", "provide", "styleSheetElements", "execQueue", "getPlugins", "_appRouteParse", "appRouteParse", "urlElement", "appHostPath", "appRoutePath", "iframe", "iframeGenerator", "_localGenerator", "localGenerator", "proxyDocument", "proxyLocation", "_proxyGenerator", "proxyGenerator", "proxyWindow", "proxy", "addSandboxCacheWithWujie", "_createClass", "key", "value", "_active", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "_this", "sync", "el", "template", "props", "alive", "prefix", "fetch", "replace", "iframeWindow", "iframeFetch", "iframeBody", "_initRenderIframeAndC", "container", "_iframeBody", "wrap", "_callee$", "_context", "prev", "next", "hrefFlag", "activeFlag", "iframeReady", "contentWindow", "input", "init", "getAbsolutePath", "href", "execFlag", "syncUrlToWindow", "syncUrlToIframe", "rawDocumentQuerySelector", "call", "document", "initRenderIframeAndContainer", "<PERSON><PERSON><PERSON><PERSON>", "patchEventTimeStamp", "onunload", "unmount", "contentDocument", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "recoverEventListeners", "renderTemplateToIframe", "recoverDocumentListeners", "abrupt", "shadowRoot", "renderElementToContainer", "createWujieWebComponent", "renderTemplateToShadowRoot", "patchCssRules", "stop", "active", "_x", "apply", "arguments", "requestIdleCallback", "callback", "_this2", "_start", "_callee2", "getExternalScripts", "_this3", "scriptResultList", "beforeScriptResultList", "afterScriptResultList", "syncScriptResultList", "asyncScriptResultList", "deferScriptResultList", "domContentLoadedTrigger", "domLoadedTrigger", "_callee2$", "_context2", "sent", "getPresetLoaders", "for<PERSON>ach", "scriptResult", "defer", "push", "async", "beforeScriptResult", "insertScriptToIframe", "concat", "contentPromise", "then", "content", "_objectSpread", "mount", "_this3$execQueue$shif", "eventTrigger", "shift", "afterScriptResult", "_this3$execQueue$shif2", "isFunction", "__WUJIE_UNMOUNT", "removeLoading", "Promise", "resolve", "_this3$execQueue$shif3", "start", "_x2", "_this$execQueue$shift", "mountFlag", "__WUJIE_MOUNT", "_this$lifecycles", "_this$lifecycles$befo", "_this$lifecycles2", "_this$lifecycles2$aft", "beforeMount", "afterMount", "_this$lifecycles3", "_this$lifecycles3$act", "activated", "_unmount", "_callee3", "_this$lifecycles4", "_this$lifecycles4$dea", "_this$lifecycles5", "_this$lifecycles5$bef", "_this$lifecycles6", "_this$lifecycles6$aft", "_this$bus", "_callee3$", "_context3", "clearInactiveAppUrl", "deactivated", "beforeUnmount", "afterUnmount", "$clear", "removeEventListener", "head", "body", "destroy", "elementEventCacheMap", "_this$iframe$parentNo", "__WUJIE_EVENTLISTENER__", "o", "type", "listener", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "deleteWujieById", "rebuildStyleSheets", "_this4", "length", "styleSheetElement", "rawElementAppendChild", "hasAttribute", "WUJIE_DATA_ATTACH_CSS_FLAG", "_getPatchStyleElement", "getPatchStyleElements", "Array", "from", "querySelectorAll", "map", "sheet", "_getPatchStyleElement2", "_slicedToArray", "hostStyleSheetElement", "fontStyleSheetElement", "append<PERSON><PERSON><PERSON>", "setAttribute", "bus", "EventBus", "Date", "now", "toString", "window", "__WU<PERSON><PERSON>", "__POWERED_BY_WUJIE__", "stopMainAppRun", "processAppForHrefJump", "defineWujieWebComponent", "wujieSupport", "warn", "WUJIE_TIPS_NOT_SUPPORTED", "setupApp", "options", "name", "addSandboxCacheWithOptions", "startApp", "_x", "_startApp", "apply", "arguments", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee2", "startOptions", "_newSandbox$lifecycle", "_newSandbox$lifecycle2", "sandbox", "cacheOptions", "url", "html", "replace", "fetch", "props", "attrs", "degradeAttrs", "fiber", "alive", "degrade", "sync", "prefix", "el", "loading", "plugins", "lifecycles", "_if<PERSON><PERSON><PERSON><PERSON><PERSON>", "_sandbox$lifecycles3", "_sandbox$lifecycles3$", "_sandbox$lifecycles2", "_sandbox$lifecycles2$", "_yield$importHTML2", "_getExternalScripts", "_sandbox$lifecycles4", "_sandbox$lifecycles4$", "_sandbox$lifecycles5", "_sandbox$lifecycles5$", "newSandbox", "_yield$importHTML3", "template", "getExternalScripts", "getExternalStyleSheets", "processedHtml", "wrap", "_callee2$", "_context2", "prev", "next", "getWujieById", "getOptionsById", "mergeOptions", "getPlugins", "iframeWindow", "iframe", "contentWindow", "preload", "active", "execFlag", "beforeLoad", "call", "importHTML", "opts", "loadError", "sent", "start", "activated", "abrupt", "destroy", "isFunction", "__WUJIE_MOUNT", "unmount", "rebuildStyleSheets", "beforeMount", "afterMount", "mountFlag", "addLoading", "<PERSON><PERSON><PERSON>", "processCssLoader", "stop", "preloadApp", "preOptions", "requestIdleCallback", "isMatchSyncQueryById", "_objectSpread", "exec", "runPreload", "_ref", "_callee", "_sandbox$lifecycles", "_sandbox$lifecycles$b", "_yield$importHTML", "_callee$", "_context", "destroyApp", "id", "_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "_typeof", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "wujieVueOptions", "props", "width", "String", "height", "loading", "HTMLElement", "undefined", "url", "sync", "Boolean", "prefix", "alive", "attrs", "replace", "Function", "fetch", "fiber", "degrade", "plugins", "Array", "beforeLoad", "beforeMount", "afterMount", "beforeUnmount", "afterUnmount", "activated", "deactivated", "loadError", "data", "startAppQueue", "mounted", "_this", "bus", "$onAll", "handleEmit", "execStartApp", "$watch", "methods", "event", "_len", "args", "_key", "$emit", "concat", "startApp", "_this2", "_callee", "_callee$", "_context", "rawStartApp", "el", "$refs", "wujie", "t0", "console", "log", "destroy", "destroyApp", "<PERSON><PERSON><PERSON><PERSON>", "$offAll", "render", "style", "ref", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defineComponent", "setupApp", "preloadApp", "install", "app", "component"]}