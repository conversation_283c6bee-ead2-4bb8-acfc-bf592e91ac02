// node_modules/js-binary-schema-parser/src/parsers/uint8.js
var buildStream = (uint8Data) => ({
  data: uint8Data,
  pos: 0
});
var readByte = () => (stream) => {
  return stream.data[stream.pos++];
};
var peekByte = (offset = 0) => (stream) => {
  return stream.data[stream.pos + offset];
};
var readBytes = (length) => (stream) => {
  return stream.data.subarray(stream.pos, stream.pos += length);
};
var peekBytes = (length) => (stream) => {
  return stream.data.subarray(stream.pos, stream.pos + length);
};
var readString = (length) => (stream) => {
  return Array.from(readBytes(length)(stream)).map((value) => String.fromCharCode(value)).join("");
};
var readUnsigned = (littleEndian) => (stream) => {
  const bytes = readBytes(2)(stream);
  return littleEndian ? (bytes[1] << 8) + bytes[0] : (bytes[0] << 8) + bytes[1];
};
var readArray = (byteSize, totalOrFunc) => (stream, result, parent) => {
  const total = typeof totalOrFunc === "function" ? totalOrFunc(stream, result, parent) : totalOrFunc;
  const parser = readBytes(byteSize);
  const arr = new Array(total);
  for (var i = 0; i < total; i++) {
    arr[i] = parser(stream);
  }
  return arr;
};
var subBitsTotal = (bits, startIndex, length) => {
  var result = 0;
  for (var i = 0; i < length; i++) {
    result += bits[startIndex + i] && 2 ** (length - i - 1);
  }
  return result;
};
var readBits = (schema) => (stream) => {
  const byte = readByte()(stream);
  const bits = new Array(8);
  for (var i = 0; i < 8; i++) {
    bits[7 - i] = !!(byte & 1 << i);
  }
  return Object.keys(schema).reduce((res, key) => {
    const def = schema[key];
    if (def.length) {
      res[key] = subBitsTotal(bits, def.index, def.length);
    } else {
      res[key] = bits[def.index];
    }
    return res;
  }, {});
};

export {
  buildStream,
  readByte,
  peekByte,
  readBytes,
  peekBytes,
  readString,
  readUnsigned,
  readArray,
  readBits
};
//# sourceMappingURL=chunk-IH545NJ6.js.map
