import {
  J1,
  <PERSON>
} from "./chunk-SH7QETZO.js";
import "./chunk-HU6QSPE3.js";
import {
  computed,
  createCommentVNode,
  createElementBlock,
  defineComponent,
  inject,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  openBlock,
  reactive,
  ref,
  renderSlot,
  watch,
  withModifiers
} from "./chunk-2QF5QTCF.js";
import "./chunk-PLDDJCW6.js";

// node_modules/vue3-ts-jsoneditor/dist/index.mjs
var le = [
  "selection",
  "mainMenuBar",
  "navigationBar",
  "statusBar",
  "askToFormat",
  "readOnly",
  "indentation",
  "tabSize",
  "escapeControlCharacters",
  "escapeUnicodeCharacters",
  "flattenColumns",
  "validator",
  "onClassName",
  "onRenderValue",
  "onRenderMenu"
];
var se = [
  "selection",
  "mode",
  "mainMenuBar",
  "navigationBar",
  "statusBar",
  "askToFormat",
  "readOnly",
  "indentation",
  "tabSize",
  "escapeControlCharacters",
  "escapeUnicodeCharacters",
  "flattenColumns",
  "validator",
  "parser",
  "validationParser",
  "pathParser",
  "onClassName",
  "onRenderValue",
  "onRenderMenu"
];
var ie = (a = {}, s) => {
  const u = {};
  for (const n of se) {
    const d = s[n] !== void 0 ? s[n] : a[n];
    d !== void 0 && (u[n] = d);
  }
  return u;
};
var de = `
  <svg 
    class="fa-icon svelte-1dof0an" 
    viewBox="0 0 1024 1024" 
    version="1.1"
     xmlns="http://www.w3.org/2000/svg" 
     p-id="1927" xmlns:xlink="http://www.w3.org/1999/xlink" 
     width="24" 
     height="24"
  >
    <path d="M63.989383 105.442494l0 268.396843c0 18.935258 15.368012 34.304294 34.304294 34.304294 18.936281 0 34.304294-15.369036 34.304294-34.304294L132.597971 180.156126l218.107483 218.176045c12.82919 12.830213 33.618679 12.830213 46.515407 0 12.830213-12.897751 12.830213-33.686217 0-46.51643l-218.176045-218.107483 193.683211 0c18.935258 0 34.304294-15.369036 34.304294-34.304294 0-18.935258-15.369036-34.304294-34.304294-34.304294L104.331183 65.09967C79.288834 65.09967 63.989383 77.999468 63.989383 105.442494L63.989383 105.442494z" p-id="1928" fill="#e6e6e6"></path><path d="M917.688719 65.09967 649.290853 65.09967c-18.935258 0-34.304294 15.369036-34.304294 34.304294 0 18.936281 15.369036 34.304294 34.304294 34.304294l193.683211 0-218.176045 218.107483c-12.830213 12.82919-12.830213 33.618679 0 46.51643 12.897751 12.830213 33.686217 12.830213 46.515407 0L889.420909 180.156126l0 193.683211c0 18.935258 15.369036 34.304294 34.304294 34.304294 18.936281 0 34.304294-15.369036 34.304294-34.304294L958.029496 105.442494C958.029496 77.999468 942.79963 65.09967 917.688719 65.09967L917.688719 65.09967z" p-id="1929" fill="#e6e6e6"></path>
    <path d="M104.331183 957.013353l268.397866 0c18.935258 0 34.304294-15.368012 34.304294-34.304294 0-18.936281-15.369036-34.304294-34.304294-34.304294L179.045839 888.404766l218.176045-218.107483c12.830213-12.82919 12.830213-33.618679 0-46.515407-12.897751-12.830213-33.686217-12.830213-46.515407 0l-218.107483 218.176045L132.598994 648.27471c0-18.935258-15.368012-34.304294-34.304294-34.304294-18.936281 0-34.304294 15.369036-34.304294 34.304294l0 268.397866C63.989383 944.115602 79.288834 957.013353 104.331183 957.013353L104.331183 957.013353z" p-id="1930" fill="#e6e6e6"></path>
    <path d="M958.029496 916.671553 958.029496 648.27471c0-18.935258-15.368012-34.304294-34.304294-34.304294-18.935258 0-34.304294 15.369036-34.304294 34.304294l0 193.683211L671.313425 623.781876c-12.82919-12.830213-33.618679-12.830213-46.515407 0-12.830213 12.897751-12.830213 33.686217 0 46.515407l218.176045 218.107483L649.290853 888.404766c-18.935258 0-34.304294 15.368012-34.304294 34.304294 0 18.936281 15.369036 34.304294 34.304294 34.304294l268.397866 0C942.79963 957.013353 958.029496 944.115602 958.029496 916.671553L958.029496 916.671553z" p-id="1931" fill="#e6e6e6"></path>
  </svg>
`;
var P = (a, s, u = false) => {
  const n = a && typeof a[s] < "u";
  return !n && u && console.warn("Typeguard warn!!! The object has no property: ", s), n;
};
var ce = defineComponent({
  name: "JsonEditor",
  props: {
    /**
     * ### modelValue: unknown
     * Pass the JSON value or string to be rendered in the JSONEditor.
     * */
    modelValue: [Object, Array, Number, String, Boolean, String, null],
    /**
     * ### value: unknown
     * props value is an alternative to modelValue
     * Pass the JSON value or string to be rendered in the JSONEditor.
     * */
    value: {
      type: [Object, Array, Number, String, Boolean, String, null],
      default: void 0
    },
    /**
     * ### json: unknown
     * Pass the JSON value to be rendered in the JSONEditor.
     * */
    json: {
      type: [Object, Array, Number, String, Boolean, null],
      default: void 0
    },
    /**
     * ### text: string
     * Pass the JSON string to be rendered in the JSONEditor.
     * */
    text: String,
    /**
     * ### jsonString: string
     * Same as prop 'text'. Pass the JSON string to be rendered in the JSONEditor.
     * */
    jsonString: String,
    /**
     * ### selection: JSONEditorSelection | null.
     * The current selected contents. You can use two-way binding using bind:selection. The tree mode
     * supports MultiSelection, KeySelection, ValueSelection, InsideSelection, or AfterSelection. The
     * table mode supports ValueSelection, and text mode supports TextSelection.
     * */
    selection: {
      type: Object,
      default: void 0
    },
    /**
     * ### mode: 'tree' | 'text' | 'table'.
     * Open the editor in 'tree' mode (default) or 'text' mode (formerly: code mode).
     * */
    mode: {
      type: String,
      default: "tree"
    },
    /**
     * ### mainMenuBar: boolean
     * Show the main menu bar. Default value is true.
     * */
    mainMenuBar: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### navigationBar: boolean
     * Show the navigation bar with, where you can see the selected path and navigate through your
     * document from there. Default value is true.
     * */
    navigationBar: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### statusBar: boolean
     * Show a status bar at the bottom of the 'text' editor, showing information about the cursor
     * location and selected contents. Default value is true.
     * */
    statusBar: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### askToFormat: boolean
     * When true (default), the user will be asked whether he/she wants to format the JSON document
     * when a compact document is loaded or pasted in 'text' mode. Only applicable to 'text' mode.
     */
    askToFormat: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### readOnly: boolean
     * Open the editor in read-only mode: no changes can be made, non-relevant buttons are hidden
     * from the menu, and the context menu is not enabled. Default value is false.
     * */
    readOnly: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### indentation: number | string
     * Number of spaces use for indentation when stringifying JSON, or a string to be used as indentation
     * like '\t' to use a tab as indentation, or ' ' to use 4 spaces (which is equivalent to configuring
     * indentation: 4). See also property tabSize.
     * */
    indentation: [String, Number],
    /**
     * ### tabSize: number
     * When indentation is configured as a tab character (indentation: '\t'), tabSize configures how
     * large a tab character is rendered. Default value is 4. Only applicable to text mode.
     * */
    tabSize: Number,
    /**
     * ### escapeControlCharacters: boolean.
     * False by default. When true, control characters like newline and tab are rendered as escaped
     * characters \n and \t. Only applicable for 'tree' mode, in 'text' mode control characters are
     * always escaped.
     * */
    escapeControlCharacters: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### escapeUnicodeCharacters: boolean.
     * False by default. When true, unicode characters like ☎ and 😀 are rendered escaped
     * like \u260e and \ud83d\ude00.
     * */
    escapeUnicodeCharacters: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### flattenColumns: boolean.
     * True by default. Only applicable to 'table' mode. When true, nested object properties
     * will be displayed each in their own column, with the nested path as column name. When false,
     * nested objects will be rendered inline, and double-clicking them will open them in a popup
     * */
    flattenColumns: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### validator: function (json: unknown): ValidationError[].
     * Validate the JSON document. For example use the built-in JSON Schema validator
     * powered by Ajv:
     * ```ts
     *  import { createAjvValidator } from 'svelte-jsoneditor'
     *  const validator = createAjvValidator(schema, schemaDefinitions)
     * ```
     * */
    validator: Function,
    /**
     * ### parser: JSON = JSON
     * Configure a custom JSON parser, like lossless-json. By default, the native JSON
     * parser of JavaScript is used. The JSON interface is an object with a parse and
     * stringify function.
     * */
    parser: Object,
    /**
     * ### validationParser: JSONParser = JSON
     * Only applicable when a validator is provided. This is the same as parser, except
     * that this parser is used to parse the data before sending it to the validator.
     * Configure a custom JSON parser that is used to parse JSON before passing it to the
     * validator. By default, the built-in JSON parser is used. When passing a custom
     * validationParser, make sure the output of the parser is supported by the configured
     * validator. So, when the validationParser can output bigint numbers or other numeric
     * types, the validator must also support that. In tree mode, when parser is not equal
     * to validationParser, the JSON document will be converted before it is passed to the
     * validator via validationParser.parse(parser.stringify(json))
     * */
    validationParser: Object,
    /**
     * ### pathParser: JSONPathParser
     * An optional object with a parse and stringify method to parse and stringify a JSONPath,
     * which is an array with property names. The pathParser is used in the path editor in the
     * navigation bar, which is opened by clicking the edit button on the right side of the
     * navigation bar. The pathParser.parse function is allowed to throw an Error when the input
     * is invalid. By default, a JSON Path notation is used, which looks like $.data[2].nested.property.
     * Alternatively, it is possible to use for example a JSON Pointer notation
     * like /data/2/nested/property or something custom-made. Related helper functions:
     * parseJSONPath and stringifyJSONPath, parseJSONPointer and compileJSONPointer
     * */
    pathParser: Object,
    /**
     * ### queryLanguagesIds: QueryLanguageId[].
     * Configure one or multiple query language that can be used in the Transform modal.
     * An array of available query languages id's
     * [javascript', 'lodash', 'jmespath']
     * */
    queryLanguagesIds: Array,
    /**
     * ### queryLanguageId: string.
     * The id of the currently selected query language.
     * 'javascript' | 'lodash' | 'jmespath'
     * */
    queryLanguageId: String,
    /**
     * ### onClassName(path: Path, value: any): string | undefined.
     * Add a custom class name to specific nodes, based on their path and/or value.
     * */
    onClassName: Function,
    /**
     * ### onRenderValue(props: RenderValueProps) : RenderValueComponentDescription[]
     *
     * ## EXPERIMENTAL! This API will most likely change in future versions.
     * Customize rendering of the values. By default, renderValue is used, which renders a value as an
     * editable div and depending on the value can also render a boolean toggle, a color picker, and a
     * timestamp tag. Multiple components can be rendered alongside each other, like the boolean toggle
     * and color picker being rendered left from the editable div. Built in value renderer components:
     *
     *  > EditableValue, ReadonlyValue, BooleanToggle, ColorPicker, TimestampTag, EnumValue.
     *
     *
     * ```ts
     *  import { renderJSONSchemaEnum, renderValue } from 'svelte-jsoneditor'
     *
     *  function onRenderValue(props) {
     *    // use the enum renderer, and fallback on the default renderer
     *    return renderJSONSchemaEnum(props, schema, schemaDefinitions) || renderValue(props)
     *  }
     * ```
     * */
    onRenderValue: Function,
    /**
     * ### onRenderMenu(items: MenuItem[], context: { mode: 'tree' | 'text' | 'table', modal: boolean }) : MenuItem[] | undefined.
     * Callback which can be used to make changes to the menu items. New items can be added, or existing items can be removed or
     * reorganized. When the function returns undefined, the original items will be applied. Using the context values mode and
     * modal, different actions can be taken depending on the mode of the editor and whether the editor is rendered inside a
     * modal or not.
     *
     *  A menu item MenuItem can be one of the following types:
     *
     *  - Button:
     *  ```ts
     *  interface MenuButtonItem {
     *    onClick: () => void
     *    icon?: FontAwesomeIcon
     *    text?: string
     *    title?: string
     *    className?: string
     *    disabled?: boolean
     *  }
     *  ```
     *
     *  - Separator (gray vertical line between a group of items):
     *  ```ts
     *    interface MenuSeparatorItem {
     *      separator: true
     *    }
     *  ```
     *
     *  - Space (fills up empty space):
     *  ```ts
     *    interface MenuSpaceItem {
     *      space: true
     *    }
     *  ```
     * */
    onRenderMenu: Function,
    /**
     * ### height: string | number
     * Height of render container
     * */
    height: [String, Number],
    /**
     * ### fullWidthButton: boolean
     * Show full screen button
     * */
    fullWidthButton: {
      type: Boolean,
      default: void 0
    },
    /**
     * ### darkTheme: boolean
     * Switch to dark theme
     * */
    darkTheme: {
      type: Boolean,
      default: void 0
    }
  },
  emits: [
    "update:modelValue",
    "update:json",
    "update:text",
    "update:jsonString",
    "update:selection",
    "change",
    "error",
    "change-mode",
    "update:mode",
    "change-query-language",
    "focus",
    "blur"
  ],
  setup(a, { expose: s, emit: u }) {
    const n = inject("jsonEditorOptions", {}), d = ref(), i = ref(null), g = ref(false), m = ref(false), y = ref(false), v = ref("tree"), r = ref(null), T = computed(() => {
      const e = a.height || (n == null ? void 0 : n.height);
      return e && !g.value ? {
        height: e + "px"
      } : {};
    }), B = computed(() => a.darkTheme || (n == null ? void 0 : n.darkTheme)), w = computed(() => a.queryLanguagesIds || (n == null ? void 0 : n.queryLanguagesIds)), N = computed(() => a.queryLanguageId || (n == null ? void 0 : n.queryLanguageId)), f = reactive({}), V = async () => {
      var t;
      if (typeof window > "u" || typeof w.value > "u" || !((t = w.value) != null && t.length))
        return;
      for (const l of w.value)
        if (!f[l])
          switch (l) {
            case "javascript": {
              const { javascriptQueryLanguage: o } = await import("./vanilla-jsoneditor-CH8w8edm-ALGJPI77.js");
              f[l] = o;
              break;
            }
            case "lodash": {
              const { lodashQueryLanguage: o } = await import("./vanilla-jsoneditor-CH8w8edm-ALGJPI77.js");
              f[l] = o;
              break;
            }
            case "jmespath": {
              const { jmespathQueryLanguage: o } = await import("./vanilla-jsoneditor-CH8w8edm-ALGJPI77.js");
              f[l] = o;
              break;
            }
            case "jsonquery": {
              const { jsonQueryLanguage: o } = await import("./vanilla-jsoneditor-CH8w8edm-ALGJPI77.js");
              f[l] = o;
              break;
            }
            case "jsonpath": {
              const { jsonpathQueryLanguage: o } = await import("./vanilla-jsoneditor-CH8w8edm-ALGJPI77.js");
              f[l] = o;
              break;
            }
          }
      const e = Object.values(f);
      if (e.length !== 0)
        return e;
    }, L = () => {
      i.value && (i.value.removeEventListener("click", b), i.value = null);
    }, q = async () => {
      if (typeof window > "u") return;
      const { getElement: e, createElement: t } = await import("./full-width-button-handler-D8h6mHdo-VWCSLLFL.js"), { setFullWidthButtonStyle: l } = await import("./styles-handler-nPP1xL5s-UM7LI7SO.js");
      await l();
      const o = e(".jse-full-width"), O = (n == null ? void 0 : n.fullWidthButton) !== void 0 ? n == null ? void 0 : n.fullWidthButton : true;
      if (!(a.fullWidthButton !== void 0 ? a.fullWidthButton : O) || o) return;
      i.value && L();
      const x = e(".jse-menu"), D = Array.from(x.classList).find((H) => H.startsWith("svelte-"));
      i.value = t("button"), i.value.classList.add("jse-full-width"), i.value.classList.add("jse-button"), i.value.classList.add(D), i.value.innerHTML += de, x.appendChild(i.value), i.value.addEventListener("click", b);
    }, b = () => {
      var e, t;
      g.value = !g.value, g.value ? (e = i.value) == null || e.classList.add("jse-full-width--active") : (t = i.value) == null || t.classList.remove("jse-full-width--active");
    }, F = (e, t, l) => {
      if (y.value) {
        y.value = false;
        return;
      }
      m.value = true, P(e, "json") && (u("update:json", e.json), u("update:modelValue", e.json)), P(e, "text") && (u("update:text", e.text), u("update:jsonString", e.text), u("update:modelValue", e.text)), u("change", e, t, l);
    }, M = (e) => {
      u("error", e);
    }, W = (e) => {
      v.value = e, u("change-mode", e), u("update:mode", e);
    }, I = (e) => {
      u("change-query-language", e);
    }, A = () => {
      u("focus");
    }, R = () => {
      u("blur");
    }, J = (e) => {
      u("update:selection", e);
    }, z = (e, t) => (nextTick(() => {
      q();
    }), typeof a.onRenderMenu == "function" ? a.onRenderMenu(e, t) : e), j = async () => {
      const e = { fullWidthButton: true, ...n || {} }, t = await V();
      return {
        ...ie(e, a),
        queryLanguages: t,
        queryLanguageId: N.value,
        onChange: F,
        onError: M,
        onChangeMode: W,
        onChangeQueryLanguage: I,
        onFocus: A,
        onBlur: R,
        onRenderMenu: z,
        onSelect: J
      };
    }, k = ref(true), S = () => {
      const e = (o = {}) => o === null || typeof o > "u" || typeof o == "number" || typeof o == "bigint" || typeof o == "string" || typeof o == "boolean" ? {
        json: o
      } : Array.isArray(o) ? {
        json: [...o]
      } : {
        json: { ...o }
      }, t = (o = "") => ({
        text: o || ""
      }), l = a.modelValue || a.value;
      return l ? v.value === "text" && typeof l == "string" ? t(l) : e(l) : typeof a.json < "u" ? e(a.json) : typeof a.text < "u" ? t(a.text) : typeof a.jsonString < "u" ? t(a.jsonString) : t();
    }, Q = async () => {
      if (!(typeof window > "u")) {
        if (!r.value) {
          const e = await j(), { createJSONEditor: t } = await import("./vanilla-jsoneditor-CH8w8edm-ALGJPI77.js");
          k.value = false, r.value = t({
            target: d.value,
            props: e
          }), r.value.set(S());
        }
        r.value.focus();
      }
    }, C = async () => {
      var t;
      const e = await j();
      (t = r.value) == null || t.updateProps(e);
    }, U = () => {
      var e;
      if (m.value) {
        m.value = false;
        return;
      }
      y.value = true, (e = r.value) == null || e.update(S());
    }, _ = () => {
      r.value && (r.value.destroy(), r.value = null), L();
    };
    return watch(
      [
        ...le.map((e) => () => a[e])
      ],
      C,
      { deep: true }
    ), watch(
      [() => a.modelValue, () => a.value, () => a.json, () => a.text, () => a.jsonString],
      U,
      {
        deep: true
      }
    ), watch(
      () => a.mode,
      (e) => {
        e !== v.value && (v.value = e, C());
      }
    ), watch(
      () => B.value,
      async (e) => {
        if (e) {
          const { setDarkThemeStyle: t } = await import("./styles-handler-nPP1xL5s-UM7LI7SO.js");
          await t();
        }
      },
      { immediate: true }
    ), onMounted(() => {
      nextTick(() => {
        Q();
      });
    }), onBeforeUnmount(() => {
      _();
    }), s({
      $collapseAll() {
        var e;
        v.value === "tree" && ((e = r.value) == null || e.collapse([], true));
      },
      $expandAll() {
        var e;
        v.value === "tree" && ((e = r.value) == null || e.expand([]));
      },
      $expand(e, t) {
        var l, o;
        typeof e == "function" ? ((l = r.value) == null || l.expand([], e), console.warn(
          "In new API you must pass the path before the callback!!! Backwards compatibility is deprecated and will be discontinued in the future"
        )) : (o = r.value) == null || o.expand(e, t);
      },
      $get() {
        var e;
        return (e = r.value) == null ? void 0 : e.get();
      },
      $set(e) {
        var t;
        (t = r.value) == null || t.set(e);
      },
      $update(e) {
        var t;
        (t = r.value) == null || t.update(e);
      },
      $updateProps(e) {
        var t;
        (t = r.value) == null || t.updateProps(e);
      },
      async $refresh() {
        var e;
        await ((e = r.value) == null ? void 0 : e.refresh());
      },
      $focus() {
        var e;
        (e = r.value) == null || e.focus();
      },
      async $destroy() {
        var e;
        await ((e = r.value) == null ? void 0 : e.destroy());
      },
      $patch(e) {
        var t;
        return (t = r.value) == null ? void 0 : t.patch(e);
      },
      $transform(e) {
        var t;
        (t = r.value) == null || t.transform(e);
      },
      async $scrollTo(e) {
        var t;
        await ((t = r.value) == null ? void 0 : t.scrollTo(e));
      },
      $findElement(e) {
        var t;
        return (t = r.value) == null ? void 0 : t.findElement(e);
      },
      $acceptAutoRepair() {
        var e;
        return (e = r.value) == null ? void 0 : e.acceptAutoRepair();
      },
      $validate() {
        var e;
        return (e = r.value) == null ? void 0 : e.validate();
      }
    }), {
      max: g,
      getHeight: T,
      container: d,
      darkThemeStyle: B,
      fallbackSlot: k
    };
  }
});
var fe = (a, s) => {
  const u = a.__vccOpts || a;
  for (const [n, d] of s)
    u[n] = d;
  return u;
};
function ve(a, s, u, n, d, i) {
  return openBlock(), createElementBlock("div", {
    class: normalizeClass(["vue-ts-json-editor", { "vue-ts-json-editor--max-box": a.max, "jse-theme-dark": a.darkThemeStyle }]),
    style: normalizeStyle(a.getHeight),
    ref: "container",
    onKeydown: s[0] || (s[0] = withModifiers(() => {
    }, ["stop"]))
  }, [
    a.fallbackSlot ? renderSlot(a.$slots, "default", { key: 0 }) : createCommentVNode("", true)
  ], 38);
}
var E = fe(ce, [["render", ve]]);
E.install = () => {
  console.error(`Default import not working!!! Use "import {JsonEditorPlugin} from 'vue3-ts-jsoneditor';"`);
};
var pe = {
  install(a, s = {}) {
    a.config.globalProperties.$_vue3TsJsoneditor || (a.config.globalProperties.$_vue3TsJsoneditor = true, a.component(s.componentName || "JsonEditor", E), a.provide("jsonEditorOptions", s.options));
  }
};
export {
  pe as JsonEditorPlugin,
  E as default,
  KC as renderJSONSchemaEnum,
  J1 as renderValue
};
//# sourceMappingURL=vue3-ts-jsoneditor.js.map
