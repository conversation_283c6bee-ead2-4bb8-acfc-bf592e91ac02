{"version": 3, "sources": ["../../vue3-ts-jsoneditor/dist/string-styles-DT93GIY_.js"], "sourcesContent": ["const e = `\n.vue-ts-json-editor {\n  min-width:300px;\n  width:100%\n}\n\n.vue-ts-json-editor--max-box{\n  position:fixed;\n  top:0;\n  left:0;\n  width:100vw;\n  height:100vh;\n  z-index:10000\n}\n\n.vue-ts-json-editor .jse-menu .jse-full-width{\n  display:flex\n}\n\n.vue-ts-json-editor .jse-menu .jse-full-width--active{\n  background-color:#ffffff38!important;\n  border-color:#fff9!important\n}\n`, o = `\n.jse-theme-dark {\n  /* over all fonts, sizes, and colors */\n  --jse-theme-color: #2f6dd0;\n  --jse-theme-color-highlight: #467cd2;\n  --jse-background-color: #1e1e1e;\n  --jse-text-color: #d4d4d4;\n\n  /* main, menu, modal */\n  --jse-main-border: 1px solid #4f4f4f;\n  --jse-menu-color: #fff;\n  --jse-modal-background: #2f2f2f;\n  --jse-modal-overlay-background: rgba(0, 0, 0, 0.5);\n  --jse-modal-code-background: #2f2f2f;\n\n  /* panels: navigation bar, gutter, search box */\n  --jse-panel-background: #333333;\n  --jse-panel-background-border: 1px solid #464646;\n  --jse-panel-color: var(--jse-text-color);\n  --jse-panel-color-readonly: #737373;\n  --jse-panel-border: 1px solid #3c3c3c;\n  --jse-panel-button-color-highlight: #e5e5e5;\n  --jse-panel-button-background-highlight: #464646;\n\n  /* navigation-bar */\n  --jse-navigation-bar-background: #656565;\n  --jse-navigation-bar-background-highlight: #7e7e7e;\n  --jse-navigation-bar-dropdown-color: var(--jse-text-color);\n\n  /* context menu */\n  --jse-context-menu-background: #4b4b4b;\n  --jse-context-menu-background-highlight: #595959;\n  --jse-context-menu-separator-color: #595959;\n  --jse-context-menu-color: var(--jse-text-color);\n  --jse-context-menu-button-background: #737373;\n  --jse-context-menu-button-background-highlight: #818181;\n  --jse-context-menu-button-color: var(--jse-context-menu-color);\n\n  /* contents: json key and values */\n  --jse-key-color: #9cdcfe;\n  --jse-value-color: var(--jse-text-color);\n  --jse-value-color-number: #b5cea8;\n  --jse-value-color-boolean: #569cd6;\n  --jse-value-color-null: #569cd6;\n  --jse-value-color-string: #ce9178;\n  --jse-value-color-url: #ce9178;\n  --jse-delimiter-color: #949494;\n  --jse-edit-outline: 2px solid var(--jse-text-color);\n\n  /* contents: selected or hovered */\n  --jse-selection-background-color: #464646;\n  --jse-selection-background-light-color: #333333;\n  --jse-hover-background-color: #343434;\n\n  /* contents: section of collapsed items in an array */\n  --jse-collapsed-items-background-color: #333333;\n  --jse-collapsed-items-selected-background-color: #565656;\n  --jse-collapsed-items-link-color: #b2b2b2;\n  --jse-collapsed-items-link-color-highlight: #ec8477;\n\n  /* contents: highlighting of search results */\n  --jse-search-match-color: #724c27;\n  --jse-search-match-outline: 1px solid #966535;\n  --jse-search-match-active-color: #9f6c39;\n  --jse-search-match-active-outline: 1px solid #bb7f43;\n\n  /* contents: inline tags inside the JSON document */\n  --jse-tag-background: #444444;\n  --jse-tag-color: #bdbdbd;\n\n  /* controls in modals: inputs, buttons, and \\`a\\` */\n  --jse-input-background: #3d3d3d;\n  --jse-input-border: var(--jse-main-border);\n  --jse-button-background: #808080;\n  --jse-button-background-highlight: #7a7a7a;\n  --jse-button-color: #e0e0e0;\n  --jse-a-color: #55abff;\n  --jse-a-color-highlight: #4387c9;\n\n  /* svelte-select */\n  --background: #3d3d3d;\n  --border: 1px solid #4f4f4f;\n  --listBackground: #3d3d3d;\n  --itemHoverBG: #505050;\n  --multiItemBG: #5b5b5b;\n  --inputColor: #d4d4d4;\n  --multiClearBG: #8a8a8a;\n  --listShadow: 0 2px 6px 0 rgba(0, 0, 0, 0.24);\n\n  /* color picker */\n  --jse-color-picker-background: #656565;\n  --jse-color-picker-border-box-shadow: #8c8c8c 0 0 0 1px;\n}\n`;\nexport {\n  o as darkTheme,\n  e as fullWidthButton\n};\n"], "mappings": ";;;AAAA,IAAM,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAV,IAuBG,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;", "names": []}