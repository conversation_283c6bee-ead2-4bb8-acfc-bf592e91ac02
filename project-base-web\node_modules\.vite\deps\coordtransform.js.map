{"version": 3, "sources": ["../../coordtransform/index.js"], "sourcesContent": ["/**\n * Created by Wandergis on 2015/7/8.\n * 提供了百度坐标（BD09）、国测局坐标（火星坐标，GCJ02）、和WGS84坐标系之间的转换\n */\n//UMD魔法代码\n// if the module has no dependencies, the above pattern can be simplified to\n(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module.\n    define([], factory);\n  } else if (typeof module === 'object' && module.exports) {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory();\n  } else {\n    // Browser globals (root is window)\n    root.coordtransform = factory();\n  }\n}(this, function () {\n  //定义一些常量\n  var x_PI = 3.14159265358979324 * 3000.0 / 180.0;\n  var PI = 3.1415926535897932384626;\n  var a = 6378245.0;\n  var ee = 0.00669342162296594323;\n  /**\n   * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换\n   * 即 百度 转 谷歌、高德\n   * @param bd_lon\n   * @param bd_lat\n   * @returns {*[]}\n   */\n  var bd09togcj02 = function bd09togcj02(bd_lon, bd_lat) {\n    var bd_lon = +bd_lon;\n    var bd_lat = +bd_lat;\n    var x = bd_lon - 0.0065;\n    var y = bd_lat - 0.006;\n    var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);\n    var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);\n    var gg_lng = z * Math.cos(theta);\n    var gg_lat = z * Math.sin(theta);\n    return [gg_lng, gg_lat]\n  };\n\n  /**\n   * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换\n   * 即谷歌、高德 转 百度\n   * @param lng\n   * @param lat\n   * @returns {*[]}\n   */\n  var gcj02tobd09 = function gcj02tobd09(lng, lat) {\n    var lat = +lat;\n    var lng = +lng;\n    var z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);\n    var theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);\n    var bd_lng = z * Math.cos(theta) + 0.0065;\n    var bd_lat = z * Math.sin(theta) + 0.006;\n    return [bd_lng, bd_lat]\n  };\n\n  /**\n   * WGS84转GCj02\n   * @param lng\n   * @param lat\n   * @returns {*[]}\n   */\n  var wgs84togcj02 = function wgs84togcj02(lng, lat) {\n    var lat = +lat;\n    var lng = +lng;\n    if (out_of_china(lng, lat)) {\n      return [lng, lat]\n    } else {\n      var dlat = transformlat(lng - 105.0, lat - 35.0);\n      var dlng = transformlng(lng - 105.0, lat - 35.0);\n      var radlat = lat / 180.0 * PI;\n      var magic = Math.sin(radlat);\n      magic = 1 - ee * magic * magic;\n      var sqrtmagic = Math.sqrt(magic);\n      dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);\n      dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);\n      var mglat = lat + dlat;\n      var mglng = lng + dlng;\n      return [mglng, mglat]\n    }\n  };\n\n  /**\n   * GCJ02 转换为 WGS84\n   * @param lng\n   * @param lat\n   * @returns {*[]}\n   */\n  var gcj02towgs84 = function gcj02towgs84(lng, lat) {\n    var lat = +lat;\n    var lng = +lng;\n    if (out_of_china(lng, lat)) {\n      return [lng, lat]\n    } else {\n      var dlat = transformlat(lng - 105.0, lat - 35.0);\n      var dlng = transformlng(lng - 105.0, lat - 35.0);\n      var radlat = lat / 180.0 * PI;\n      var magic = Math.sin(radlat);\n      magic = 1 - ee * magic * magic;\n      var sqrtmagic = Math.sqrt(magic);\n      dlat = (dlat * 180.0) / ((a * (1 - ee)) / (magic * sqrtmagic) * PI);\n      dlng = (dlng * 180.0) / (a / sqrtmagic * Math.cos(radlat) * PI);\n      var mglat = lat + dlat;\n      var mglng = lng + dlng;\n      return [lng * 2 - mglng, lat * 2 - mglat]\n    }\n  };\n\n  var transformlat = function transformlat(lng, lat) {\n    var lat = +lat;\n    var lng = +lng;\n    var ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));\n    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;\n    ret += (20.0 * Math.sin(lat * PI) + 40.0 * Math.sin(lat / 3.0 * PI)) * 2.0 / 3.0;\n    ret += (160.0 * Math.sin(lat / 12.0 * PI) + 320 * Math.sin(lat * PI / 30.0)) * 2.0 / 3.0;\n    return ret\n  };\n\n  var transformlng = function transformlng(lng, lat) {\n    var lat = +lat;\n    var lng = +lng;\n    var ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));\n    ret += (20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0 / 3.0;\n    ret += (20.0 * Math.sin(lng * PI) + 40.0 * Math.sin(lng / 3.0 * PI)) * 2.0 / 3.0;\n    ret += (150.0 * Math.sin(lng / 12.0 * PI) + 300.0 * Math.sin(lng / 30.0 * PI)) * 2.0 / 3.0;\n    return ret\n  };\n\n  /**\n   * 判断是否在国内，不在国内则不做偏移\n   * @param lng\n   * @param lat\n   * @returns {boolean}\n   */\n  var out_of_china = function out_of_china(lng, lat) {\n    var lat = +lat;\n    var lng = +lng;\n    // 纬度3.86~53.55,经度73.66~135.05 \n    return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);\n  };\n\n  return {\n    bd09togcj02: bd09togcj02,\n    gcj02tobd09: gcj02tobd09,\n    wgs84togcj02: wgs84togcj02,\n    gcj02towgs84: gcj02towgs84\n  }\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,KAAC,SAAU,MAAM,SAAS;AACxB,UAAI,OAAO,WAAW,cAAc,OAAO,KAAK;AAE9C,eAAO,CAAC,GAAG,OAAO;AAAA,MACpB,WAAW,OAAO,WAAW,YAAY,OAAO,SAAS;AAIvD,eAAO,UAAU,QAAQ;AAAA,MAC3B,OAAO;AAEL,aAAK,iBAAiB,QAAQ;AAAA,MAChC;AAAA,IACF,GAAE,SAAM,WAAY;AAElB,UAAI,OAAO,oBAAsB,MAAS;AAC1C,UAAI,KAAK;AACT,UAAI,IAAI;AACR,UAAI,KAAK;AAQT,UAAI,cAAc,SAASA,aAAY,QAAQ,QAAQ;AACrD,YAAI,SAAS,CAAC;AACd,YAAI,SAAS,CAAC;AACd,YAAI,IAAI,SAAS;AACjB,YAAI,IAAI,SAAS;AACjB,YAAI,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,OAAU,KAAK,IAAI,IAAI,IAAI;AAC9D,YAAI,QAAQ,KAAK,MAAM,GAAG,CAAC,IAAI,OAAW,KAAK,IAAI,IAAI,IAAI;AAC3D,YAAI,SAAS,IAAI,KAAK,IAAI,KAAK;AAC/B,YAAI,SAAS,IAAI,KAAK,IAAI,KAAK;AAC/B,eAAO,CAAC,QAAQ,MAAM;AAAA,MACxB;AASA,UAAI,cAAc,SAASC,aAAY,KAAK,KAAK;AAC/C,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,CAAC;AACX,YAAI,IAAI,KAAK,KAAK,MAAM,MAAM,MAAM,GAAG,IAAI,OAAU,KAAK,IAAI,MAAM,IAAI;AACxE,YAAI,QAAQ,KAAK,MAAM,KAAK,GAAG,IAAI,OAAW,KAAK,IAAI,MAAM,IAAI;AACjE,YAAI,SAAS,IAAI,KAAK,IAAI,KAAK,IAAI;AACnC,YAAI,SAAS,IAAI,KAAK,IAAI,KAAK,IAAI;AACnC,eAAO,CAAC,QAAQ,MAAM;AAAA,MACxB;AAQA,UAAI,eAAe,SAASC,cAAa,KAAK,KAAK;AACjD,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,CAAC;AACX,YAAI,aAAa,KAAK,GAAG,GAAG;AAC1B,iBAAO,CAAC,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,cAAI,OAAO,aAAa,MAAM,KAAO,MAAM,EAAI;AAC/C,cAAI,OAAO,aAAa,MAAM,KAAO,MAAM,EAAI;AAC/C,cAAI,SAAS,MAAM,MAAQ;AAC3B,cAAI,QAAQ,KAAK,IAAI,MAAM;AAC3B,kBAAQ,IAAI,KAAK,QAAQ;AACzB,cAAI,YAAY,KAAK,KAAK,KAAK;AAC/B,iBAAQ,OAAO,OAAW,KAAK,IAAI,OAAQ,QAAQ,aAAa;AAChE,iBAAQ,OAAO,OAAU,IAAI,YAAY,KAAK,IAAI,MAAM,IAAI;AAC5D,cAAI,QAAQ,MAAM;AAClB,cAAI,QAAQ,MAAM;AAClB,iBAAO,CAAC,OAAO,KAAK;AAAA,QACtB;AAAA,MACF;AAQA,UAAI,eAAe,SAASC,cAAa,KAAK,KAAK;AACjD,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,CAAC;AACX,YAAI,aAAa,KAAK,GAAG,GAAG;AAC1B,iBAAO,CAAC,KAAK,GAAG;AAAA,QAClB,OAAO;AACL,cAAI,OAAO,aAAa,MAAM,KAAO,MAAM,EAAI;AAC/C,cAAI,OAAO,aAAa,MAAM,KAAO,MAAM,EAAI;AAC/C,cAAI,SAAS,MAAM,MAAQ;AAC3B,cAAI,QAAQ,KAAK,IAAI,MAAM;AAC3B,kBAAQ,IAAI,KAAK,QAAQ;AACzB,cAAI,YAAY,KAAK,KAAK,KAAK;AAC/B,iBAAQ,OAAO,OAAW,KAAK,IAAI,OAAQ,QAAQ,aAAa;AAChE,iBAAQ,OAAO,OAAU,IAAI,YAAY,KAAK,IAAI,MAAM,IAAI;AAC5D,cAAI,QAAQ,MAAM;AAClB,cAAI,QAAQ,MAAM;AAClB,iBAAO,CAAC,MAAM,IAAI,OAAO,MAAM,IAAI,KAAK;AAAA,QAC1C;AAAA,MACF;AAEA,UAAI,eAAe,SAASC,cAAa,KAAK,KAAK;AACjD,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,OAAS,IAAM,MAAM,IAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC;AAC5G,gBAAQ,KAAO,KAAK,IAAI,IAAM,MAAM,EAAE,IAAI,KAAO,KAAK,IAAI,IAAM,MAAM,EAAE,KAAK,IAAM;AACnF,gBAAQ,KAAO,KAAK,IAAI,MAAM,EAAE,IAAI,KAAO,KAAK,IAAI,MAAM,IAAM,EAAE,KAAK,IAAM;AAC7E,gBAAQ,MAAQ,KAAK,IAAI,MAAM,KAAO,EAAE,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,EAAI,KAAK,IAAM;AACrF,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,SAASC,cAAa,KAAK,KAAK;AACjD,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,MAAQ,MAAM,IAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,KAAK,KAAK,KAAK,IAAI,GAAG,CAAC;AACrG,gBAAQ,KAAO,KAAK,IAAI,IAAM,MAAM,EAAE,IAAI,KAAO,KAAK,IAAI,IAAM,MAAM,EAAE,KAAK,IAAM;AACnF,gBAAQ,KAAO,KAAK,IAAI,MAAM,EAAE,IAAI,KAAO,KAAK,IAAI,MAAM,IAAM,EAAE,KAAK,IAAM;AAC7E,gBAAQ,MAAQ,KAAK,IAAI,MAAM,KAAO,EAAE,IAAI,MAAQ,KAAK,IAAI,MAAM,KAAO,EAAE,KAAK,IAAM;AACvF,eAAO;AAAA,MACT;AAQA,UAAI,eAAe,SAASC,cAAa,KAAK,KAAK;AACjD,YAAI,MAAM,CAAC;AACX,YAAI,MAAM,CAAC;AAEX,eAAO,EAAE,MAAM,SAAS,MAAM,UAAU,MAAM,QAAQ,MAAM;AAAA,MAC9D;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA;AAAA;", "names": ["bd09togcj02", "gcj02tobd09", "wgs84togcj02", "gcj02towgs84", "transformlat", "transformlng", "out_of_china"]}