[1;35m16:37:54.324[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m16:37:54.550[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m16:37:54.554[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m16:37:54.657[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m16:37:54.669[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarting,53][0;39m - Starting GatewayXApplication using Java 17.0.14 with PID 39520 (D:\x\gateway-x\gateway-x-server\target\classes started by Administrator in D:\x)
[1;35m16:37:54.672[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal"
[1;35m16:37:54.752[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m16:37:54.752[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m16:37:54.754[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=gateway-x.yml, group=DEFAULT_GROUP] success
[1;35m16:37:56.670[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'sentinelGatewayFilter' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=gatewayConfig; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/gatewayx/config/GatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.sentinel.gateway.scg.SentinelSCGAutoConfiguration; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/sentinel/gateway/scg/SentinelSCGAutoConfiguration.class]]
[1;35m16:37:56.995[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m16:37:56.999[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m16:37:57.123[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 97 ms. Found 0 Redis repository interfaces.
[1;35m16:37:57.465[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=a682fabe-27a3-3ecc-8e94-3197e1d58eef
[1;35m16:37:58.605[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m16:37:59.191[0;39m [32m[redisson-netty-3-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m16:37:59.237[0;39m [32m[redisson-netty-3-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m16:37:59.545[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m16:37:59.589[0;39m [32m[redisson-netty-6-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m16:37:59.642[0;39m [32m[redisson-netty-6-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m16:38:00.116[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m16:38:02.643[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m16:38:02.644[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m16:38:02.644[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m16:38:02.645[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m16:38:02.645[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m16:38:02.645[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m16:38:02.645[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m16:38:02.645[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m16:38:02.646[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m16:38:02.646[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m16:38:02.646[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m16:38:02.646[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m16:38:02.647[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m16:38:02.647[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m16:38:03.328[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m16:38:03.779[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m16:38:03.998[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m16:38:04.066[0;39m [32m[redisson-netty-11-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m16:38:04.117[0;39m [32m[redisson-netty-11-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m16:38:04.859[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.NacosConfigService[0;39m - [36m[<init>,78][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m16:38:04.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,48][0;39m - DefaultLabelsCollectorManager get labels.....
[1;35m16:38:04.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,62][0;39m - Process LabelsCollector with [name:defaultNacosLabelsCollector]
[1;35m16:38:04.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,64][0;39m - default nacos collect properties raw labels: null
[1;35m16:38:04.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,71][0;39m - default nacos collect properties labels: {}
[1;35m16:38:04.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,74][0;39m - default nacos collect jvm raw labels: null
[1;35m16:38:04.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,80][0;39m - default nacos collect jvm labels: {}
[1;35m16:38:04.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,83][0;39m - default nacos collect env raw labels: null
[1;35m16:38:04.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,91][0;39m - default nacos collect env labels: {}
[1;35m16:38:04.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,50][0;39m - DefaultLabelsCollectorManager get labels finished,labels :{}
[1;35m16:38:04.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m16:38:04.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m16:38:05.108[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m16:38:05.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m16:38:05.110[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] gateway-x-sentinel.json+DEFAULT_GROUP+pedestal
[1;35m16:38:05.131[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=gateway-x-sentinel.json, group=DEFAULT_GROUP, cnt=1
[1;35m16:38:05.132[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0
[1;35m16:38:05.133[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$352/0x000001e31d2bea50
[1;35m16:38:05.134[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$353/0x000001e31d2bee80
[1;35m16:38:05.135[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
[1;35m16:38:05.137[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
[1;35m16:38:05.138[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m16:38:05.140[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m16:38:05.167[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Success to connect to server [***********:8848] on start up, connectionId = 1755851884257_**********_60657
[1;35m16:38:05.168[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m16:38:05.168[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Notify connected event to listeners.
[1;35m16:38:05.169[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$371/0x000001e31d420230
[1;35m16:38:05.169[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [56c443b0-8d6e-4fc7-9229-c6b99bd4e4e1_config-0] Connected,notify listen context...
[1;35m16:38:05.309[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 10001 (http)
[1;35m16:38:05.384[0;39m [32m[nacos.client.config.listener.task-0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[safeNotifyListener,474][0;39m - [fixed-pedestal-***********_8848] [notify-listener] task submitted to user executor, dataId=gateway-x-sentinel.json, group=DEFAULT_GROUP,tenant=pedestal, md5=398b365fcb4c9a2439020ddcb9aa1a76, listener=com.alibaba.csp.sentinel.datasource.nacos.NacosDataSource$1@6d386f05 
[1;35m16:38:05.389[0;39m [32m[sentinel-nacos-ds-update-thread-1][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[run,451][0;39m - [fixed-pedestal-***********_8848] [notify-ok] dataId=gateway-x-sentinel.json, group=DEFAULT_GROUP,tenant=pedestal, md5=398b365fcb4c9a2439020ddcb9aa1a76, listener=com.alibaba.csp.sentinel.datasource.nacos.NacosDataSource$1@6d386f05 ,job run cost=4 millis.
[1;35m16:38:05.673[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m16:38:05.674[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m16:38:05.675[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m16:38:05.676[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m16:38:05.688[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m16:38:05.696[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m16:38:05.696[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m16:38:05.840[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 00251e39-674b-4103-b578-4819c55e588e
[1;35m16:38:05.843[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->00251e39-674b-4103-b578-4819c55e588e
[1;35m16:38:05.843[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m16:38:05.844[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m16:38:05.844[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m16:38:05.845[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m16:38:05.845[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m16:38:05.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Success to connect to server [***********:8848] on start up, connectionId = 1755851884969_**********_60659
[1;35m16:38:05.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m16:38:05.871[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Notify connected event to listeners.
[1;35m16:38:05.871[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$371/0x000001e31d420230
[1;35m16:38:05.872[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m16:38:05.874[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service gateway-x with instance Instance{instanceId='null', ip='**********', port=10001, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m16:38:05.899[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP gateway-x **********:10001 register finished
[1;35m16:38:06.026[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.026[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:seata-server, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.026[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-city, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.027[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.027[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:seata-server, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.027[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-city, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.027[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-decoration, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.027[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-campus, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.027[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-decoration, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.028[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:edtap-server, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.028[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-campus, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.028[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:edtap-server, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.028[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.028[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.028[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.029[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.031[0;39m [32m[boundedElastic-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-campus, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.032[0;39m [32m[boundedElastic-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-campus, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.032[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:distribution, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.032[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-admin, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.032[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-17][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:ti-dix, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-admin, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:distribution, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:seata-server, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-decoration, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-17][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:ti-dix, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-city, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.033[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:seata-server, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.034[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:edtap-server, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.034[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-decoration, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.034[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-city, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.034[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.034[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.035[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:edtap-server, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.035[0;39m [32m[boundedElastic-21][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.035[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.035[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:distribution, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.036[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-admin, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.036[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:ti-dix, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.035[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m16:38:06.036[0;39m [32m[boundedElastic-21][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.037[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-admin, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.037[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:ti-dix, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.037[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.037[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:distribution, group:DEFAULT_GROUP, cluster: 
[1;35m16:38:06.065[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-city -> [{"instanceId":"**********#8989#DEFAULT#DEFAULT_GROUP@@builder-city","ip":"**********","port":8989,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-city","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.065[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.065[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-decoration -> [{"instanceId":"**********#8991#DEFAULT#DEFAULT_GROUP@@builder-decoration","ip":"**********","port":8991,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-decoration","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.065[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(2) service: DEFAULT_GROUP@@edtap-server -> [{"instanceId":"***********#10011##DEFAULT_GROUP@@edtap-server","ip":"***********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"**********#10101##DEFAULT_GROUP@@edtap-server","ip":"**********","port":10101,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.065[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(2) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.069[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-city -> [{"instanceId":"**********#8989#DEFAULT#DEFAULT_GROUP@@builder-city","ip":"**********","port":8989,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-city","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.069[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-decoration -> [{"instanceId":"**********#8991#DEFAULT#DEFAULT_GROUP@@builder-decoration","ip":"**********","port":8991,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-decoration","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.069[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.069[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.070[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@edtap-server -> [{"instanceId":"***********#10011##DEFAULT_GROUP@@edtap-server","ip":"***********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"**********#10101##DEFAULT_GROUP@@edtap-server","ip":"**********","port":10101,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.071[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.071[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@ti-dix -> [{"instanceId":"***********#10009#DEFAULT#DEFAULT_GROUP@@ti-dix","ip":"***********","port":10009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ti-dix","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.072[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.072[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@ti-dix -> [{"instanceId":"***********#10009#DEFAULT#DEFAULT_GROUP@@ti-dix","ip":"***********","port":10009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ti-dix","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.075[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.075[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@seata-server -> [{"instanceId":"***********#8091#default#DEFAULT_GROUP@@seata-server","ip":"***********","port":8091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"default","serviceName":"DEFAULT_GROUP@@seata-server","metadata":{},"instanceIdGenerator":"simple","instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.076[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.077[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@seata-server -> [{"instanceId":"***********#8091#default#DEFAULT_GROUP@@seata-server","ip":"***********","port":8091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"default","serviceName":"DEFAULT_GROUP@@seata-server","metadata":{},"instanceIdGenerator":"simple","instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.080[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@distribution -> [{"instanceId":"***********#81#DEFAULT#DEFAULT_GROUP@@distribution","ip":"***********","port":81,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@distribution","metadata":{"preserved.heart.beat.timeout":"15000","spring.application.name":"distribution","preserved.ip.delete.timeout":"30000","env":"root","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.081[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-admin -> [{"instanceId":"**********#8988#DEFAULT#DEFAULT_GROUP@@builder-admin","ip":"**********","port":8988,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-admin","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.081[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-campus -> [{"instanceId":"**********#8990#DEFAULT#DEFAULT_GROUP@@builder-campus","ip":"**********","port":8990,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-campus","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.082[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-admin -> [{"instanceId":"**********#8988#DEFAULT#DEFAULT_GROUP@@builder-admin","ip":"**********","port":8988,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-admin","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.086[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@distribution -> [{"instanceId":"***********#81#DEFAULT#DEFAULT_GROUP@@distribution","ip":"***********","port":81,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@distribution","metadata":{"preserved.heart.beat.timeout":"15000","spring.application.name":"distribution","preserved.ip.delete.timeout":"30000","env":"root","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"5000"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.082[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-campus -> [{"instanceId":"**********#8990#DEFAULT#DEFAULT_GROUP@@builder-campus","ip":"**********","port":8990,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-campus","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.306[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarted,59][0;39m - Started GatewayXApplication in 16.004 seconds (process running for 17.378)
[1;35m16:38:06.335[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] gateway-x.yml+DEFAULT_GROUP+pedestal
[1;35m16:38:06.335[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=gateway-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m16:38:06.335[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=gateway-x.yml, group=DEFAULT_GROUP
[1;35m16:38:06.337[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal
[1;35m16:38:06.337[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m16:38:06.337[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m16:38:06.339[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal
[1;35m16:38:06.340[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m16:38:06.340[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m16:38:06.455[0;39m [32m[nacos-grpc-client-executor-***********-30][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7164
[1;35m16:38:06.456[0;39m [32m[nacos-grpc-client-executor-***********-30][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.457[0;39m [32m[nacos-grpc-client-executor-***********-30][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:38:06.469[0;39m [32m[nacos-grpc-client-executor-***********-30][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7164
[1;35m16:38:06.655[0;39m [32m[nacos-grpc-client-executor-***********-15][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7166
[1;35m16:38:06.656[0;39m [32m[nacos-grpc-client-executor-***********-15][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7166
[1;35m16:38:06.659[0;39m [32m[nacos-grpc-client-executor-***********-16][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7167
[1;35m16:38:06.659[0;39m [32m[nacos-grpc-client-executor-***********-16][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7167
[1;35m16:38:06.661[0;39m [32m[nacos-grpc-client-executor-***********-17][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7168
[1;35m16:38:06.661[0;39m [32m[nacos-grpc-client-executor-***********-17][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7168
[1;35m16:38:06.664[0;39m [32m[nacos-grpc-client-executor-***********-18][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7169
[1;35m16:38:06.665[0;39m [32m[nacos-grpc-client-executor-***********-18][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7169
[1;35m16:38:06.666[0;39m [32m[nacos-grpc-client-executor-***********-20][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7170
[1;35m16:38:06.667[0;39m [32m[nacos-grpc-client-executor-***********-20][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7170
[1;35m16:38:06.669[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7171
[1;35m16:38:06.669[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7171
[1;35m16:38:06.671[0;39m [32m[nacos-grpc-client-executor-***********-22][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7172
[1;35m16:38:06.672[0;39m [32m[nacos-grpc-client-executor-***********-22][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7172
[1;35m16:38:06.674[0;39m [32m[nacos-grpc-client-executor-***********-21][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7173
[1;35m16:38:06.674[0;39m [32m[nacos-grpc-client-executor-***********-21][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7173
[1;35m16:38:06.676[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7174
[1;35m16:38:06.677[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7174
[1;35m16:38:06.678[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7175
[1;35m16:38:06.678[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7175
[1;35m16:38:06.679[0;39m [32m[nacos-grpc-client-executor-***********-26][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7176
[1;35m16:38:06.680[0;39m [32m[nacos-grpc-client-executor-***********-26][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7176
[1;35m16:38:06.962[0;39m [32m[RMI TCP Connection(3)-198.18.0.1][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m16:39:15.327[0;39m [32m[nacos-grpc-client-executor-***********-56][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Receive server push request, request = NotifySubscriberRequest, requestId = 7180
[1;35m16:39:15.328[0;39m [32m[nacos-grpc-client-executor-***********-56][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:39:15.328[0;39m [32m[nacos-grpc-client-executor-***********-56][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000},{"instanceId":"***********#10004##DEFAULT_GROUP@@system-x","ip":"***********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"instanceHeartBeatTimeOut":15000,"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000}]
[1;35m16:39:15.344[0;39m [32m[nacos-grpc-client-executor-***********-56][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [00251e39-674b-4103-b578-4819c55e588e] Ack server push request, request = NotifySubscriberRequest, requestId = 7180
[1;35m16:41:30.336[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[deregister,95][0;39m - De-registering from Nacos Server now...
[1;35m16:41:30.337[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[deregisterService,272][0;39m - [DEREGISTER-SERVICE] pedestal deregistering service gateway-x with instance: Instance{instanceId='null', ip='**********', port=10001, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
[1;35m16:41:30.357[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[deregister,115][0;39m - De-registration finished.
[1;35m16:41:30.358[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,180][0;39m - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[1;35m16:41:30.359[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,182][0;39m - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[1;35m16:41:30.360[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,184][0;39m - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[1;35m16:41:30.360[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,182][0;39m - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[1;35m16:41:30.360[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,204][0;39m - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[1;35m16:41:30.361[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,147][0;39m - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[1;35m16:41:30.361[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,149][0;39m - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[1;35m16:41:30.361[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,218][0;39m - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[1;35m16:41:30.362[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,223][0;39m - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[1;35m16:41:30.362[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,468][0;39m - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[1;35m16:41:30.362[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,470][0;39m - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[1;35m16:41:30.362[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,487][0;39m - Shutdown naming grpc client proxy for  uuid->00251e39-674b-4103-b578-4819c55e588e
[1;35m16:41:30.363[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,331][0;39m - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@3faf45c[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 67]
[1;35m16:41:30.363[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[shutdown,425][0;39m - Shutdown rpc client, set status to shutdown
[1;35m16:41:30.363[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[shutdown,427][0;39m - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@42b83e59[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[1;35m16:41:30.363[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1755851884969_**********_60659
[1;35m16:41:30.375[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[shutdown,178][0;39m - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@26dae977[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 206]
[1;35m16:41:30.376[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutDownAndRemove,497][0;39m - shutdown and remove naming rpc client  for uuid ->00251e39-674b-4103-b578-4819c55e588e
[1;35m16:41:30.376[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.a.r.i.CredentialWatcher[0;39m - [36m[stop,107][0;39m - [null] CredentialWatcher is stopped
[1;35m16:41:30.376[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.a.r.i.CredentialService[0;39m - [36m[free,91][0;39m - [null] CredentialService is freed
[1;35m16:41:30.377[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,211][0;39m - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
