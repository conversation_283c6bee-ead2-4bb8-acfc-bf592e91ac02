package com.uino.x.migration.ui;

import java.awt.BorderLayout;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.BoxLayout;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JProgressBar;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.SwingUtilities;
import javax.swing.border.TitledBorder;
import javax.swing.table.DefaultTableModel;
import javax.swing.JTable;

import com.uino.x.migration.config.ConfigManager;
import com.uino.x.migration.config.DatabaseConfig;
import com.uino.x.migration.service.DatabaseService;

import lombok.extern.slf4j.Slf4j;

/**
 * SQL执行面板
 * 提供直接执行SQL语句的功能，与系统配置面板功能一致，但去掉固定的数据映射配置
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
public class SqlExecutionPanel extends JPanel {
    
    private final DatabaseService databaseService;
    private final CombinedDatabaseConfigPanel databaseConfigPanel;
    
    // UI组件
    private JComboBox<String> databaseConfigCombo;
    private JButton refreshConfigButton;
    private JTextField schemaField;
    private JCheckBox prefixMatchCheckBox;
    private JCheckBox suffixMatchCheckBox;
    private JPanel excludeSchemaPanel;
    private List<ExcludeSchemaRow> excludeSchemaRows;
    private JButton addExcludeButton;
    private JTextArea sqlTextArea;
    private JButton executeButton;
    private JButton clearButton;
    private JButton saveConfigButton;
    private JProgressBar progressBar;
    private JTextArea logArea;
    private JTable resultTable;
    private DefaultTableModel tableModel;
    
    // 配置回调
    private Runnable configCallback;

    public SqlExecutionPanel(DatabaseService databaseService, CombinedDatabaseConfigPanel databaseConfigPanel) {
        this.databaseService = databaseService;
        this.databaseConfigPanel = databaseConfigPanel;
        this.excludeSchemaRows = new ArrayList<>();

        initializeComponents();
        setupLayout();
        setupEventHandlers();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        // 数据库配置选择
        databaseConfigCombo = new JComboBox<>();
        databaseConfigCombo.addItem("请选择数据库配置");
        refreshConfigButton = new JButton("刷新配置");

        // Schema配置
        schemaField = new JTextField();
        prefixMatchCheckBox = new JCheckBox("前缀匹配");
        suffixMatchCheckBox = new JCheckBox("后缀匹配");

        // 排除Schema面板
        excludeSchemaPanel = new JPanel();
        excludeSchemaPanel.setLayout(new BoxLayout(excludeSchemaPanel, BoxLayout.Y_AXIS));
        addExcludeButton = new JButton("添加排除Schema");

        // SQL输入区域
        sqlTextArea = new JTextArea(8, 50);
        sqlTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        sqlTextArea.setLineWrap(true);
        sqlTextArea.setWrapStyleWord(true);
        sqlTextArea.setTabSize(4);
        sqlTextArea.setText("-- 请在此输入SQL语句\nSELECT * FROM sys_config LIMIT 10;");

        executeButton = new JButton("执行SQL");
        clearButton = new JButton("清空");
        saveConfigButton = new JButton("保存配置");

        // 进度条和日志
        progressBar = new JProgressBar(0, 100);
        progressBar.setStringPainted(true);
        progressBar.setString("就绪");

        logArea = new JTextArea(8, 50);
        logArea.setEditable(false);
        logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        // 结果表格
        tableModel = new DefaultTableModel();
        resultTable = new JTable(tableModel);
        resultTable.setAutoResizeMode(JTable.AUTO_RESIZE_OFF);
        resultTable.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 11));

        // 添加一个默认的排除Schema行
        addExcludeSchemaRow();
    }
    
    /**
     * 设置布局
     */
    private void setupLayout() {
        setLayout(new BorderLayout());

        // 创建主面板
        JPanel mainPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // 数据库配置选择
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 1;
        gbc.weightx = 1.0;
        gbc.weighty = 0.0;
        mainPanel.add(createDatabaseConfigSelectionPanel(), gbc);

        // Schema配置
        gbc.gridy = 1;
        mainPanel.add(createSchemaConfigPanel(), gbc);

        // 排除Schema配置
        gbc.gridy = 2;
        gbc.weighty = 0.2;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createExcludeSchemaPanel(), gbc);

        // SQL输入区域
        gbc.gridy = 3;
        gbc.weighty = 0.3;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createSqlInputPanel(), gbc);

        // 操作按钮
        gbc.gridy = 4;
        gbc.weighty = 0.0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        mainPanel.add(createButtonPanel(), gbc);

        // 结果显示区域
        gbc.gridy = 5;
        gbc.weighty = 0.3;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createResultPanel(), gbc);

        // 进度和日志
        gbc.gridy = 6;
        gbc.weighty = 0.4;
        gbc.fill = GridBagConstraints.BOTH;
        mainPanel.add(createProgressAndLogPanel(), gbc);

        add(mainPanel, BorderLayout.CENTER);
    }
    
    /**
     * 创建数据库配置选择面板
     */
    private JPanel createDatabaseConfigSelectionPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "数据库配置", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        
        // 数据库配置选择
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("数据库配置:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(databaseConfigCombo, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
        panel.add(refreshConfigButton, gbc);
        
        return panel;
    }
    
    /**
     * 创建Schema配置面板
     */
    private JPanel createSchemaConfigPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "Schema配置",
            TitledBorder.LEFT, TitledBorder.TOP));

        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        // Schema配置
        gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("Schema:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
        panel.add(schemaField, gbc);

        // 匹配选项
        gbc.gridx = 0; gbc.gridy = 1; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        JPanel matchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        matchPanel.add(prefixMatchCheckBox);
        matchPanel.add(suffixMatchCheckBox);
        panel.add(matchPanel, gbc);

        return panel;
    }

    /**
     * 创建排除Schema配置面板
     */
    private JPanel createExcludeSchemaPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "排除Schema配置",
            TitledBorder.LEFT, TitledBorder.TOP));

        // 添加按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        buttonPanel.add(addExcludeButton);
        panel.add(buttonPanel, BorderLayout.NORTH);

        // 排除列表面板
        JScrollPane scrollPane = new JScrollPane(excludeSchemaPanel);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
        scrollPane.setPreferredSize(new Dimension(0, 120));
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建SQL输入面板
     */
    private JPanel createSqlInputPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "SQL输入",
            TitledBorder.LEFT, TitledBorder.TOP));

        JScrollPane scrollPane = new JScrollPane(sqlTextArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        panel.add(scrollPane, BorderLayout.CENTER);

        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.add(executeButton);
        panel.add(clearButton);
        panel.add(saveConfigButton);
        return panel;
    }
    
    /**
     * 创建结果显示面板
     */
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "执行结果", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        JScrollPane scrollPane = new JScrollPane(resultTable);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setPreferredSize(new Dimension(0, 200));
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建进度和日志面板
     */
    private JPanel createProgressAndLogPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder(
            BorderFactory.createEtchedBorder(), "执行进度和日志", 
            TitledBorder.LEFT, TitledBorder.TOP));
        
        // 进度条
        panel.add(progressBar, BorderLayout.NORTH);
        
        // 日志区域
        JScrollPane logScrollPane = new JScrollPane(logArea);
        logScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_ALWAYS);
        panel.add(logScrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 设置事件处理器
     */
    private void setupEventHandlers() {
        // 刷新配置按钮
        refreshConfigButton.addActionListener(e -> refreshDatabaseConfigs());

        // 数据库配置选择变化
        databaseConfigCombo.addActionListener(e -> onDatabaseConfigChanged());

        // 添加排除Schema按钮
        addExcludeButton.addActionListener(e -> addExcludeSchemaRow());

        // 执行按钮
        executeButton.addActionListener(e -> executeSql());

        // 清空按钮
        clearButton.addActionListener(e -> clearAll());

        // 保存配置按钮
        saveConfigButton.addActionListener(e -> saveCurrentConfig());
    }
    
    /**
     * 添加日志
     */
    private void addLog(String message) {
        SwingUtilities.invokeLater(() -> {
            logArea.append("[" + java.time.LocalTime.now().toString() + "] " + message + "\n");
            logArea.setCaretPosition(logArea.getDocument().getLength());
        });
    }

    /**
     * 刷新数据库配置列表
     */
    private void refreshDatabaseConfigs() {
        databaseConfigCombo.removeAllItems();
        databaseConfigCombo.addItem("请选择数据库配置");

        // 获取所有命名配置
        java.util.Map<String, ConfigManager.NamedConfig> namedConfigs = ConfigManager.getAllNamedConfigs();
        for (String configName : namedConfigs.keySet()) {
            databaseConfigCombo.addItem(configName);
        }

        addLog("数据库配置列表已刷新");
    }

    /**
     * 数据库配置选择变化事件
     */
    private void onDatabaseConfigChanged() {
        String selectedConfigName = (String) databaseConfigCombo.getSelectedItem();
        if (selectedConfigName == null || "请选择数据库配置".equals(selectedConfigName)) {
            return;
        }

        try {
            ConfigManager.NamedConfig namedConfig = ConfigManager.getNamedConfig(selectedConfigName);
            if (namedConfig != null) {
                addLog("已选择数据库配置: " + selectedConfigName);
            }
        } catch (Exception e) {
            log.error("选择数据库配置失败", e);
            addLog("选择数据库配置失败: " + e.getMessage());
        }
    }

    /**
     * 执行SQL
     */
    private void executeSql() {
        // 获取数据库配置
        String selectedConfigName = (String) databaseConfigCombo.getSelectedItem();
        if (selectedConfigName == null || selectedConfigName.trim().isEmpty() || "请选择数据库配置".equals(selectedConfigName)) {
            JOptionPane.showMessageDialog(this,
                "请选择数据库配置",
                "配置错误",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 获取SQL语句
        String sql = sqlTextArea.getText();
        if (sql == null || sql.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "请输入SQL语句",
                "输入错误",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 获取Schema输入
        String schemaInput = schemaField.getText();
        if (schemaInput == null || schemaInput.trim().isEmpty()) {
            JOptionPane.showMessageDialog(this,
                "请输入Schema",
                "输入错误",
                JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 在后台线程中执行
        SwingUtilities.invokeLater(() -> {
            executeButton.setEnabled(false);
            progressBar.setValue(0);
            progressBar.setString("正在执行SQL...");

            new Thread(() -> {
                try {
                    performSqlExecution(selectedConfigName, sql.trim(), schemaInput.trim());
                } finally {
                    SwingUtilities.invokeLater(() -> {
                        executeButton.setEnabled(true);
                        progressBar.setString("执行完成");
                    });
                }
            }).start();
        });
    }

    /**
     * 执行实际的SQL操作
     */
    private void performSqlExecution(String configName, String sql, String schemaInput) {
        try {
            addLog("开始执行SQL...");
            addLog("配置: " + configName);
            addLog("Schema: " + schemaInput);
            addLog("SQL: " + sql);

            // 获取数据库配置
            ConfigManager.NamedConfig namedConfig = ConfigManager.getNamedConfig(configName);
            if (namedConfig == null) {
                addLog("错误: 无法找到配置 " + configName);
                return;
            }

            // 获取数据库连接
            DatabaseConfig sourceDb = namedConfig.getSourceDatabase();
            if (sourceDb == null) {
                addLog("错误: 配置中没有源数据库信息");
                return;
            }

            SwingUtilities.invokeLater(() -> {
                progressBar.setValue(10);
                progressBar.setString("正在连接数据库...");
            });

            try (Connection connection = databaseService.createDataSource(sourceDb).getConnection()) {
                addLog("数据库连接成功");

                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(20);
                    progressBar.setString("正在解析Schema...");
                });

                // 构建Schema列表
                List<String> schemas = getTargetSchemas(sourceDb, schemaInput);
                addLog("将处理 " + schemas.size() + " 个Schema: " + schemas);

                if (schemas.isEmpty()) {
                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(100);
                        progressBar.setString("没有找到匹配的Schema");
                    });
                    addLog("没有找到匹配的Schema，操作完成");
                    return;
                }

                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(30);
                    progressBar.setString("开始执行SQL...");
                });

                // 清空之前的结果并设置表头
                SwingUtilities.invokeLater(() -> {
                    tableModel.setRowCount(0);
                    tableModel.setColumnCount(0);
                    tableModel.setColumnIdentifiers(new String[]{"Schema", "执行状态", "影响行数", "备注"});
                });

                int totalAffectedRows = 0;
                int successCount = 0;
                int schemaCount = schemas.size();

                for (int i = 0; i < schemaCount; i++) {
                    String schema = schemas.get(i);
                    final int currentIndex = i;
                    final int baseProgress = 30 + (60 * currentIndex / schemaCount);

                    SwingUtilities.invokeLater(() -> {
                        progressBar.setValue(baseProgress);
                        progressBar.setString("正在处理Schema: " + schema + " (" + (currentIndex + 1) + "/" + schemaCount + ")");
                    });
                    addLog("正在处理Schema: " + schema + " (" + (currentIndex + 1) + "/" + schemaCount + ")");

                    try {
                        int affectedRows = executeSqlInSchema(connection, schema, sql);
                        totalAffectedRows += affectedRows;
                        successCount++;

                        // 添加执行结果到表格
                        final int finalAffectedRows = affectedRows;
                        SwingUtilities.invokeLater(() -> {
                            String status = "成功";
                            String remark = sql.toLowerCase().trim().startsWith("select") ? "查询返回 " + finalAffectedRows + " 行" : "更新操作";
                            tableModel.addRow(new Object[]{schema, status, finalAffectedRows, remark});
                        });

                        addLog("Schema " + schema + " 执行成功，影响 " + affectedRows + " 行");
                    } catch (SQLException e) {
                        addLog("Schema " + schema + " 执行失败: " + e.getMessage());

                        // 添加失败结果到表格
                        SwingUtilities.invokeLater(() -> {
                            tableModel.addRow(new Object[]{schema, "失败", 0, e.getMessage()});
                        });
                    }
                }

                SwingUtilities.invokeLater(() -> {
                    progressBar.setValue(100);
                    progressBar.setString("SQL执行完成");
                });
                addLog("SQL执行完成！总共处理了 " + schemaCount + " 个Schema，成功 " + successCount + " 个，总共影响了 " + totalAffectedRows + " 行");

                // 弹框提示完成
                final int finalTotalAffectedRows = totalAffectedRows;
                final int finalSuccessCount = successCount;
                final int finalSchemaCount = schemaCount;
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                        "SQL执行完成！\n" +
                        "处理Schema数量: " + finalSchemaCount + "\n" +
                        "成功执行: " + finalSuccessCount + "\n" +
                        "失败数量: " + (finalSchemaCount - finalSuccessCount) + "\n" +
                        "总影响行数: " + finalTotalAffectedRows,
                        "执行完成",
                        JOptionPane.INFORMATION_MESSAGE);
                });

            } catch (SQLException e) {
                log.error("数据库操作失败", e);
                addLog("数据库操作失败: " + e.getMessage());

                // 弹框提示错误
                SwingUtilities.invokeLater(() -> {
                    JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                        "数据库操作失败：" + e.getMessage(),
                        "执行失败",
                        JOptionPane.ERROR_MESSAGE);
                });
            }

        } catch (Exception e) {
            log.error("执行SQL失败", e);
            addLog("执行失败: " + e.getMessage());

            // 弹框提示错误
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                    "执行失败：" + e.getMessage(),
                    "执行失败",
                    JOptionPane.ERROR_MESSAGE);
            });
        }
    }

    /**
     * 执行查询语句
     */
    private void executeQuery(Connection connection, String sql) throws SQLException {
        try (Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery(sql)) {

            // 获取结果集元数据
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 清空之前的结果
            SwingUtilities.invokeLater(() -> {
                tableModel.setRowCount(0);
                tableModel.setColumnCount(0);
            });

            // 设置列名
            String[] columnNames = new String[columnCount];
            for (int i = 1; i <= columnCount; i++) {
                columnNames[i - 1] = metaData.getColumnName(i);
            }

            SwingUtilities.invokeLater(() -> {
                tableModel.setColumnIdentifiers(columnNames);
            });

            // 添加数据行
            List<Object[]> rows = new ArrayList<>();
            int rowCount = 0;
            while (resultSet.next() && rowCount < 1000) { // 限制最多显示1000行
                Object[] row = new Object[columnCount];
                for (int i = 1; i <= columnCount; i++) {
                    row[i - 1] = resultSet.getObject(i);
                }
                rows.add(row);
                rowCount++;
            }

            SwingUtilities.invokeLater(() -> {
                for (Object[] row : rows) {
                    tableModel.addRow(row);
                }
            });

            addLog("查询完成，返回 " + rowCount + " 行数据");
            if (rowCount >= 1000) {
                addLog("注意：结果已限制为前1000行");
            }
        }
    }

    /**
     * 执行更新语句
     */
    private void executeUpdate(Connection connection, String sql) throws SQLException {
        try (Statement statement = connection.createStatement()) {
            int affectedRows = statement.executeUpdate(sql);

            // 清空结果表格
            SwingUtilities.invokeLater(() -> {
                tableModel.setRowCount(0);
                tableModel.setColumnCount(0);
                tableModel.setColumnIdentifiers(new String[]{"执行结果"});
                tableModel.addRow(new Object[]{"影响行数: " + affectedRows});
            });

            addLog("更新完成，影响 " + affectedRows + " 行");

            // 弹框提示完成
            SwingUtilities.invokeLater(() -> {
                JOptionPane.showMessageDialog(SqlExecutionPanel.this,
                    "SQL执行完成！\n影响行数: " + affectedRows,
                    "执行完成",
                    JOptionPane.INFORMATION_MESSAGE);
            });
        }
    }

    /**
     * 清空所有内容
     */
    private void clearAll() {
        sqlTextArea.setText("-- 请在此输入SQL语句\n");
        logArea.setText("");
        tableModel.setRowCount(0);
        tableModel.setColumnCount(0);
        progressBar.setValue(0);
        progressBar.setString("就绪");
        addLog("界面已清空");
    }

    /**
     * 设置配置回调
     */
    public void setConfigCallback(Runnable configCallback) {
        this.configCallback = configCallback;
    }

    /**
     * 初始化时刷新配置列表
     */
    public void initializeDatabaseConfigs() {
        refreshDatabaseConfigs();
    }

    /**
     * 保存当前配置
     */
    private void saveCurrentConfig() {
        // 直接保存到主配置，不弹框
        if (configCallback != null) {
            configCallback.run();
            JOptionPane.showMessageDialog(this,
                "SQL执行面板配置已保存",
                "保存成功",
                JOptionPane.INFORMATION_MESSAGE);
            log.info("SQL执行面板配置已保存");
        }
    }

    /**
     * 获取当前SQL执行配置
     */
    public ConfigManager.SqlExecutionConfig getSqlExecutionConfig() {
        ConfigManager.SqlExecutionConfig config = new ConfigManager.SqlExecutionConfig();

        // 设置选择的数据库配置
        String selectedConfig = (String) databaseConfigCombo.getSelectedItem();
        if (selectedConfig != null && !"请选择数据库配置".equals(selectedConfig)) {
            config.setSelectedDatabaseConfig(selectedConfig);
        }

        // 设置SQL内容
        config.setSqlContent(sqlTextArea.getText());

        // 设置Schema配置
        config.setSchemaInput(schemaField.getText());
        config.setPrefixMatch(prefixMatchCheckBox.isSelected());
        config.setSuffixMatch(suffixMatchCheckBox.isSelected());

        // 设置排除Schema列表
        config.setExcludeSchemas(getExcludeSchemas());

        return config;
    }

    /**
     * 设置SQL执行配置到UI组件
     */
    public void setSqlExecutionConfig(ConfigManager.SqlExecutionConfig config) {
        if (config == null) return;

        // 设置选择的数据库配置
        if (config.getSelectedDatabaseConfig() != null) {
            databaseConfigCombo.setSelectedItem(config.getSelectedDatabaseConfig());
            onDatabaseConfigChanged();
        }

        // 设置SQL内容
        if (config.getSqlContent() != null) {
            sqlTextArea.setText(config.getSqlContent());
        }

        // 设置Schema配置
        if (config.getSchemaInput() != null) {
            schemaField.setText(config.getSchemaInput());
        }
        prefixMatchCheckBox.setSelected(config.isPrefixMatch());
        suffixMatchCheckBox.setSelected(config.isSuffixMatch());

        // 设置排除Schema列表
        excludeSchemaRows.clear();
        excludeSchemaPanel.removeAll();
        if (config.getExcludeSchemas() != null && !config.getExcludeSchemas().isEmpty()) {
            for (String excludeSchema : config.getExcludeSchemas()) {
                ExcludeSchemaRow row = new ExcludeSchemaRow();
                row.setSchemaName(excludeSchema);
                excludeSchemaRows.add(row);
                excludeSchemaPanel.add(row.getPanel());
            }
        } else {
            // 添加一个默认的排除Schema行
            addExcludeSchemaRow();
        }
        excludeSchemaPanel.revalidate();
        excludeSchemaPanel.repaint();
    }

    /**
     * 添加排除Schema行
     */
    private void addExcludeSchemaRow() {
        ExcludeSchemaRow row = new ExcludeSchemaRow();
        excludeSchemaRows.add(row);
        excludeSchemaPanel.add(row.getPanel());
        excludeSchemaPanel.revalidate();
        excludeSchemaPanel.repaint();
    }

    /**
     * 获取目标Schema列表（使用与系统配置修改相同的逻辑）
     */
    private List<String> getTargetSchemas(DatabaseConfig config, String schemaInput) throws SQLException {
        List<String> allSchemas = databaseService.getSchemas(config);
        List<String> targetSchemas = new ArrayList<>();

        if (schemaInput == null || schemaInput.trim().isEmpty()) {
            addLog("Schema输入为空");
            return targetSchemas;
        }

        String trimmedInput = schemaInput.trim();
        boolean prefixMatch = prefixMatchCheckBox.isSelected();
        boolean suffixMatch = suffixMatchCheckBox.isSelected();

        if (!prefixMatch && !suffixMatch) {
            // 直接匹配
            if (allSchemas.contains(trimmedInput)) {
                targetSchemas.add(trimmedInput);
            }
        } else if (prefixMatch && !suffixMatch) {
            // 前缀匹配
            for (String schema : allSchemas) {
                if (schema.startsWith(trimmedInput)) {
                    targetSchemas.add(schema);
                }
            }
        } else if (!prefixMatch && suffixMatch) {
            // 后缀匹配
            for (String schema : allSchemas) {
                if (schema.endsWith(trimmedInput)) {
                    targetSchemas.add(schema);
                }
            }
        } else {
            // 同时选择前缀和后缀匹配，这种情况下使用前缀匹配
            addLog("同时选择了前缀和后缀匹配，将使用前缀匹配");
            for (String schema : allSchemas) {
                if (schema.startsWith(trimmedInput)) {
                    targetSchemas.add(schema);
                }
            }
        }

        // 应用排除列表
        List<String> excludeSchemas = getExcludeSchemas();
        if (!excludeSchemas.isEmpty()) {
            targetSchemas.removeAll(excludeSchemas);
            addLog("排除Schema: " + excludeSchemas);
        }

        return targetSchemas;
    }

    /**
     * 获取排除Schema列表
     */
    private List<String> getExcludeSchemas() {
        List<String> excludeSchemas = new ArrayList<>();
        for (ExcludeSchemaRow row : excludeSchemaRows) {
            String schemaName = row.getSchemaName();
            if (!schemaName.isEmpty()) {
                excludeSchemas.add(schemaName);
            }
        }
        return excludeSchemas;
    }

    /**
     * 在指定Schema中执行SQL
     */
    private int executeSqlInSchema(Connection connection, String schema, String sql) throws SQLException {
        // 尝试切换到指定的schema（支持不同数据库类型）
        boolean schemaSwitched = false;
        try (Statement schemaStatement = connection.createStatement()) {
            // 尝试不同的schema切换语句
            String[] switchCommands = {
                "USE " + schema,           // MySQL
                "SET SCHEMA " + schema,    // DB2, PostgreSQL
                "ALTER SESSION SET CURRENT_SCHEMA = " + schema  // Oracle, DM
            };

            for (String command : switchCommands) {
                try {
                    schemaStatement.execute(command);
                    schemaSwitched = true;
                    addLog("成功切换到Schema: " + schema + " (使用命令: " + command + ")");
                    break;
                } catch (SQLException e) {
                    // 继续尝试下一个命令
                }
            }

            if (!schemaSwitched) {
                addLog("警告: 无法切换schema，将在SQL中添加schema前缀");
            }
        }

        // 处理SQL，确保使用正确的schema
        String actualSql = schemaSwitched ? sql : processSqlWithSchema(sql, schema);
        addLog("在Schema " + schema + " 中执行SQL: " + actualSql);

        try (Statement statement = connection.createStatement()) {
            // 判断SQL类型
            String sqlLower = actualSql.toLowerCase().trim();
            if (sqlLower.startsWith("select") || sqlLower.startsWith("show") || sqlLower.startsWith("desc") || sqlLower.startsWith("explain")) {
                // 查询语句 - 只返回行数，不显示具体数据
                try (ResultSet resultSet = statement.executeQuery(actualSql)) {
                    int rowCount = 0;
                    while (resultSet.next()) {
                        rowCount++;
                    }
                    return rowCount;
                }
            } else {
                // 更新语句
                return statement.executeUpdate(actualSql);
            }
        }
    }

    /**
     * 处理SQL，添加schema前缀
     */
    private String processSqlWithSchema(String sql, String schema) {
        // 如果SQL中已经包含了schema前缀，直接返回
        if (sql.toLowerCase().contains(schema.toLowerCase() + ".")) {
            return sql;
        }

        // 为SQL添加schema前缀
        String processedSql = sql;

        // 处理FROM子句
        processedSql = processedSql.replaceAll("(?i)\\bFROM\\s+(\\w+)(?!\\s*\\.)", "FROM " + schema + ".$1");

        // 处理UPDATE语句
        processedSql = processedSql.replaceAll("(?i)\\bUPDATE\\s+(\\w+)(?!\\s*\\.)", "UPDATE " + schema + ".$1");

        // 处理INSERT INTO语句
        processedSql = processedSql.replaceAll("(?i)\\bINTO\\s+(\\w+)(?!\\s*\\.)", "INTO " + schema + ".$1");

        // 处理JOIN语句
        processedSql = processedSql.replaceAll("(?i)\\bJOIN\\s+(\\w+)(?!\\s*\\.)", "JOIN " + schema + ".$1");
        processedSql = processedSql.replaceAll("(?i)\\bINNER\\s+JOIN\\s+(\\w+)(?!\\s*\\.)", "INNER JOIN " + schema + ".$1");
        processedSql = processedSql.replaceAll("(?i)\\bLEFT\\s+JOIN\\s+(\\w+)(?!\\s*\\.)", "LEFT JOIN " + schema + ".$1");
        processedSql = processedSql.replaceAll("(?i)\\bRIGHT\\s+JOIN\\s+(\\w+)(?!\\s*\\.)", "RIGHT JOIN " + schema + ".$1");

        return processedSql;
    }

    /**
     * 排除Schema行组件
     */
    private class ExcludeSchemaRow {
        private JPanel panel;
        private JTextField schemaNameField;
        private JButton removeButton;

        public ExcludeSchemaRow() {
            schemaNameField = new JTextField(20);
            removeButton = new JButton("删除");

            panel = new JPanel(new GridBagLayout());
            panel.setBorder(BorderFactory.createEtchedBorder());
            GridBagConstraints gbc = new GridBagConstraints();
            gbc.insets = new Insets(5, 5, 5, 5);

            gbc.gridx = 0; gbc.gridy = 0; gbc.anchor = GridBagConstraints.WEST;
            panel.add(new JLabel("排除Schema:"), gbc);
            gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL; gbc.weightx = 1.0;
            panel.add(schemaNameField, gbc);

            gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE; gbc.weightx = 0;
            panel.add(removeButton, gbc);

            // 删除按钮事件
            removeButton.addActionListener(e -> {
                excludeSchemaRows.remove(this);
                excludeSchemaPanel.remove(panel);
                excludeSchemaPanel.revalidate();
                excludeSchemaPanel.repaint();
            });
        }

        public JPanel getPanel() {
            return panel;
        }

        public String getSchemaName() {
            return schemaNameField.getText().trim();
        }

        public void setSchemaName(String schemaName) {
            schemaNameField.setText(schemaName);
        }
    }
}
