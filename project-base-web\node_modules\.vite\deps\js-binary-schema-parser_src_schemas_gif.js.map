{"version": 3, "sources": ["../../js-binary-schema-parser/src/schemas/gif.js"], "sourcesContent": ["import { conditional, loop } from '../'\r\nimport {\r\n  readByte,\r\n  peekByte,\r\n  readBytes,\r\n  peekBytes,\r\n  readString,\r\n  readUnsigned,\r\n  readArray,\r\n  readBits,\r\n} from '../parsers/uint8'\r\n\r\n// a set of 0x00 terminated subblocks\r\nvar subBlocksSchema = {\r\n  blocks: (stream) => {\r\n    const terminator = 0x00\r\n    const chunks = []\r\n    const streamSize = stream.data.length\r\n    var total = 0\r\n    for (\r\n      var size = readByte()(stream);\r\n      size !== terminator;\r\n      size = readByte()(stream)\r\n    ) {\r\n      // size becomes undefined for some case when file is corrupted and  terminator is not proper \r\n      // null check to avoid recursion\r\n      if(!size) break;\r\n      // catch corrupted files with no terminator\r\n      if (stream.pos + size >= streamSize) {\r\n        const availableSize = streamSize - stream.pos\r\n        chunks.push(readBytes(availableSize)(stream))\r\n        total += availableSize\r\n        break\r\n      }\r\n      chunks.push(readBytes(size)(stream))\r\n      total += size\r\n    }\r\n    const result = new Uint8Array(total)\r\n    var offset = 0\r\n    for (var i = 0; i < chunks.length; i++) {\r\n      result.set(chunks[i], offset)\r\n      offset += chunks[i].length\r\n    }\r\n    return result\r\n  },\r\n}\r\n\r\n// global control extension\r\nconst gceSchema = conditional(\r\n  {\r\n    gce: [\r\n      { codes: readBytes(2) },\r\n      { byteSize: readByte() },\r\n      {\r\n        extras: readBits({\r\n          future: { index: 0, length: 3 },\r\n          disposal: { index: 3, length: 3 },\r\n          userInput: { index: 6 },\r\n          transparentColorGiven: { index: 7 },\r\n        }),\r\n      },\r\n      { delay: readUnsigned(true) },\r\n      { transparentColorIndex: readByte() },\r\n      { terminator: readByte() },\r\n    ],\r\n  },\r\n  (stream) => {\r\n    var codes = peekBytes(2)(stream)\r\n    return codes[0] === 0x21 && codes[1] === 0xf9\r\n  }\r\n)\r\n\r\n// image pipeline block\r\nconst imageSchema = conditional(\r\n  {\r\n    image: [\r\n      { code: readByte() },\r\n      {\r\n        descriptor: [\r\n          { left: readUnsigned(true) },\r\n          { top: readUnsigned(true) },\r\n          { width: readUnsigned(true) },\r\n          { height: readUnsigned(true) },\r\n          {\r\n            lct: readBits({\r\n              exists: { index: 0 },\r\n              interlaced: { index: 1 },\r\n              sort: { index: 2 },\r\n              future: { index: 3, length: 2 },\r\n              size: { index: 5, length: 3 },\r\n            }),\r\n          },\r\n        ],\r\n      },\r\n      conditional(\r\n        {\r\n          lct: readArray(3, (stream, result, parent) => {\r\n            return Math.pow(2, parent.descriptor.lct.size + 1)\r\n          }),\r\n        },\r\n        (stream, result, parent) => {\r\n          return parent.descriptor.lct.exists\r\n        }\r\n      ),\r\n      { data: [{ minCodeSize: readByte() }, subBlocksSchema] },\r\n    ],\r\n  },\r\n  (stream) => {\r\n    return peekByte()(stream) === 0x2c\r\n  }\r\n)\r\n\r\n// plain text block\r\nconst textSchema = conditional(\r\n  {\r\n    text: [\r\n      { codes: readBytes(2) },\r\n      { blockSize: readByte() },\r\n      {\r\n        preData: (stream, result, parent) =>\r\n          readBytes(parent.text.blockSize)(stream),\r\n      },\r\n      subBlocksSchema,\r\n    ],\r\n  },\r\n  (stream) => {\r\n    var codes = peekBytes(2)(stream)\r\n    return codes[0] === 0x21 && codes[1] === 0x01\r\n  }\r\n)\r\n\r\n// application block\r\nconst applicationSchema = conditional(\r\n  {\r\n    application: [\r\n      { codes: readBytes(2) },\r\n      { blockSize: readByte() },\r\n      { id: (stream, result, parent) => readString(parent.blockSize)(stream) },\r\n      subBlocksSchema,\r\n    ],\r\n  },\r\n  (stream) => {\r\n    var codes = peekBytes(2)(stream)\r\n    return codes[0] === 0x21 && codes[1] === 0xff\r\n  }\r\n)\r\n\r\n// comment block\r\nconst commentSchema = conditional(\r\n  {\r\n    comment: [{ codes: readBytes(2) }, subBlocksSchema],\r\n  },\r\n  (stream) => {\r\n    var codes = peekBytes(2)(stream)\r\n    return codes[0] === 0x21 && codes[1] === 0xfe\r\n  }\r\n)\r\n\r\nconst schema = [\r\n  { header: [{ signature: readString(3) }, { version: readString(3) }] },\r\n  {\r\n    lsd: [\r\n      { width: readUnsigned(true) },\r\n      { height: readUnsigned(true) },\r\n      {\r\n        gct: readBits({\r\n          exists: { index: 0 },\r\n          resolution: { index: 1, length: 3 },\r\n          sort: { index: 4 },\r\n          size: { index: 5, length: 3 },\r\n        }),\r\n      },\r\n      { backgroundColorIndex: readByte() },\r\n      { pixelAspectRatio: readByte() },\r\n    ],\r\n  },\r\n  conditional(\r\n    {\r\n      gct: readArray(3, (stream, result) =>\r\n        Math.pow(2, result.lsd.gct.size + 1)\r\n      ),\r\n    },\r\n    (stream, result) => result.lsd.gct.exists\r\n  ),\r\n  // content frames\r\n  {\r\n    frames: loop(\r\n      [gceSchema, applicationSchema, commentSchema, imageSchema, textSchema],\r\n      (stream) => {\r\n        var nextCode = peekByte()(stream)\r\n        // rather than check for a terminator, we should check for the existence\r\n        // of an ext or image block to avoid infinite loops\r\n        //var terminator = 0x3B;\r\n        //return nextCode !== terminator;\r\n        return nextCode === 0x21 || nextCode === 0x2c\r\n      }\r\n    ),\r\n  },\r\n]\r\n\r\nexport default schema\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAaA,IAAI,kBAAkB;AAAA,EACpB,QAAQ,CAAC,WAAW;AAClB,UAAM,aAAa;AACnB,UAAM,SAAS,CAAC;AAChB,UAAM,aAAa,OAAO,KAAK;AAC/B,QAAI,QAAQ;AACZ,aACM,OAAO,SAAS,EAAE,MAAM,GAC5B,SAAS,YACT,OAAO,SAAS,EAAE,MAAM,GACxB;AAGA,UAAG,CAAC,KAAM;AAEV,UAAI,OAAO,MAAM,QAAQ,YAAY;AACnC,cAAM,gBAAgB,aAAa,OAAO;AAC1C,eAAO,KAAK,UAAU,aAAa,EAAE,MAAM,CAAC;AAC5C,iBAAS;AACT;AAAA,MACF;AACA,aAAO,KAAK,UAAU,IAAI,EAAE,MAAM,CAAC;AACnC,eAAS;AAAA,IACX;AACA,UAAM,SAAS,IAAI,WAAW,KAAK;AACnC,QAAI,SAAS;AACb,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,aAAO,IAAI,OAAO,CAAC,GAAG,MAAM;AAC5B,gBAAU,OAAO,CAAC,EAAE;AAAA,IACtB;AACA,WAAO;AAAA,EACT;AACF;AAGA,IAAM,YAAY;AAAA,EAChB;AAAA,IACE,KAAK;AAAA,MACH,EAAE,OAAO,UAAU,CAAC,EAAE;AAAA,MACtB,EAAE,UAAU,SAAS,EAAE;AAAA,MACvB;AAAA,QACE,QAAQ,SAAS;AAAA,UACf,QAAQ,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,UAC9B,UAAU,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,UAChC,WAAW,EAAE,OAAO,EAAE;AAAA,UACtB,uBAAuB,EAAE,OAAO,EAAE;AAAA,QACpC,CAAC;AAAA,MACH;AAAA,MACA,EAAE,OAAO,aAAa,IAAI,EAAE;AAAA,MAC5B,EAAE,uBAAuB,SAAS,EAAE;AAAA,MACpC,EAAE,YAAY,SAAS,EAAE;AAAA,IAC3B;AAAA,EACF;AAAA,EACA,CAAC,WAAW;AACV,QAAI,QAAQ,UAAU,CAAC,EAAE,MAAM;AAC/B,WAAO,MAAM,CAAC,MAAM,MAAQ,MAAM,CAAC,MAAM;AAAA,EAC3C;AACF;AAGA,IAAM,cAAc;AAAA,EAClB;AAAA,IACE,OAAO;AAAA,MACL,EAAE,MAAM,SAAS,EAAE;AAAA,MACnB;AAAA,QACE,YAAY;AAAA,UACV,EAAE,MAAM,aAAa,IAAI,EAAE;AAAA,UAC3B,EAAE,KAAK,aAAa,IAAI,EAAE;AAAA,UAC1B,EAAE,OAAO,aAAa,IAAI,EAAE;AAAA,UAC5B,EAAE,QAAQ,aAAa,IAAI,EAAE;AAAA,UAC7B;AAAA,YACE,KAAK,SAAS;AAAA,cACZ,QAAQ,EAAE,OAAO,EAAE;AAAA,cACnB,YAAY,EAAE,OAAO,EAAE;AAAA,cACvB,MAAM,EAAE,OAAO,EAAE;AAAA,cACjB,QAAQ,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,cAC9B,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,YAC9B,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,MACA;AAAA,QACE;AAAA,UACE,KAAK,UAAU,GAAG,CAAC,QAAQ,QAAQ,WAAW;AAC5C,mBAAO,KAAK,IAAI,GAAG,OAAO,WAAW,IAAI,OAAO,CAAC;AAAA,UACnD,CAAC;AAAA,QACH;AAAA,QACA,CAAC,QAAQ,QAAQ,WAAW;AAC1B,iBAAO,OAAO,WAAW,IAAI;AAAA,QAC/B;AAAA,MACF;AAAA,MACA,EAAE,MAAM,CAAC,EAAE,aAAa,SAAS,EAAE,GAAG,eAAe,EAAE;AAAA,IACzD;AAAA,EACF;AAAA,EACA,CAAC,WAAW;AACV,WAAO,SAAS,EAAE,MAAM,MAAM;AAAA,EAChC;AACF;AAGA,IAAM,aAAa;AAAA,EACjB;AAAA,IACE,MAAM;AAAA,MACJ,EAAE,OAAO,UAAU,CAAC,EAAE;AAAA,MACtB,EAAE,WAAW,SAAS,EAAE;AAAA,MACxB;AAAA,QACE,SAAS,CAAC,QAAQ,QAAQ,WACxB,UAAU,OAAO,KAAK,SAAS,EAAE,MAAM;AAAA,MAC3C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,WAAW;AACV,QAAI,QAAQ,UAAU,CAAC,EAAE,MAAM;AAC/B,WAAO,MAAM,CAAC,MAAM,MAAQ,MAAM,CAAC,MAAM;AAAA,EAC3C;AACF;AAGA,IAAM,oBAAoB;AAAA,EACxB;AAAA,IACE,aAAa;AAAA,MACX,EAAE,OAAO,UAAU,CAAC,EAAE;AAAA,MACtB,EAAE,WAAW,SAAS,EAAE;AAAA,MACxB,EAAE,IAAI,CAAC,QAAQ,QAAQ,WAAW,WAAW,OAAO,SAAS,EAAE,MAAM,EAAE;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AAAA,EACA,CAAC,WAAW;AACV,QAAI,QAAQ,UAAU,CAAC,EAAE,MAAM;AAC/B,WAAO,MAAM,CAAC,MAAM,MAAQ,MAAM,CAAC,MAAM;AAAA,EAC3C;AACF;AAGA,IAAM,gBAAgB;AAAA,EACpB;AAAA,IACE,SAAS,CAAC,EAAE,OAAO,UAAU,CAAC,EAAE,GAAG,eAAe;AAAA,EACpD;AAAA,EACA,CAAC,WAAW;AACV,QAAI,QAAQ,UAAU,CAAC,EAAE,MAAM;AAC/B,WAAO,MAAM,CAAC,MAAM,MAAQ,MAAM,CAAC,MAAM;AAAA,EAC3C;AACF;AAEA,IAAM,SAAS;AAAA,EACb,EAAE,QAAQ,CAAC,EAAE,WAAW,WAAW,CAAC,EAAE,GAAG,EAAE,SAAS,WAAW,CAAC,EAAE,CAAC,EAAE;AAAA,EACrE;AAAA,IACE,KAAK;AAAA,MACH,EAAE,OAAO,aAAa,IAAI,EAAE;AAAA,MAC5B,EAAE,QAAQ,aAAa,IAAI,EAAE;AAAA,MAC7B;AAAA,QACE,KAAK,SAAS;AAAA,UACZ,QAAQ,EAAE,OAAO,EAAE;AAAA,UACnB,YAAY,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,UAClC,MAAM,EAAE,OAAO,EAAE;AAAA,UACjB,MAAM,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,MACA,EAAE,sBAAsB,SAAS,EAAE;AAAA,MACnC,EAAE,kBAAkB,SAAS,EAAE;AAAA,IACjC;AAAA,EACF;AAAA,EACA;AAAA,IACE;AAAA,MACE,KAAK;AAAA,QAAU;AAAA,QAAG,CAAC,QAAQ,WACzB,KAAK,IAAI,GAAG,OAAO,IAAI,IAAI,OAAO,CAAC;AAAA,MACrC;AAAA,IACF;AAAA,IACA,CAAC,QAAQ,WAAW,OAAO,IAAI,IAAI;AAAA,EACrC;AAAA;AAAA,EAEA;AAAA,IACE,QAAQ;AAAA,MACN,CAAC,WAAW,mBAAmB,eAAe,aAAa,UAAU;AAAA,MACrE,CAAC,WAAW;AACV,YAAI,WAAW,SAAS,EAAE,MAAM;AAKhC,eAAO,aAAa,MAAQ,aAAa;AAAA,MAC3C;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAO,cAAQ;", "names": []}