{"version": 3, "sources": ["../../vue3-cookies/dist/index.js"], "sourcesContent": ["/**\r\n * Vue3 Cookies v1.0.0\r\n *\r\n * Forked from\r\n * https://github.com/cmp-cc/vue-cookies\r\n * And changed format to support Vue.js 3\r\n *\r\n */\r\nimport { reactive } from \"vue\";\r\nvar defaultConfig = {\r\n    expireTimes: \"1d\",\r\n    path: \"; path=/\",\r\n    domain: \"\",\r\n    secure: false,\r\n    sameSite: \"; SameSite=Lax\",\r\n};\r\nvar VueCookiesManager = /** @class */ (function () {\r\n    function VueCookiesManager() {\r\n        this.current_default_config = defaultConfig;\r\n    }\r\n    VueCookiesManager.prototype.config = function (config) {\r\n        for (var propertyName in this.current_default_config) {\r\n            this.current_default_config[propertyName] = config[propertyName]\r\n                ? config[propertyName]\r\n                : defaultConfig[propertyName];\r\n        }\r\n    };\r\n    VueCookiesManager.prototype.get = function (keyName) {\r\n        var value = decodeURIComponent(document.cookie.replace(new RegExp(\"(?:(?:^|.*;)\\\\s*\" +\r\n            encodeURIComponent(keyName).replace(/[\\-\\.\\+\\*]/g, \"\\\\$&\") +\r\n            \"\\\\s*\\\\=\\\\s*([^;]*).*$)|^.*$\"), \"$1\")) || null;\r\n        if (value &&\r\n            value.substring(0, 1) === \"{\" &&\r\n            value.substring(value.length - 1, value.length) === \"}\") {\r\n            try {\r\n                value = JSON.parse(value);\r\n            }\r\n            catch (e) {\r\n                return value;\r\n            }\r\n        }\r\n        return value;\r\n    };\r\n    VueCookiesManager.prototype.set = function (keyName, value, expireTimes, path, domain, secure, sameSite) {\r\n        if (!keyName) {\r\n            throw new Error(\"Cookie name is not found in the first argument.\");\r\n        }\r\n        else if (/^(?:expires|max-age|path|domain|secure|SameSite)$/i.test(keyName)) {\r\n            throw new Error('Cookie name illegality. Cannot be set to [\"expires\",\"max-age\",\"path\",\"domain\",\"secure\",\"SameSite\"]\\t current key name: ' +\r\n                keyName);\r\n        }\r\n        // support json object\r\n        if (value && value.constructor === Object) {\r\n            value = JSON.stringify(value);\r\n        }\r\n        var _expires = \"\";\r\n        if (expireTimes == undefined) {\r\n            expireTimes = this.current_default_config.expireTimes\r\n                ? this.current_default_config.expireTimes\r\n                : \"\";\r\n        }\r\n        if (expireTimes && expireTimes != 0) {\r\n            switch (expireTimes.constructor) {\r\n                case Number:\r\n                    if (expireTimes === Infinity || expireTimes === -1)\r\n                        _expires = \"; expires=Fri, 31 Dec 9999 23:59:59 GMT\";\r\n                    else\r\n                        _expires = \"; max-age=\" + expireTimes;\r\n                    break;\r\n                case String:\r\n                    if (/^(?:\\d+(y|m|d|h|min|s))$/i.test(expireTimes)) {\r\n                        // get capture number group\r\n                        var _expireTime = expireTimes.replace(/^(\\d+)(?:y|m|d|h|min|s)$/i, \"$1\");\r\n                        // get capture type group , to lower case\r\n                        switch (expireTimes\r\n                            .replace(/^(?:\\d+)(y|m|d|h|min|s)$/i, \"$1\")\r\n                            .toLowerCase()) {\r\n                            // Frequency sorting\r\n                            case \"m\":\r\n                                _expires = \"; max-age=\" + +_expireTime * 2592000;\r\n                                break; // 60 * 60 * 24 * 30\r\n                            case \"d\":\r\n                                _expires = \"; max-age=\" + +_expireTime * 86400;\r\n                                break; // 60 * 60 * 24\r\n                            case \"h\":\r\n                                _expires = \"; max-age=\" + +_expireTime * 3600;\r\n                                break; // 60 * 60\r\n                            case \"min\":\r\n                                _expires = \"; max-age=\" + +_expireTime * 60;\r\n                                break; // 60\r\n                            case \"s\":\r\n                                _expires = \"; max-age=\" + _expireTime;\r\n                                break;\r\n                            case \"y\":\r\n                                _expires = \"; max-age=\" + +_expireTime * 31104000;\r\n                                break; // 60 * 60 * 24 * 30 * 12\r\n                            default:\r\n                                new Error('unknown exception of \"set operation\"');\r\n                        }\r\n                    }\r\n                    else {\r\n                        _expires = \"; expires=\" + expireTimes;\r\n                    }\r\n                    break;\r\n                case Date:\r\n                    _expires = \"; expires=\" + expireTimes.toUTCString();\r\n                    break;\r\n            }\r\n        }\r\n        document.cookie =\r\n            encodeURIComponent(keyName) +\r\n                \"=\" +\r\n                encodeURIComponent(value) +\r\n                _expires +\r\n                (domain\r\n                    ? \"; domain=\" + domain\r\n                    : this.current_default_config.domain\r\n                        ? this.current_default_config.domain\r\n                        : \"\") +\r\n                (path\r\n                    ? \"; path=\" + path\r\n                    : this.current_default_config.path\r\n                        ? this.current_default_config.path\r\n                        : \"; path=/\") +\r\n                (secure == undefined\r\n                    ? this.current_default_config.secure\r\n                        ? \"; Secure\"\r\n                        : \"\"\r\n                    : secure\r\n                        ? \"; Secure\"\r\n                        : \"\") +\r\n                (sameSite == undefined\r\n                    ? this.current_default_config.sameSite\r\n                        ? \"; SameSute=\" + this.current_default_config.sameSite\r\n                        : \"\"\r\n                    : sameSite\r\n                        ? \"; SameSite=\" + sameSite\r\n                        : \"\");\r\n        return this;\r\n    };\r\n    VueCookiesManager.prototype.remove = function (keyName, path, domain) {\r\n        if (!keyName || !this.isKey(keyName)) {\r\n            return false;\r\n        }\r\n        document.cookie =\r\n            encodeURIComponent(keyName) +\r\n                \"=; expires=Thu, 01 Jan 1970 00:00:00 GMT\" +\r\n                (domain\r\n                    ? \"; domain=\" + domain\r\n                    : this.current_default_config.domain\r\n                        ? this.current_default_config.domain\r\n                        : \"\") +\r\n                (path\r\n                    ? \"; path=\" + path\r\n                    : this.current_default_config.path\r\n                        ? this.current_default_config.path\r\n                        : \"; path=/\") +\r\n                \"; SameSite=Lax\";\r\n        return true;\r\n    };\r\n    VueCookiesManager.prototype.isKey = function (keyName) {\r\n        return new RegExp(\"(?:^|;\\\\s*)\" +\r\n            encodeURIComponent(keyName).replace(/[\\-\\.\\+\\*]/g, \"\\\\$&\") +\r\n            \"\\\\s*\\\\=\").test(document.cookie);\r\n    };\r\n    VueCookiesManager.prototype.keys = function () {\r\n        if (!document.cookie)\r\n            return [];\r\n        var _keys = document.cookie\r\n            .replace(/((?:^|\\s*;)[^\\=]+)(?=;|$)|^\\s*|\\s*(?:\\=[^;]*)?(?:\\1|$)/g, \"\")\r\n            .split(/\\s*(?:\\=[^;]*)?;\\s*/);\r\n        for (var _index = 0; _index < _keys.length; _index++) {\r\n            _keys[_index] = decodeURIComponent(_keys[_index]);\r\n        }\r\n        return _keys;\r\n    };\r\n    return VueCookiesManager;\r\n}());\r\nexport default {\r\n    install: function (app, options) {\r\n        app.config.globalProperties.$cookies = new VueCookiesManager();\r\n        if (options) {\r\n            app.config.globalProperties.$cookies.config(options);\r\n        }\r\n    },\r\n};\r\nvar GLOBAL_COOKIES_MANAGER = null;\r\nfunction globalCookiesConfig(options) {\r\n    if (GLOBAL_COOKIES_MANAGER == null) {\r\n        GLOBAL_COOKIES_MANAGER = new VueCookiesManager();\r\n    }\r\n    GLOBAL_COOKIES_MANAGER.config(options);\r\n}\r\nfunction useCookies() {\r\n    if (GLOBAL_COOKIES_MANAGER == null) {\r\n        GLOBAL_COOKIES_MANAGER = new VueCookiesManager();\r\n    }\r\n    var cookies = reactive(GLOBAL_COOKIES_MANAGER);\r\n    return { cookies: cookies };\r\n}\r\nexport { globalCookiesConfig, useCookies };\r\n"], "mappings": ";;;;;;;AASA,IAAI,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AACd;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASA,qBAAoB;AACzB,WAAK,yBAAyB;AAAA,IAClC;AACA,IAAAA,mBAAkB,UAAU,SAAS,SAAU,QAAQ;AACnD,eAAS,gBAAgB,KAAK,wBAAwB;AAClD,aAAK,uBAAuB,YAAY,IAAI,OAAO,YAAY,IACzD,OAAO,YAAY,IACnB,cAAc,YAAY;AAAA,MACpC;AAAA,IACJ;AACA,IAAAA,mBAAkB,UAAU,MAAM,SAAU,SAAS;AACjD,UAAI,QAAQ,mBAAmB,SAAS,OAAO,QAAQ,IAAI,OAAO,qBAC9D,mBAAmB,OAAO,EAAE,QAAQ,eAAe,MAAM,IACzD,6BAA6B,GAAG,IAAI,CAAC,KAAK;AAC9C,UAAI,SACA,MAAM,UAAU,GAAG,CAAC,MAAM,OAC1B,MAAM,UAAU,MAAM,SAAS,GAAG,MAAM,MAAM,MAAM,KAAK;AACzD,YAAI;AACA,kBAAQ,KAAK,MAAM,KAAK;AAAA,QAC5B,SACO,GAAG;AACN,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,MAAM,SAAU,SAAS,OAAO,aAAa,MAAM,QAAQ,QAAQ,UAAU;AACrG,UAAI,CAAC,SAAS;AACV,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACrE,WACS,qDAAqD,KAAK,OAAO,GAAG;AACzE,cAAM,IAAI,MAAM,2HACZ,OAAO;AAAA,MACf;AAEA,UAAI,SAAS,MAAM,gBAAgB,QAAQ;AACvC,gBAAQ,KAAK,UAAU,KAAK;AAAA,MAChC;AACA,UAAI,WAAW;AACf,UAAI,eAAe,QAAW;AAC1B,sBAAc,KAAK,uBAAuB,cACpC,KAAK,uBAAuB,cAC5B;AAAA,MACV;AACA,UAAI,eAAe,eAAe,GAAG;AACjC,gBAAQ,YAAY,aAAa;AAAA,UAC7B,KAAK;AACD,gBAAI,gBAAgB,YAAY,gBAAgB;AAC5C,yBAAW;AAAA;AAEX,yBAAW,eAAe;AAC9B;AAAA,UACJ,KAAK;AACD,gBAAI,4BAA4B,KAAK,WAAW,GAAG;AAE/C,kBAAI,cAAc,YAAY,QAAQ,6BAA6B,IAAI;AAEvE,sBAAQ,YACH,QAAQ,6BAA6B,IAAI,EACzC,YAAY,GAAG;AAAA,gBAEhB,KAAK;AACD,6BAAW,eAAe,CAAC,cAAc;AACzC;AAAA,gBACJ,KAAK;AACD,6BAAW,eAAe,CAAC,cAAc;AACzC;AAAA,gBACJ,KAAK;AACD,6BAAW,eAAe,CAAC,cAAc;AACzC;AAAA,gBACJ,KAAK;AACD,6BAAW,eAAe,CAAC,cAAc;AACzC;AAAA,gBACJ,KAAK;AACD,6BAAW,eAAe;AAC1B;AAAA,gBACJ,KAAK;AACD,6BAAW,eAAe,CAAC,cAAc;AACzC;AAAA,gBACJ;AACI,sBAAI,MAAM,sCAAsC;AAAA,cACxD;AAAA,YACJ,OACK;AACD,yBAAW,eAAe;AAAA,YAC9B;AACA;AAAA,UACJ,KAAK;AACD,uBAAW,eAAe,YAAY,YAAY;AAClD;AAAA,QACR;AAAA,MACJ;AACA,eAAS,SACL,mBAAmB,OAAO,IACtB,MACA,mBAAmB,KAAK,IACxB,YACC,SACK,cAAc,SACd,KAAK,uBAAuB,SACxB,KAAK,uBAAuB,SAC5B,OACT,OACK,YAAY,OACZ,KAAK,uBAAuB,OACxB,KAAK,uBAAuB,OAC5B,eACT,UAAU,SACL,KAAK,uBAAuB,SACxB,aACA,KACJ,SACI,aACA,OACT,YAAY,SACP,KAAK,uBAAuB,WACxB,gBAAgB,KAAK,uBAAuB,WAC5C,KACJ,WACI,gBAAgB,WAChB;AAClB,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,SAAS,SAAU,SAAS,MAAM,QAAQ;AAClE,UAAI,CAAC,WAAW,CAAC,KAAK,MAAM,OAAO,GAAG;AAClC,eAAO;AAAA,MACX;AACA,eAAS,SACL,mBAAmB,OAAO,IACtB,8CACC,SACK,cAAc,SACd,KAAK,uBAAuB,SACxB,KAAK,uBAAuB,SAC5B,OACT,OACK,YAAY,OACZ,KAAK,uBAAuB,OACxB,KAAK,uBAAuB,OAC5B,cACV;AACR,aAAO;AAAA,IACX;AACA,IAAAA,mBAAkB,UAAU,QAAQ,SAAU,SAAS;AACnD,aAAO,IAAI,OAAO,gBACd,mBAAmB,OAAO,EAAE,QAAQ,eAAe,MAAM,IACzD,SAAS,EAAE,KAAK,SAAS,MAAM;AAAA,IACvC;AACA,IAAAA,mBAAkB,UAAU,OAAO,WAAY;AAC3C,UAAI,CAAC,SAAS;AACV,eAAO,CAAC;AACZ,UAAI,QAAQ,SAAS,OAChB,QAAQ,2DAA2D,EAAE,EACrE,MAAM,qBAAqB;AAChC,eAAS,SAAS,GAAG,SAAS,MAAM,QAAQ,UAAU;AAClD,cAAM,MAAM,IAAI,mBAAmB,MAAM,MAAM,CAAC;AAAA,MACpD;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AACF,IAAO,eAAQ;AAAA,EACX,SAAS,SAAU,KAAK,SAAS;AAC7B,QAAI,OAAO,iBAAiB,WAAW,IAAI,kBAAkB;AAC7D,QAAI,SAAS;AACT,UAAI,OAAO,iBAAiB,SAAS,OAAO,OAAO;AAAA,IACvD;AAAA,EACJ;AACJ;AACA,IAAI,yBAAyB;AAC7B,SAAS,oBAAoB,SAAS;AAClC,MAAI,0BAA0B,MAAM;AAChC,6BAAyB,IAAI,kBAAkB;AAAA,EACnD;AACA,yBAAuB,OAAO,OAAO;AACzC;AACA,SAAS,aAAa;AAClB,MAAI,0BAA0B,MAAM;AAChC,6BAAyB,IAAI,kBAAkB;AAAA,EACnD;AACA,MAAI,UAAU,SAAS,sBAAsB;AAC7C,SAAO,EAAE,QAAiB;AAC9B;", "names": ["VueCookiesManager"]}