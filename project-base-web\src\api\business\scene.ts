/*
 * @Description:
 * @Version: 1.0
 * @Autor: hasaiki
 * @Date: 2023-04-10 10:05:37
 * @LastEditors: hasaiki
 * @LastEditTime: 2024-12-25 15:17:31
 */
import qs from 'qs';
import request from '@/utils/request';

const api = {
  // 启用场景列表
  sceneList: '/scenex/scene/list',
  // 获取单个场景详细信息
  sceneMsg: '/scenex/scene/plus-tree',
  // 同步场景资源
  syncByScene: '/modelx/model-lib/sync-by-scene',
  // 下载场景资源
  downloadSceneResource: '/scenex/scene/download',
  // 删除场景
  deleteScene: '/scenex/scene/delete',
  // 获取场景房间
  getSceneRoom: '/scenex/scene/room',
  // 获取场景内置物体
  getSceneThing: '/scenex/scene/showPlacements',
  // 获取系统字典信息
  dropDown: '/systemx/sys-dict-type/drop-down',
  // 查询场景名称是否存在
  checkSceneName: '/scenex/scene/check-name',
  // 主场景版本
  mainVersion: '/scenex/scene/main-version',
  // 子场景版本
  buildingVersion: '/scenex/scene/child-version',
  // 切换版本
  switchVersion: '/scenex/scene/switch',
  // 上传子场景
  uploadChildScene: '/scenex/scene/child-upload',
  // 可迁移主场景列表
  availableMoveScene: '/scenex/scene/available-moving-scene',
  // 迁移场景
  movingScene: '/scenex/scene/moving-scene',
  // 获取孪生体分类树
  twinClassTree: '/twinx/twin-class/class-tree',
  // 获取自定义表单数据
  twinsClassPage: '/twinx/twin-class/page',
  // 查询园区内孪生体
  queryTwinDataBySceneId: '/twinx/twin-body-data/get-twin-body-data-by-scene-id',
  // 保存孪生体摆点点位
  saveTwinBodyPointInfo: '/twinx/twin-body-data/save-twin-body-point-info',
  // 删除点位信息
  deleteTwinBodyPointInfo: '/twinx/twin-body-data/delete-twin-body-point-info',
  // 根据id获取摆点默认属性
  getTwinSceneInfoById: '/twinx/twin-body-data/get-twin-scene-info-by-id',
  // 根据编码获取场景默认信息
  getTwinSceneInfoByCode: '/twinx/twin-body-data/get-twin-scene-info-by-code',
  // 保存视角
  saveCameraConfig: '/scenex/scene/save-cam-config',
  // 保存默认孪生对象数据
  saveDefaultTwin: '/twinx/twin-body-data/save-default-twin',
  // 孪生体点位信息导出
  downloadTwinDataBySceneId: '/twinx/twin-body-data/download-twin-data-by-scene-id',
  // 获取资产数据
  getAssetList: '/twinx/twin-body-data/page',
  // 获取主场景信息
  getSceneRecord: '/scenex/scene/get-scene-record',
  // CAD和GIS点位下载模板
  downTemplate: '/scenex/scene-convert/download/template',
  // 附件cad
  uploadCad: '/scenex/scene-convert/cad',
  // 附件cad
  uploadGis: '/scenex/scene-convert/gis',
  // CAD点位保存映射对象
  saveCadTwinData: '/twinx/twin-body-data/save-cad-twin-data',
  // gis点位保存
  saveGisTwinData: '/twinx/twin-body-data/save-gis-twin-data',
  // 点位校准保存
  setCorrectTwinCbInfo: '/twinx/twin-body-data/correct-twin-cb-info',
  // 保存地图位置信息
  saveMapInfo: '/scenex/scene/save-map-info',
  // 模模搭点位转换添加属性
  setPlacements: '/scenex/scene/placements',
  // 模模搭点位保存映射对象
  saveMappingTwinData: '/twinx/twin-body-data/save-mapping-twin-data',
  // 删除历史版本场景
  deleteSceneVersion: '/scenex/scene/delete-version',
};
/**
 *保存地图位置信息
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function saveMapInfo(parameter: any) {
  return request({
    url: api.saveMapInfo,
    method: 'post',
    data: parameter,
  });
}
// 获取主场景信息
export function getSceneRecord(params: any) {
  return request({
    url: api.getSceneRecord,
    method: 'get',
    params,
  });
}
/**
 *保存默认孪生体数据
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function saveDefaultTwin(parameter: any) {
  return request({
    url: api.saveDefaultTwin,
    method: 'post',
    data: parameter,
  });
}
/**
 *保存视角
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function saveCameraConfig(parameter: any) {
  return request({
    url: api.saveCameraConfig,
    method: 'post',
    data: parameter,
  });
}
// 默认孪生体获取默认属性
export function getTwinSceneInfoByCode(params: any) {
  return request({
    url: api.getTwinSceneInfoByCode,
    method: 'get',
    params,
  });
}
// 摆点孪生体获取默认属性
export function getTwinSceneInfoById(params: any) {
  return request({
    url: api.getTwinSceneInfoById,
    method: 'get',
    params,
  });
}
/**
 *孪生体摆点点位信息删除
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function deleteTwinBodyPointInfo(parameter: any) {
  return request({
    url: api.deleteTwinBodyPointInfo,
    method: 'post',
    data: parameter,
  });
}
/**
 *孪生体摆点点位信息保存
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function saveTwinBodyPointInfo(parameter: any) {
  return request({
    url: api.saveTwinBodyPointInfo,
    method: 'post',
    data: parameter,
  });
}
/**
 * @description: 初始化设备获取该场景所有点位信息
 */

export function queryTwinDataBySceneId(params: any) {
  return request({
    url: api.queryTwinDataBySceneId,
    method: 'get',
    params,
    paramsSerializer: {
      serialize(params) {
        return qs.stringify(params, { arrayFormat: 'repeat' });
      },
    },
  });
}
// 获取自定义表单数据
export function getTwinsClassPage(parameter: any) {
  return request({
    url: api.twinsClassPage,
    method: 'get',
    params: parameter,
  });
}
/**
 *获取孪生体分类树
 *
 * @export
 * @param {*} [parameter]
 * @return {*}
 */
export function getTwinClassTree(parameter?: any) {
  return request({
    url: api.twinClassTree,
    method: 'get',
    params: parameter,
  });
}
// 上传主场景接口
export const uploadMainSceneUrl = '/scenex/scene/main-upload';
// 获取没有子场景的主场景列表
export function getSceneListOfNoChildren(parameter?: any) {
  return request({
    url: api.availableMoveScene,
    method: 'get',
    params: parameter,
  });
}
/**
 *迁移主场景
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function movingScene(parameter: any) {
  return request({
    url: api.movingScene,
    method: 'post',
    data: parameter,
  });
}
// 上传子场景
export function uploadChildScene(parameter: any, back: Function) {
  return request({
    url: api.uploadChildScene,
    method: 'post',
    data: parameter,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress(progressEvent) {
      back(progressEvent);
    },
  });
}
/**
 *切换版本
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function switchSceneVersion(parameter: any) {
  return request({
    url: api.switchVersion,
    method: 'post',
    data: parameter,
  });
}
/**
 *获取主场景版本列表
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function getMainVersion(parameter: any) {
  return request({
    url: api.mainVersion,
    method: 'get',
    params: parameter,
  });
}
/**
 *获取子场景版本列表
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function getBuildingVersion(parameter: any) {
  return request({
    url: api.buildingVersion,
    method: 'get',
    params: parameter,
  });
}
// 查询场景名称
export function checkSceneName(parameter: any) {
  return request({
    url: api.checkSceneName,
    method: 'get',
    params: parameter,
  });
}
/**
 *获取系统字典配置
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function sysDictTypeDropDown(parameter: any) {
  return request({
    url: api.dropDown,
    method: 'get',
    params: parameter,
  });
}
/**
 *获取场景房间
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function getScenesRoom(parameter: any) {
  return request({
    url: api.getSceneRoom,
    method: 'get',
    params: parameter,
  });
}
/**
 *获取场景内置物体
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function getScenesThing(parameter: any) {
  return request({
    url: api.getSceneThing,
    method: 'get',
    params: parameter,
  });
}
/**
 * 获取启用场景列表
 * @param parameter
 * @returns
 */
export function getSceneList(parameter = {}) {
  return request({
    url: api.sceneList,
    method: 'get',
    params: parameter,
  });
}

/**
 * 获取单个场景信息
 * @param parameter flag:是否不显示房间（true：不显示，false：显示） twinFlag: 是否显示孪生体（true：显示，false：不显示）
 * @returns
 */
export function getSceneMsg(params: any) {
  return request({
    url: api.sceneMsg,
    method: 'get',
    params,
  });
}
/**
 * 同步场景资源
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function syncByScene(parameter: any) {
  return request({
    url: api.syncByScene,
    method: 'get',
    params: parameter,
  });
}
/**
 *下载场景资源
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function downloadSceneResource(parameter: any) {
  return request({
    url: api.downloadSceneResource,
    method: 'get',
    params: parameter,
  });
}
/**
 * 删除场景
 *
 * @export
 * @param {*} parameter
 * @return {*}
 */
export function deleteScene(parameter: any) {
  return request({
    url: api.deleteScene,
    method: 'post',
    data: parameter,
  });
}
/**
 * 孪生体点位信息导出
 * @param data
 * @returns
 */
export function downloadTwinDataBySceneId(data: any) {
  return request({
    url: api.downloadTwinDataBySceneId,
    method: 'post',
    responseType: 'blob',
    data,
  });
}
/**
 * 获取资产数据
 * @param params
 * @returns
 */
export function getAssetList(params: any) {
  return request({
    url: api.getAssetList,
    method: 'get',
    params,
  });
}
/**
 * CAD和GIS点位下载模板
 * @param params
 * @returns
 */
export function downTemplate(params: any) {
  return request({
    url: api.downTemplate,
    method: 'get',
    responseType: 'blob',
    params,
  });
}
/**
 * 上传gis文件
 * @param data
 * @returns
 */
export function uploadGis(data: any, back: Function) {
  return request({
    url: api.uploadGis,
    method: 'post',
    data,
    onUploadProgress(progressEvent) {
      back(progressEvent);
    },
  });
}
/**
 * 附件cad
 * @param data
 * @returns
 */
export function uploadCad(data: any, back: Function) {
  return request({
    url: api.uploadCad,
    method: 'post',
    data,
    onUploadProgress(progressEvent) {
      back(progressEvent);
    },
  });
}
/**
 * GIS点位保存映射对象
 * @param data
 * @returns
 */
export function saveGisTwinData(data: any) {
  return request({
    url: api.saveGisTwinData,
    method: 'post',
    data,
  });
}
/**
 * CAD点位保存映射对象
 * @param data
 * @returns
 */
export function saveCadTwinData(data: any) {
  return request({
    url: api.saveCadTwinData,
    method: 'post',
    data,
  });
}
/**
 * 点位校准保存
 * @param data
 * @returns
 */
export function setCorrectTwinCbInfo(data: any) {
  return request({
    url: api.setCorrectTwinCbInfo,
    method: 'post',
    data,
  });
}
/**
 * 模模搭点位转换添加属性
 * @param data
 * @returns
 */
export function setPlacements(data: any) {
  return request({
    url: api.setPlacements,
    method: 'post',
    data,
  });
}
/**
 * 模模搭点位保存映射对象
 * @param data
 * @returns
 */
export function saveMappingTwinData(data: any) {
  return request({
    url: api.saveMappingTwinData,
    method: 'post',
    data,
  });
}

/**
 * 删除历史版本
 * @param data
 * @returns
 */
export function deleteSceneVersion(data: any) {
  return request({
    url: api.deleteSceneVersion,
    method: 'post',
    data,
  });
}
