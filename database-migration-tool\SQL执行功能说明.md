# SQL执行功能说明

## 功能概述

在SQL转换面板后新增了SQL执行面板，提供了强大的多Schema SQL执行功能。该面板与系统配置面板功能一致，但去掉了固定的数据映射配置，直接提供SQL输入框文本域，可直接执行输入的SQL。

## 主要功能

### 1. 数据库配置选择
- 支持选择已配置的数据库连接
- 可刷新配置列表

### 2. Schema配置
- **Schema输入框**: 输入要匹配的Schema名称或模式
- **前缀匹配**: 选中后匹配以输入内容开头的所有Schema
- **后缀匹配**: 选中后匹配以输入内容结尾的所有Schema
- **精确匹配**: 不选择任何匹配选项时，进行精确匹配

### 3. 排除Schema配置
- 支持添加多个需要排除的Schema
- 可动态添加和删除排除项
- 在Schema匹配后会自动排除指定的Schema

### 4. SQL输入和执行
- **SQL输入框**: 支持多行SQL语句输入
- **语法高亮**: 使用等宽字体，支持Tab缩进
- **智能执行**: 自动判断SQL类型（查询/更新）
- **多Schema执行**: 在所有匹配的Schema下遍历执行SQL

### 5. 结果显示
- **执行结果表格**: 显示每个Schema的执行状态、影响行数和备注信息（不显示查询的具体数据）
- **进度条**: 实时显示执行进度
- **详细日志**: 记录每个Schema的执行情况
- **统计信息**: 显示总处理Schema数量、成功数量、失败数量和总影响行数

## 使用场景

### 场景1: 批量查询多个租户数据
```sql
-- 输入Schema: tenant_
-- 选择前缀匹配
-- SQL:
SELECT COUNT(*) FROM sys_config WHERE status = 1;
```
这将在所有以"tenant_"开头的Schema中执行查询。

### 场景2: 批量更新配置
```sql
-- 输入Schema: _prod
-- 选择后缀匹配
-- 排除Schema: test_prod, backup_prod
-- SQL:
UPDATE sys_config SET value = 'new_value' WHERE code = 'system.timeout';
```
这将在所有以"_prod"结尾的Schema中执行更新，但排除测试和备份Schema。

### 场景3: 精确Schema操作
```sql
-- 输入Schema: main_database
-- 不选择匹配选项
-- SQL:
SELECT * FROM user_info WHERE create_time > '2024-01-01';
```
这将只在"main_database"Schema中执行查询。

## 技术特性

### 1. 智能SQL处理
- **多数据库Schema切换支持**: 自动尝试不同数据库的Schema切换命令
  - MySQL: `USE schema_name`
  - PostgreSQL/DB2: `SET SCHEMA schema_name`
  - Oracle/达梦: `ALTER SESSION SET CURRENT_SCHEMA = schema_name`
- **智能Schema前缀**: 当无法切换Schema时，自动为SQL语句添加Schema前缀
- 支持SELECT、UPDATE、INSERT、DELETE等各种SQL类型
- 自动处理FROM、UPDATE、INTO、JOIN等关键字的Schema前缀

### 2. 安全性
- 事务支持，确保数据一致性
- 错误处理，单个Schema失败不影响其他Schema
- 详细的错误日志记录

### 3. 性能优化
- 顺序执行多个Schema操作（确保数据一致性）
- 进度条实时反馈
- 结果优化（查询操作只返回行数统计，不显示具体数据）
- 智能错误处理（单个Schema失败不影响其他Schema继续执行）

### 4. 配置持久化
- 自动保存Schema配置
- 保存排除列表设置
- 保存SQL内容

## 操作步骤

1. **选择数据库配置**: 从下拉列表中选择已配置的数据库连接
2. **配置Schema匹配**: 
   - 输入Schema名称或模式
   - 选择匹配方式（前缀/后缀/精确）
3. **设置排除列表**: 添加需要排除的Schema名称
4. **输入SQL语句**: 在文本框中输入要执行的SQL
5. **执行SQL**: 点击"执行SQL"按钮开始执行
6. **查看结果**: 在结果表格和日志区域查看执行结果

## 注意事项

1. **权限要求**: 确保数据库用户有足够权限访问目标Schema
2. **SQL语法**: SQL语句应该是标准的数据库语法
3. **Schema命名**: Schema名称区分大小写
4. **批量操作**: 大批量操作时请谨慎，建议先在测试环境验证
5. **事务处理**: 每个Schema的操作都在独立事务中执行

## 错误处理

- 连接失败时会显示详细错误信息
- 单个Schema执行失败不会影响其他Schema
- 所有错误都会记录在日志区域
- 支持弹窗提示执行结果

## 配置保存

所有配置（包括数据库选择、Schema配置、排除列表、SQL内容）都会自动保存到配置文件中，下次打开时会自动恢复。
