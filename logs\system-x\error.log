[1;35m12:54:25.502[0;39m [32m[main][0;39m [1;31mERROR[0;39m [36mo.s.b.SpringApplication[0;39m - [36m[reportFailure,857][0;39m - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'initConfigRunner' defined in file [D:\x\pedestal-x\pedestal-x-app\system-x\target\classes\com\uino\x\pedestal\app\systemx\config\InitConfigRunner.class]: Unsatisfied dependency expressed through constructor parameter 0: Error creating bean with name 'tenantInfoServiceImpl' defined in file [D:\x\pedestal-x\pedestal-x-module\tenant\tenant-impl\target\classes\com\uino\x\pedestal\tenant\impl\TenantInfoServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'realTimeMessageServiceImpl' defined in file [D:\x\pedestal-x\pedestal-x-module\message\message-impl\target\classes\com\uino\x\pedestal\message\impl\RealTimeMessageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysUserMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\identity\identity-dao\target\classes\com\uino\x\pedestal\identity\dao\mapper\SysUserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.instantiateSingleton(DefaultListableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingleton(DefaultListableBeanFactory.java:1188)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:1123)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:987)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:627)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:752)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:439)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:318)
	at com.uino.x.pedestal.app.systemx.SystemXApplication.main(SystemXApplication.java:34)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantInfoServiceImpl' defined in file [D:\x\pedestal-x\pedestal-x-module\tenant\tenant-impl\target\classes\com\uino\x\pedestal\tenant\impl\TenantInfoServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'realTimeMessageServiceImpl' defined in file [D:\x\pedestal-x\pedestal-x-module\message\message-impl\target\classes\com\uino\x\pedestal\message\impl\RealTimeMessageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysUserMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\identity\identity-dao\target\classes\com\uino\x\pedestal\identity\dao\mapper\SysUserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'realTimeMessageServiceImpl' defined in file [D:\x\pedestal-x\pedestal-x-module\message\message-impl\target\classes\com\uino\x\pedestal\message\impl\RealTimeMessageServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 1: Error creating bean with name 'sysUserMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\identity\identity-dao\target\classes\com\uino\x\pedestal\identity\dao\mapper\SysUserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:240)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1395)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1232)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysUserMapper' defined in file [D:\x\pedestal-x\pedestal-x-module\identity\identity-dao\target\classes\com\uino\x\pedestal\identity\dao\mapper\SysUserMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory': Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1450)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:606)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1683)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:804)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:546)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:569)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1745)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1541)
	... 58 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/baomidou/dynamic/datasource/spring/boot/autoconfigure/DynamicDataSourceAutoConfiguration.class]: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1826)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:607)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:529)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:339)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:373)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1683)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1628)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:913)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 71 common frames omitted
Caused by: java.lang.RuntimeException: dynamic-datasource enabled ALIBABA SEATA,however without seata dependency
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.checkEnv(DynamicRoutingDataSource.java:261)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.afterPropertiesSet(DynamicRoutingDataSource.java:225)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.invokeInitMethods(AbstractAutowireCapableBeanFactory.java:1873)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1822)
	... 81 common frames omitted
Caused by: java.lang.ClassNotFoundException: io.seata.rm.datasource.DataSourceProxy
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641)
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188)
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:525)
	at java.base/java.lang.Class.forName0(Native Method)
	at java.base/java.lang.Class.forName(Class.java:375)
	at com.baomidou.dynamic.datasource.DynamicRoutingDataSource.checkEnv(DynamicRoutingDataSource.java:258)
	... 84 common frames omitted
[1;35m13:05:14.853[0;39m [32m[tenant-info-service-impl52][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-4010001, stmt-4020014} execute error. ALTER TABLE `legend_management`
    MODIFY COLUMN `file_id` bigint NULL COMMENT '文件Id' AFTER `code`,
    ADD COLUMN `files` json NULL COMMENT '文件列表' AFTER `file_id`
java.sql.SQLSyntaxErrorException: Duplicate column name 'files'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:837)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:685)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2993)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.execute(StatementProxyImpl.java:141)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:215)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.mysql.MySQLNamedLockTemplate.execute(MySQLNamedLockTemplate.java:62)
	at org.flywaydb.database.mysql.MySQLConnection.lock(MySQLConnection.java:159)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:164)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:247)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:210)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:188)
	at com.uino.x.pedestal.tenant.migrate.impl.InitTenantMigration.migrate(InitTenantMigration.java:36)
	at com.uino.x.pedestal.tenant.impl.TenantInfoServiceImpl.lambda$13(TenantInfoServiceImpl.java:633)
	at com.uino.x.common.core.util.AsyncUtils.lambda$6(AsyncUtils.java:434)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m13:05:14.893[0;39m [32m[tenant-info-service-impl52][0;39m [1;31mERROR[0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[error,49][0;39m - Migration of schema `tphbw_systemx` to version "2025.***********.05 - 增加图例列表" failed! Please restore backups and roll back database and code!
[1;35m13:05:14.908[0;39m [32m[tenant-info-service-impl53][0;39m [1;31mERROR[0;39m [36mdruid.sql.Statement[0;39m - [36m[statementLogError,148][0;39m - {conn-5010001, stmt-5020014} execute error. ALTER TABLE `legend_management`
    MODIFY COLUMN `file_id` bigint NULL COMMENT '文件Id' AFTER `code`,
    ADD COLUMN `files` json NULL COMMENT '文件列表' AFTER `file_id`
java.sql.SQLSyntaxErrorException: Duplicate column name 'files'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:837)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:685)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2993)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.execute(StatementProxyImpl.java:141)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:215)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.mysql.MySQLNamedLockTemplate.execute(MySQLNamedLockTemplate.java:62)
	at org.flywaydb.database.mysql.MySQLConnection.lock(MySQLConnection.java:159)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:164)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:247)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:210)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:188)
	at com.uino.x.pedestal.tenant.migrate.impl.InitTenantMigration.migrate(InitTenantMigration.java:36)
	at com.uino.x.pedestal.tenant.impl.TenantInfoServiceImpl.lambda$13(TenantInfoServiceImpl.java:633)
	at com.uino.x.common.core.util.AsyncUtils.lambda$6(AsyncUtils.java:434)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
[1;35m13:05:14.918[0;39m [32m[tenant-info-service-impl53][0;39m [1;31mERROR[0;39m [36mo.f.c.i.c.DbMigrate[0;39m - [36m[error,49][0;39m - Migration of schema `xmcs002_systemx` to version "2025.***********.05 - 增加图例列表" failed! Please restore backups and roll back database and code!
[1;35m13:05:15.786[0;39m [32m[tenant-info-service-impl52][0;39m [1;31mERROR[0;39m [36mc.u.x.p.t.m.i.InitTenantMigration[0;39m - [36m[migrate,40][0;39m - Script V2025.***********.05__增加图例列表.sql failed
----------------------------------------------
SQL State  : 42S21
Error Code : 1060
Message    : Duplicate column name 'files'
Location   : db/mysql/migration/legend/1.0.0/V2025.***********.05__增加图例列表.sql (D:\x\pedestal-x\pedestal-x-module\legend\legend-pojo\target\classes\db\mysql\migration\legend\1.0.0\V2025.***********.05__增加图例列表.sql)
Line       : 1
Statement  : Run Flyway with -X option to see the actual statement causing the problem

org.flywaydb.core.internal.exception.FlywayMigrateException: Script V2025.***********.05__增加图例列表.sql failed
----------------------------------------------
SQL State  : 42S21
Error Code : 1060
Message    : Duplicate column name 'files'
Location   : db/mysql/migration/legend/1.0.0/V2025.***********.05__增加图例列表.sql (D:\x\pedestal-x\pedestal-x-module\legend\legend-pojo\target\classes\db\mysql\migration\legend\1.0.0\V2025.***********.05__增加图例列表.sql)
Line       : 1
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.mysql.MySQLNamedLockTemplate.execute(MySQLNamedLockTemplate.java:62)
	at org.flywaydb.database.mysql.MySQLConnection.lock(MySQLConnection.java:159)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:164)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:247)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:210)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:188)
	at com.uino.x.pedestal.tenant.migrate.impl.InitTenantMigration.migrate(InitTenantMigration.java:36)
	at com.uino.x.pedestal.tenant.impl.TenantInfoServiceImpl.lambda$13(TenantInfoServiceImpl.java:633)
	at com.uino.x.common.core.util.AsyncUtils.lambda$6(AsyncUtils.java:434)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V2025.***********.05__增加图例列表.sql failed
----------------------------------------------
SQL State  : 42S21
Error Code : 1060
Message    : Duplicate column name 'files'
Location   : db/mysql/migration/legend/1.0.0/V2025.***********.05__增加图例列表.sql (D:\x\pedestal-x\pedestal-x-module\legend\legend-pojo\target\classes\db\mysql\migration\legend\1.0.0\V2025.***********.05__增加图例列表.sql)
Line       : 1
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 19 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'files'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:837)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:685)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2993)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.execute(StatementProxyImpl.java:141)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:215)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 25 common frames omitted
[1;35m13:05:15.833[0;39m [32m[tenant-info-service-impl53][0;39m [1;31mERROR[0;39m [36mc.u.x.p.t.m.i.InitTenantMigration[0;39m - [36m[migrate,40][0;39m - Script V2025.***********.05__增加图例列表.sql failed
----------------------------------------------
SQL State  : 42S21
Error Code : 1060
Message    : Duplicate column name 'files'
Location   : db/mysql/migration/legend/1.0.0/V2025.***********.05__增加图例列表.sql (D:\x\pedestal-x\pedestal-x-module\legend\legend-pojo\target\classes\db\mysql\migration\legend\1.0.0\V2025.***********.05__增加图例列表.sql)
Line       : 1
Statement  : Run Flyway with -X option to see the actual statement causing the problem

org.flywaydb.core.internal.exception.FlywayMigrateException: Script V2025.***********.05__增加图例列表.sql failed
----------------------------------------------
SQL State  : 42S21
Error Code : 1060
Message    : Duplicate column name 'files'
Location   : db/mysql/migration/legend/1.0.0/V2025.***********.05__增加图例列表.sql (D:\x\pedestal-x\pedestal-x-module\legend\legend-pojo\target\classes\db\mysql\migration\legend\1.0.0\V2025.***********.05__增加图例列表.sql)
Line       : 1
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:399)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$applyMigrations$1(DbMigrate.java:283)
	at org.flywaydb.core.internal.jdbc.TransactionalExecutionTemplate.execute(TransactionalExecutionTemplate.java:59)
	at org.flywaydb.core.internal.command.DbMigrate.applyMigrations(DbMigrate.java:282)
	at org.flywaydb.core.internal.command.DbMigrate.migrateGroup(DbMigrate.java:255)
	at org.flywaydb.core.internal.command.DbMigrate.lambda$migrateAll$0(DbMigrate.java:153)
	at org.flywaydb.database.mysql.MySQLNamedLockTemplate.execute(MySQLNamedLockTemplate.java:62)
	at org.flywaydb.database.mysql.MySQLConnection.lock(MySQLConnection.java:159)
	at org.flywaydb.core.internal.schemahistory.JdbcTableSchemaHistory.lock(JdbcTableSchemaHistory.java:164)
	at org.flywaydb.core.internal.command.DbMigrate.migrateAll(DbMigrate.java:153)
	at org.flywaydb.core.internal.command.DbMigrate.migrate(DbMigrate.java:104)
	at org.flywaydb.core.Flyway.lambda$migrate$1(Flyway.java:247)
	at org.flywaydb.core.FlywayExecutor.execute(FlywayExecutor.java:210)
	at org.flywaydb.core.Flyway.migrate(Flyway.java:188)
	at com.uino.x.pedestal.tenant.migrate.impl.InitTenantMigration.migrate(InitTenantMigration.java:36)
	at com.uino.x.pedestal.tenant.impl.TenantInfoServiceImpl.lambda$13(TenantInfoServiceImpl.java:633)
	at com.uino.x.common.core.util.AsyncUtils.lambda$6(AsyncUtils.java:434)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: org.flywaydb.core.internal.sqlscript.FlywaySqlScriptException: Script V2025.***********.05__增加图例列表.sql failed
----------------------------------------------
SQL State  : 42S21
Error Code : 1060
Message    : Duplicate column name 'files'
Location   : db/mysql/migration/legend/1.0.0/V2025.***********.05__增加图例列表.sql (D:\x\pedestal-x\pedestal-x-module\legend\legend-pojo\target\classes\db\mysql\migration\legend\1.0.0\V2025.***********.05__增加图例列表.sql)
Line       : 1
Statement  : Run Flyway with -X option to see the actual statement causing the problem

	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.handleException(DefaultSqlScriptExecutor.java:256)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:217)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.execute(DefaultSqlScriptExecutor.java:137)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.executeOnce(SqlMigrationExecutor.java:75)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.lambda$execute$0(SqlMigrationExecutor.java:66)
	at org.flywaydb.core.internal.database.DefaultExecutionStrategy.execute(DefaultExecutionStrategy.java:31)
	at org.flywaydb.core.internal.resolver.sql.SqlMigrationExecutor.execute(SqlMigrationExecutor.java:65)
	at org.flywaydb.core.internal.command.DbMigrate.doMigrateGroup(DbMigrate.java:391)
	... 19 common frames omitted
Caused by: java.sql.SQLSyntaxErrorException: Duplicate column name 'files'
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:112)
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:114)
	at com.mysql.cj.jdbc.StatementImpl.executeInternal(StatementImpl.java:837)
	at com.mysql.cj.jdbc.StatementImpl.execute(StatementImpl.java:685)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2993)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.filter.FilterAdapter.statement_execute(FilterAdapter.java:2483)
	at com.alibaba.druid.filter.FilterEventAdapter.statement_execute(FilterEventAdapter.java:182)
	at com.alibaba.druid.filter.FilterChainImpl.statement_execute(FilterChainImpl.java:2991)
	at com.alibaba.druid.proxy.jdbc.StatementProxyImpl.execute(StatementProxyImpl.java:141)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:635)
	at org.flywaydb.core.internal.jdbc.JdbcTemplate.executeStatement(JdbcTemplate.java:215)
	at org.flywaydb.core.internal.sqlscript.ParsedSqlStatement.execute(ParsedSqlStatement.java:88)
	at org.flywaydb.core.internal.sqlscript.DefaultSqlScriptExecutor.executeStatement(DefaultSqlScriptExecutor.java:212)
	... 25 common frames omitted
