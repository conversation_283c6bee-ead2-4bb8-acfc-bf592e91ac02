[1;35m14:49:20.574[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m14:49:20.753[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m14:49:20.770[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m14:49:20.852[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m14:49:20.861[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarting,53][0;39m - Starting GatewayXApplication using Java 17.0.14 with PID 33104 (D:\x\gateway-x\gateway-x-server\target\classes started by Administrator in D:\x)
[1;35m14:49:20.865[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal"
[1;35m14:49:20.974[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m14:49:20.975[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m14:49:20.975[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=gateway-x.yml, group=DEFAULT_GROUP] success
[1;35m14:49:22.418[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'sentinelGatewayFilter' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=gatewayConfig; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/gatewayx/config/GatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.sentinel.gateway.scg.SentinelSCGAutoConfiguration; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/sentinel/gateway/scg/SentinelSCGAutoConfiguration.class]]
[1;35m14:49:22.714[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m14:49:22.721[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m14:49:22.812[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 67 ms. Found 0 Redis repository interfaces.
[1;35m14:49:23.114[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=a682fabe-27a3-3ecc-8e94-3197e1d58eef
[1;35m14:49:24.048[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m14:49:24.460[0;39m [32m[redisson-netty-3-5][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m14:49:24.487[0;39m [32m[redisson-netty-3-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m14:49:24.729[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m14:49:24.782[0;39m [32m[redisson-netty-6-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m14:49:24.805[0;39m [32m[redisson-netty-6-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m14:49:25.157[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m14:49:26.638[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m14:49:26.638[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m14:49:26.639[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m14:49:26.639[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m14:49:26.640[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m14:49:26.640[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m14:49:26.640[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m14:49:26.640[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m14:49:26.640[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m14:49:26.641[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m14:49:26.641[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m14:49:26.641[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m14:49:26.641[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m14:49:26.641[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m14:49:27.125[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m14:49:27.460[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m14:49:27.624[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m14:49:27.667[0;39m [32m[redisson-netty-11-5][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m14:49:27.695[0;39m [32m[redisson-netty-11-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m14:49:28.342[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.NacosConfigService[0;39m - [36m[<init>,78][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m14:49:28.344[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,48][0;39m - DefaultLabelsCollectorManager get labels.....
[1;35m14:49:28.345[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,62][0;39m - Process LabelsCollector with [name:defaultNacosLabelsCollector]
[1;35m14:49:28.345[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,64][0;39m - default nacos collect properties raw labels: null
[1;35m14:49:28.345[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,71][0;39m - default nacos collect properties labels: {}
[1;35m14:49:28.346[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,74][0;39m - default nacos collect jvm raw labels: null
[1;35m14:49:28.346[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,80][0;39m - default nacos collect jvm labels: {}
[1;35m14:49:28.346[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,83][0;39m - default nacos collect env raw labels: null
[1;35m14:49:28.346[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,91][0;39m - default nacos collect env labels: {}
[1;35m14:49:28.347[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,50][0;39m - DefaultLabelsCollectorManager get labels finished,labels :{}
[1;35m14:49:28.347[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m14:49:28.347[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m14:49:28.487[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m14:49:28.488[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m14:49:28.496[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] gateway-x-sentinel.json+DEFAULT_GROUP+pedestal
[1;35m14:49:28.511[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=gateway-x-sentinel.json, group=DEFAULT_GROUP, cnt=1
[1;35m14:49:28.512[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0
[1;35m14:49:28.512[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$352/0x000001ff832ee148
[1;35m14:49:28.512[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$353/0x000001ff832ee578
[1;35m14:49:28.513[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
[1;35m14:49:28.513[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
[1;35m14:49:28.513[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m14:49:28.514[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m14:49:28.530[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Success to connect to server [***********:8848] on start up, connectionId = 1756104568264_**********_62038
[1;35m14:49:28.530[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m14:49:28.530[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Notify connected event to listeners.
[1;35m14:49:28.530[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$371/0x000001ff8342f618
[1;35m14:49:28.530[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [30ce74ca-53e0-48a0-b4f0-6ad83b5133ed_config-0] Connected,notify listen context...
[1;35m14:49:28.661[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 10001 (http)
[1;35m14:49:29.009[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m14:49:29.011[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m14:49:29.012[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m14:49:29.012[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m14:49:29.025[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m14:49:29.031[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m14:49:29.032[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m14:49:29.155[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 9e1f4e43-61f7-4854-bdf3-afbec394a765
[1;35m14:49:29.161[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->9e1f4e43-61f7-4854-bdf3-afbec394a765
[1;35m14:49:29.161[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m14:49:29.162[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m14:49:29.162[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m14:49:29.163[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m14:49:29.163[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m14:49:29.180[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Success to connect to server [***********:8848] on start up, connectionId = 1756104568915_**********_62042
[1;35m14:49:29.181[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m14:49:29.181[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Notify connected event to listeners.
[1;35m14:49:29.181[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$371/0x000001ff8342f618
[1;35m14:49:29.181[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m14:49:29.182[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service gateway-x with instance Instance{instanceId='null', ip='**********', port=10001, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m14:49:29.202[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP gateway-x **********:10001 register finished
[1;35m14:49:29.369[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.369[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-city, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.369[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.369[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.370[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.370[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.370[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-city, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.370[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.386[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:edtap-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.386[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-campus, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.387[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:edtap-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.387[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:seata-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.387[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-campus, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.387[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:ti-dix, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.388[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:ti-dix, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.388[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:seata-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.390[0;39m [32m[boundedElastic-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.392[0;39m [32m[boundedElastic-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.394[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.392[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-decoration, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.394[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-admin, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.395[0;39m [32m[boundedElastic-17][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-decoration, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.395[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-campus, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.395[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:distribution, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.395[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:edtap-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.397[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.397[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-campus, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.397[0;39m [32m[boundedElastic-17][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-decoration, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.397[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.398[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:edtap-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.398[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-decoration, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.398[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-admin, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.399[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:distribution, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.399[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.401[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:seata-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.401[0;39m [32m[boundedElastic-21][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.402[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:distribution, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.402[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.402[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-admin, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.403[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:seata-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.403[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-city, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.404[0;39m [32m[boundedElastic-21][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.404[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.404[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-admin, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.405[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:distribution, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.405[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-city, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.406[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:ti-dix, group:DEFAULT_GROUP, clusters: 
[1;35m14:49:29.406[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:ti-dix, group:DEFAULT_GROUP, cluster: 
[1;35m14:49:29.414[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(0) service: DEFAULT_GROUP@@system-x -> []
[1;35m14:49:29.415[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.415[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@edtap-server -> [{"instanceId":"***********#10011##DEFAULT_GROUP@@edtap-server","ip":"***********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.415[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-campus -> [{"instanceId":"**********#8990#DEFAULT#DEFAULT_GROUP@@builder-campus","ip":"**********","port":8990,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-campus","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.416[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@edtap-server -> [{"instanceId":"***********#10011##DEFAULT_GROUP@@edtap-server","ip":"***********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.416[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@ti-dix -> [{"instanceId":"***********#10009#DEFAULT#DEFAULT_GROUP@@ti-dix","ip":"***********","port":10009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ti-dix","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.416[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-campus -> [{"instanceId":"**********#8990#DEFAULT#DEFAULT_GROUP@@builder-campus","ip":"**********","port":8990,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-campus","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.417[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.417[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.417[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-city -> [{"instanceId":"**********#8989#DEFAULT#DEFAULT_GROUP@@builder-city","ip":"**********","port":8989,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-city","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.417[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@seata-server -> [{"instanceId":"***********#8091#default#DEFAULT_GROUP@@seata-server","ip":"***********","port":8091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"default","serviceName":"DEFAULT_GROUP@@seata-server","metadata":{},"ipDeleteTimeout":30000,"instanceIdGenerator":"simple","instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.424[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.425[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-city -> [{"instanceId":"**********#8989#DEFAULT#DEFAULT_GROUP@@builder-city","ip":"**********","port":8989,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-city","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.425[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@ti-dix -> [{"instanceId":"***********#10009#DEFAULT#DEFAULT_GROUP@@ti-dix","ip":"***********","port":10009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ti-dix","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.428[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@seata-server -> [{"instanceId":"***********#8091#default#DEFAULT_GROUP@@seata-server","ip":"***********","port":8091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"default","serviceName":"DEFAULT_GROUP@@seata-server","metadata":{},"ipDeleteTimeout":30000,"instanceIdGenerator":"simple","instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.450[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-decoration -> [{"instanceId":"**********#8991#DEFAULT#DEFAULT_GROUP@@builder-decoration","ip":"**********","port":8991,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-decoration","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.450[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.450[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-decoration -> [{"instanceId":"**********#8991#DEFAULT#DEFAULT_GROUP@@builder-decoration","ip":"**********","port":8991,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-decoration","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.452[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@distribution -> [{"instanceId":"***********#81#DEFAULT#DEFAULT_GROUP@@distribution","ip":"***********","port":81,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@distribution","metadata":{"preserved.heart.beat.timeout":"15000","spring.application.name":"distribution","preserved.ip.delete.timeout":"30000","env":"root","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.452[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@distribution -> [{"instanceId":"***********#81#DEFAULT#DEFAULT_GROUP@@distribution","ip":"***********","port":81,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@distribution","metadata":{"preserved.heart.beat.timeout":"15000","spring.application.name":"distribution","preserved.ip.delete.timeout":"30000","env":"root","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.452[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.459[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-admin -> [{"instanceId":"**********#8988#DEFAULT#DEFAULT_GROUP@@builder-admin","ip":"**********","port":8988,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-admin","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.463[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-admin -> [{"instanceId":"**********#8988#DEFAULT#DEFAULT_GROUP@@builder-admin","ip":"**********","port":8988,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-admin","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.673[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarted,59][0;39m - Started GatewayXApplication in 12.377 seconds (process running for 13.491)
[1;35m14:49:29.712[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] gateway-x.yml+DEFAULT_GROUP+pedestal
[1;35m14:49:29.713[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=gateway-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m14:49:29.713[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=gateway-x.yml, group=DEFAULT_GROUP
[1;35m14:49:29.734[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal
[1;35m14:49:29.735[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m14:49:29.735[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m14:49:29.749[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal
[1;35m14:49:29.750[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m14:49:29.750[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m14:49:29.781[0;39m [32m[nacos-grpc-client-executor-***********-12][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7246
[1;35m14:49:29.783[0;39m [32m[nacos-grpc-client-executor-***********-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.784[0;39m [32m[nacos-grpc-client-executor-***********-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:29.792[0;39m [32m[nacos-grpc-client-executor-***********-12][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7246
[1;35m14:49:29.980[0;39m [32m[nacos-grpc-client-executor-***********-16][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7248
[1;35m14:49:29.981[0;39m [32m[nacos-grpc-client-executor-***********-16][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7248
[1;35m14:49:29.984[0;39m [32m[nacos-grpc-client-executor-***********-13][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7249
[1;35m14:49:29.986[0;39m [32m[nacos-grpc-client-executor-***********-13][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7249
[1;35m14:49:29.989[0;39m [32m[nacos-grpc-client-executor-***********-14][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7250
[1;35m14:49:29.991[0;39m [32m[nacos-grpc-client-executor-***********-14][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7250
[1;35m14:49:29.995[0;39m [32m[nacos-grpc-client-executor-***********-17][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7251
[1;35m14:49:29.995[0;39m [32m[nacos-grpc-client-executor-***********-17][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7251
[1;35m14:49:29.997[0;39m [32m[nacos-grpc-client-executor-***********-18][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7252
[1;35m14:49:29.998[0;39m [32m[nacos-grpc-client-executor-***********-18][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7252
[1;35m14:49:30.000[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7253
[1;35m14:49:30.000[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7253
[1;35m14:49:30.004[0;39m [32m[nacos-grpc-client-executor-***********-20][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7254
[1;35m14:49:30.005[0;39m [32m[nacos-grpc-client-executor-***********-20][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7254
[1;35m14:49:30.007[0;39m [32m[nacos-grpc-client-executor-***********-21][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7255
[1;35m14:49:30.008[0;39m [32m[nacos-grpc-client-executor-***********-21][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7255
[1;35m14:49:30.010[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7256
[1;35m14:49:30.010[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7256
[1;35m14:49:30.012[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7257
[1;35m14:49:30.012[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7257
[1;35m14:49:30.014[0;39m [32m[nacos-grpc-client-executor-***********-22][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7258
[1;35m14:49:30.015[0;39m [32m[nacos-grpc-client-executor-***********-22][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7258
[1;35m14:49:30.118[0;39m [32m[RMI TCP Connection(2)-**********][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m14:49:43.176[0;39m [32m[nacos-grpc-client-executor-***********-34][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7259
[1;35m14:49:43.177[0;39m [32m[nacos-grpc-client-executor-***********-34][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:43.186[0;39m [32m[nacos-grpc-client-executor-***********-34][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:49:43.200[0;39m [32m[nacos-grpc-client-executor-***********-34][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7259
[1;35m14:51:37.290[0;39m [32m[nacos-grpc-client-executor-***********-79][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7263
[1;35m14:51:37.290[0;39m [32m[nacos-grpc-client-executor-***********-79][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:51:37.290[0;39m [32m[nacos-grpc-client-executor-***********-79][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:51:37.300[0;39m [32m[nacos-grpc-client-executor-***********-79][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7263
[1;35m14:54:09.569[0;39m [32m[nacos-grpc-client-executor-***********-147][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Receive server push request, request = NotifySubscriberRequest, requestId = 7274
[1;35m14:54:09.570[0;39m [32m[nacos-grpc-client-executor-***********-147][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,107][0;39m - removed ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:54:09.572[0;39m [32m[nacos-grpc-client-executor-***********-147][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(0) service: DEFAULT_GROUP@@system-x -> []
[1;35m14:54:09.594[0;39m [32m[nacos-grpc-client-executor-***********-147][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [9e1f4e43-61f7-4854-bdf3-afbec394a765] Ack server push request, request = NotifySubscriberRequest, requestId = 7274
[1;35m14:55:47.704[0;39m [32m[background-preinit][0;39m [34mINFO [0;39m [36mo.h.v.i.util.Version[0;39m - [36m[<clinit>,21][0;39m - HV000001: Hibernate Validator 8.0.2.Final
[1;35m14:55:47.923[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.PasswordInitializer[0;39m - [36m[initialize,15][0;39m - ApplicationContextInitializer<ConfigurableApplicationContext> =======> 
[1;35m14:55:47.926[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,27][0;39m - 开始解密
[1;35m14:55:48.019[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.c.r.RSADcryEnv[0;39m - [36m[decrypt,30][0;39m - 解密完成
[1;35m14:55:48.028[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarting,53][0;39m - Starting GatewayXApplication using Java 17.0.14 with PID 30004 (D:\x\gateway-x\gateway-x-server\target\classes started by Administrator in D:\x)
[1;35m14:55:48.030[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStartupProfileInfo,658][0;39m - The following 1 profile is active: "pedestal"
[1;35m14:55:48.108[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-config.yml, group=DEFAULT_GROUP] success
[1;35m14:55:48.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=server-info.yml, group=DEFAULT_GROUP] success
[1;35m14:55:48.109[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.c.NacosConfigDataLoader[0;39m - [36m[logTo,252][0;39m - [Nacos Config] Load config[dataId=gateway-x.yml, group=DEFAULT_GROUP] success
[1;35m14:55:50.275[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.f.s.DefaultListableBeanFactory[0;39m - [36m[logBeanDefinitionOverriding,1334][0;39m - Overriding bean definition for bean 'sentinelGatewayFilter' with a different definition: replacing [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=gatewayConfig; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/uino/x/gatewayx/config/GatewayConfig.class]] with [Root bean: class=null; scope=; abstract=false; lazyInit=null; autowireMode=3; dependencyCheck=0; autowireCandidate=true; primary=false; fallback=false; factoryBeanName=com.alibaba.cloud.sentinel.gateway.scg.SentinelSCGAutoConfiguration; factoryMethodName=sentinelGatewayFilter; initMethodNames=null; destroyMethodNames=[(inferred)]; defined in class path resource [com/alibaba/cloud/sentinel/gateway/scg/SentinelSCGAutoConfiguration.class]]
[1;35m14:55:50.755[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[multipleStoresDetected,294][0;39m - Multiple Spring Data modules found, entering strict repository configuration mode
[1;35m14:55:50.761[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,145][0;39m - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[1;35m14:55:50.918[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.d.r.c.RepositoryConfigurationDelegate[0;39m - [36m[registerRepositoriesIn,213][0;39m - Finished Spring Data repository scanning in 120 ms. Found 0 Redis repository interfaces.
[1;35m14:55:51.349[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.c.s.GenericScope[0;39m - [36m[setSerializationId,280][0;39m - BeanFactory id=a682fabe-27a3-3ecc-8e94-3197e1d58eef
[1;35m14:55:53.577[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m14:55:54.184[0;39m [32m[redisson-netty-3-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m14:55:54.224[0;39m [32m[redisson-netty-3-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m14:55:54.538[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m14:55:54.592[0;39m [32m[redisson-netty-6-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m14:55:54.621[0;39m [32m[redisson-netty-6-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m14:55:55.087[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayFilter,143][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
[1;35m14:55:56.861[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [After]
[1;35m14:55:56.861[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Before]
[1;35m14:55:56.862[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Between]
[1;35m14:55:56.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Cookie]
[1;35m14:55:56.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Header]
[1;35m14:55:56.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Host]
[1;35m14:55:56.863[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Method]
[1;35m14:55:56.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Path]
[1;35m14:55:56.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Query]
[1;35m14:55:56.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [ReadBody]
[1;35m14:55:56.864[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [RemoteAddr]
[1;35m14:55:56.865[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [XForwardedRemoteAddr]
[1;35m14:55:56.865[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [Weight]
[1;35m14:55:56.865[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.c.g.r.RouteDefinitionRouteLocator[0;39m - [36m[lambda$initFactories$1,89][0;39m - Loaded RoutePredicateFactory [CloudFoundryRouteService]
[1;35m14:55:57.425[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.a.e.w.EndpointLinksResolver[0;39m - [36m[<init>,60][0;39m - Exposing 1 endpoint beneath base path '/actuator'
[1;35m14:55:57.767[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.s.g.s.SentinelSCGAutoConfiguration[0;39m - [36m[sentinelGatewayBlockExceptionHandler,133][0;39m - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
[1;35m14:55:57.953[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.redisson.Version[0;39m - [36m[logVersion,43][0;39m - Redisson 3.50.0
[1;35m14:55:57.989[0;39m [32m[redisson-netty-11-6][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 1 connections initialized for ***********/***********:6379
[1;35m14:55:58.042[0;39m [32m[redisson-netty-11-15][0;39m [34mINFO [0;39m [36mo.r.c.ConnectionsHolder[0;39m - [36m[lambda$initConnections$1,132][0;39m - 6 connections initialized for ***********/***********:6379
[1;35m14:55:58.740[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.NacosConfigService[0;39m - [36m[<init>,78][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m14:55:58.742[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,48][0;39m - DefaultLabelsCollectorManager get labels.....
[1;35m14:55:58.742[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,62][0;39m - Process LabelsCollector with [name:defaultNacosLabelsCollector]
[1;35m14:55:58.742[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,64][0;39m - default nacos collect properties raw labels: null
[1;35m14:55:58.743[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,71][0;39m - default nacos collect properties labels: {}
[1;35m14:55:58.743[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,74][0;39m - default nacos collect jvm raw labels: null
[1;35m14:55:58.743[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,80][0;39m - default nacos collect jvm labels: {}
[1;35m14:55:58.743[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,83][0;39m - default nacos collect env raw labels: null
[1;35m14:55:58.743[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[collectLabels,91][0;39m - default nacos collect env labels: {}
[1;35m14:55:58.744[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.common.labels[0;39m - [36m[getLabels,50][0;39m - DefaultLabelsCollectorManager get labels finished,labels :{}
[1;35m14:55:58.744[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m14:55:58.744[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m14:55:58.877[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[initNotifyWarnTimeout,72][0;39m - config listener notify warn timeout millis use default 60000 millis 
[1;35m14:55:58.878[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[<clinit>,99][0;39m - nacos.cache.data.init.snapshot = true 
[1;35m14:55:58.886[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] gateway-x-sentinel.json+DEFAULT_GROUP+pedestal
[1;35m14:55:58.900[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=gateway-x-sentinel.json, group=DEFAULT_GROUP, cnt=1
[1;35m14:55:58.900[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 0d18c395-1618-43ea-82ed-7fc0961f3639_config-0
[1;35m14:55:58.901[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$352/0x000002243f2be858
[1;35m14:55:58.901[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$353/0x000002243f2bec88
[1;35m14:55:58.901[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
[1;35m14:55:58.902[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
[1;35m14:55:58.902[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m14:55:58.902[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m14:55:58.920[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Success to connect to server [***********:8848] on start up, connectionId = 1756104958651_**********_63522
[1;35m14:55:58.920[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m14:55:58.920[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Notify connected event to listeners.
[1;35m14:55:58.921[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$371/0x000002243f420000
[1;35m14:55:58.921[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[onConnected,713][0;39m - [0d18c395-1618-43ea-82ed-7fc0961f3639_config-0] Connected,notify listen context...
[1;35m14:55:59.041[0;39m [32m[main][0;39m [34mINFO [0;39m [36mo.s.b.w.e.n.NettyWebServer[0;39m - [36m[start,126][0;39m - Netty started on port 10001 (http)
[1;35m14:55:59.358[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[init,102][0;39m - Nacos client key init properties: 
	serverAddr=***********:8848
	namespace=pedestal
	username=nacos
	password=Xq*******os

[1;35m14:55:59.359[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[initNamespaceForNaming,62][0;39m - initializer namespace from ans.namespace attribute : null
[1;35m14:55:59.360[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$0,66][0;39m - initializer namespace from ALIBABA_ALIWARE_NAMESPACE attribute :null
[1;35m14:55:59.360[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[lambda$initNamespaceForNaming$1,73][0;39m - initializer namespace from namespace attribute :null
[1;35m14:55:59.370[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,75][0;39m - FailoverDataSource type is class com.alibaba.nacos.client.naming.backups.datasource.DiskFailoverDataSource
[1;35m14:55:59.377[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[1;35m14:55:59.377[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[0;39m - [36m[init,56][0;39m - [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[1;35m14:55:59.513[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[lambda$createClient$0,118][0;39m - [RpcClientFactory] create a new rpc client of 872a1bcd-fd05-468c-acf2-0fb1301fdf10
[1;35m14:55:59.517[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[<init>,109][0;39m - Create naming rpc client for uuid->872a1bcd-fd05-468c-acf2-0fb1301fdf10
[1;35m14:55:59.517[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
[1;35m14:55:59.517[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
[1;35m14:55:59.518[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
[1;35m14:55:59.518[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Try to connect to server on start up, server: {serverIp = '***********', server main port = 8848}
[1;35m14:55:59.519[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[createNewManagedChannel,201][0;39m - grpc client connection server:*********** ip,serverPort:9848,grpcTslConfig:{"enableTls":false,"mutualAuthEnable":false,"trustAll":true}
[1;35m14:55:59.535[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Success to connect to server [***********:8848] on start up, connectionId = 1756104959266_**********_63524
[1;35m14:55:59.535[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
[1;35m14:55:59.535[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Notify connected event to listeners.
[1;35m14:55:59.535[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$371/0x000002243f420000
[1;35m14:55:59.535[0;39m [32m[com.alibaba.nacos.client.remote.worker.0][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[onConnected,90][0;39m - Grpc connection connect
[1;35m14:55:59.537[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[registerService,133][0;39m - [REGISTER-SERVICE] pedestal registering service gateway-x with instance Instance{instanceId='null', ip='**********', port=10001, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}
[1;35m14:55:59.547[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[register,76][0;39m - nacos registry, DEFAULT_GROUP gateway-x **********:10001 register finished
[1;35m14:55:59.620[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.620[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-city, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.620[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:seata-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.621[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-decoration, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.621[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.621[0;39m [32m[boundedElastic-5][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:seata-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.621[0;39m [32m[boundedElastic-4][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-city, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.621[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-campus, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-6][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-decoration, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:system-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-campus, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-8][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:system-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-city, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:edtap-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-city, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.622[0;39m [32m[boundedElastic-9][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:edtap-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.623[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:seata-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.623[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.623[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:seata-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.623[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.623[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.624[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:distribution, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.625[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.624[0;39m [32m[boundedElastic-17][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-campus, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.624[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-decoration, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.626[0;39m [32m[boundedElastic-14][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:distribution, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.626[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-admin, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.626[0;39m [32m[boundedElastic-17][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-campus, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.626[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-admin, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.626[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.626[0;39m [32m[boundedElastic-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:log-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.627[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:ti-dix, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.627[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-decoration, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.627[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:edtap-server, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.627[0;39m [32m[boundedElastic-21][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:scene-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.627[0;39m [32m[boundedElastic-1][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:log-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.628[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:ti-dix, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.628[0;39m [32m[boundedElastic-21][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:scene-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.628[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:edtap-server, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.628[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:distribution, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:distribution, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:gateway-x, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:ti-dix, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-23][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:gateway-x, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-25][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:ti-dix, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,164][0;39m - [SUBSCRIBE-SERVICE] service:builder-admin, group:DEFAULT_GROUP, clusters: 
[1;35m14:55:59.629[0;39m [32m[boundedElastic-24][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[subscribe,377][0;39m - [GRPC-SUBSCRIBE] service:builder-admin, group:DEFAULT_GROUP, cluster: 
[1;35m14:55:59.644[0;39m [32m[boundedElastic-3][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(0) service: DEFAULT_GROUP@@system-x -> []
[1;35m14:55:59.644[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.645[0;39m [32m[boundedElastic-16][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.651[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.651[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.651[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-campus -> [{"instanceId":"**********#8990#DEFAULT#DEFAULT_GROUP@@builder-campus","ip":"**********","port":8990,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-campus","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.651[0;39m [32m[boundedElastic-12][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@log-x -> [{"instanceId":"***********#10014#DEFAULT#DEFAULT_GROUP@@log-x","ip":"***********","port":10014,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@log-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.652[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-city -> [{"instanceId":"**********#8989#DEFAULT#DEFAULT_GROUP@@builder-city","ip":"**********","port":8989,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-city","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.652[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@seata-server -> [{"instanceId":"***********#8091#default#DEFAULT_GROUP@@seata-server","ip":"***********","port":8091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"default","serviceName":"DEFAULT_GROUP@@seata-server","metadata":{},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceIdGenerator":"simple","instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.652[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-admin -> [{"instanceId":"**********#8988#DEFAULT#DEFAULT_GROUP@@builder-admin","ip":"**********","port":8988,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-admin","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.652[0;39m [32m[boundedElastic-11][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@ti-dix -> [{"instanceId":"***********#10009#DEFAULT#DEFAULT_GROUP@@ti-dix","ip":"***********","port":10009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ti-dix","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@edtap-server -> [{"instanceId":"***********#10011##DEFAULT_GROUP@@edtap-server","ip":"***********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@builder-decoration -> [{"instanceId":"**********#8991#DEFAULT#DEFAULT_GROUP@@builder-decoration","ip":"**********","port":8991,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-decoration","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-7][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-campus -> [{"instanceId":"**********#8990#DEFAULT#DEFAULT_GROUP@@builder-campus","ip":"**********","port":8990,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-campus","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-13][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@seata-server -> [{"instanceId":"***********#8091#default#DEFAULT_GROUP@@seata-server","ip":"***********","port":8091,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"default","serviceName":"DEFAULT_GROUP@@seata-server","metadata":{},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceIdGenerator":"simple","instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-18][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-admin -> [{"instanceId":"**********#8988#DEFAULT#DEFAULT_GROUP@@builder-admin","ip":"**********","port":8988,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-admin","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.654[0;39m [32m[boundedElastic-10][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-city -> [{"instanceId":"**********#8989#DEFAULT#DEFAULT_GROUP@@builder-city","ip":"**********","port":8989,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-city","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.655[0;39m [32m[boundedElastic-20][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@ti-dix -> [{"instanceId":"***********#10009#DEFAULT#DEFAULT_GROUP@@ti-dix","ip":"***********","port":10009,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@ti-dix","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.655[0;39m [32m[boundedElastic-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@edtap-server -> [{"instanceId":"***********#10011##DEFAULT_GROUP@@edtap-server","ip":"***********","port":10011,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@edtap-server","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.655[0;39m [32m[boundedElastic-15][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@builder-decoration -> [{"instanceId":"**********#8991#DEFAULT#DEFAULT_GROUP@@builder-decoration","ip":"**********","port":8991,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@builder-decoration","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.668[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,51][0;39m - init new ips(1) service: DEFAULT_GROUP@@distribution -> [{"instanceId":"***********#81#DEFAULT#DEFAULT_GROUP@@distribution","ip":"***********","port":81,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@distribution","metadata":{"preserved.heart.beat.timeout":"15000","spring.application.name":"distribution","preserved.ip.delete.timeout":"30000","env":"root","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.669[0;39m [32m[boundedElastic-22][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@distribution -> [{"instanceId":"***********#81#DEFAULT#DEFAULT_GROUP@@distribution","ip":"***********","port":81,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@distribution","metadata":{"preserved.heart.beat.timeout":"15000","spring.application.name":"distribution","preserved.ip.delete.timeout":"30000","env":"root","preserved.register.source":"SPRING_CLOUD","version":"1.0.0","preserved.heart.beat.interval":"5000"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:55:59.826[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.u.x.GatewayXApplication[0;39m - [36m[logStarted,59][0;39m - Started GatewayXApplication in 16.405 seconds (process running for 17.458)
[1;35m14:55:59.839[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] gateway-x.yml+DEFAULT_GROUP+pedestal
[1;35m14:55:59.839[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=gateway-x.yml, group=DEFAULT_GROUP, cnt=1
[1;35m14:55:59.840[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=gateway-x.yml, group=DEFAULT_GROUP
[1;35m14:55:59.855[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-config.yml+DEFAULT_GROUP+pedestal
[1;35m14:55:59.856[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-config.yml, group=DEFAULT_GROUP, cnt=1
[1;35m14:55:59.856[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-config.yml, group=DEFAULT_GROUP
[1;35m14:55:59.858[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.ClientWorker[0;39m - [36m[addCacheDataIfAbsent,418][0;39m - [fixed-pedestal-***********_8848] [subscribe] server-info.yml+DEFAULT_GROUP+pedestal
[1;35m14:55:59.858[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.n.c.c.i.CacheData[0;39m - [36m[addListener,236][0;39m - [fixed-pedestal-***********_8848] [add-listener] ok, tenant=pedestal, dataId=server-info.yml, group=DEFAULT_GROUP, cnt=1
[1;35m14:55:59.858[0;39m [32m[main][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosContextRefresher[0;39m - [36m[registerNacosListener,145][0;39m - [Nacos Config] Listening config: dataId=server-info.yml, group=DEFAULT_GROUP
[1;35m14:56:00.092[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7283
[1;35m14:56:00.093[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:56:00.094[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@gateway-x -> [{"instanceId":"***********#10001##DEFAULT_GROUP@@gateway-x","ip":"***********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"**********#10001##DEFAULT_GROUP@@gateway-x","ip":"**********","port":10001,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@gateway-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:56:00.103[0;39m [32m[nacos-grpc-client-executor-***********-19][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7283
[1;35m14:56:00.156[0;39m [32m[RMI TCP Connection(4)-**********][0;39m [34mINFO [0;39m [36mc.a.c.s.e.SentinelHealthIndicator[0;39m - [36m[doHealthCheck,92][0;39m - Find sentinel dashboard server list: []
[1;35m14:56:00.193[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7285
[1;35m14:56:00.194[0;39m [32m[nacos-grpc-client-executor-***********-23][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7285
[1;35m14:56:00.197[0;39m [32m[nacos-grpc-client-executor-***********-25][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7286
[1;35m14:56:00.198[0;39m [32m[nacos-grpc-client-executor-***********-25][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7286
[1;35m14:56:00.200[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7287
[1;35m14:56:00.201[0;39m [32m[nacos-grpc-client-executor-***********-24][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7287
[1;35m14:56:00.204[0;39m [32m[nacos-grpc-client-executor-***********-28][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7288
[1;35m14:56:00.204[0;39m [32m[nacos-grpc-client-executor-***********-28][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7288
[1;35m14:56:00.206[0;39m [32m[nacos-grpc-client-executor-***********-29][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7289
[1;35m14:56:00.207[0;39m [32m[nacos-grpc-client-executor-***********-29][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7289
[1;35m14:56:00.208[0;39m [32m[nacos-grpc-client-executor-***********-27][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7290
[1;35m14:56:00.210[0;39m [32m[nacos-grpc-client-executor-***********-27][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7290
[1;35m14:56:00.211[0;39m [32m[nacos-grpc-client-executor-***********-30][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7291
[1;35m14:56:00.211[0;39m [32m[nacos-grpc-client-executor-***********-30][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7291
[1;35m14:56:00.213[0;39m [32m[nacos-grpc-client-executor-***********-26][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7292
[1;35m14:56:00.213[0;39m [32m[nacos-grpc-client-executor-***********-26][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7292
[1;35m14:56:00.216[0;39m [32m[nacos-grpc-client-executor-***********-0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7293
[1;35m14:56:00.217[0;39m [32m[nacos-grpc-client-executor-***********-0][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7293
[1;35m14:56:00.218[0;39m [32m[nacos-grpc-client-executor-***********-1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7294
[1;35m14:56:00.218[0;39m [32m[nacos-grpc-client-executor-***********-1][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7294
[1;35m14:56:00.220[0;39m [32m[nacos-grpc-client-executor-***********-2][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7295
[1;35m14:56:00.220[0;39m [32m[nacos-grpc-client-executor-***********-2][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7295
[1;35m14:56:19.345[0;39m [32m[nacos-grpc-client-executor-***********-36][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7297
[1;35m14:56:19.346[0;39m [32m[nacos-grpc-client-executor-***********-36][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:56:19.347[0;39m [32m[nacos-grpc-client-executor-***********-36][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(2) service: DEFAULT_GROUP@@scene-x -> [{"instanceId":"**********#10007##DEFAULT_GROUP@@scene-x","ip":"**********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000},{"instanceId":"***********#10007##DEFAULT_GROUP@@scene-x","ip":"***********","port":10007,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@scene-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:56:19.357[0;39m [32m[nacos-grpc-client-executor-***********-36][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7297
[1;35m14:56:44.303[0;39m [32m[nacos-grpc-client-executor-***********-45][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Receive server push request, request = NotifySubscriberRequest, requestId = 7301
[1;35m14:56:44.303[0;39m [32m[nacos-grpc-client-executor-***********-45][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[doDiff,101][0;39m - new ips(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:56:44.304[0;39m [32m[nacos-grpc-client-executor-***********-45][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[processServiceInfo,142][0;39m - current ips:(1) service: DEFAULT_GROUP@@system-x -> [{"instanceId":"**********#10004##DEFAULT_GROUP@@system-x","ip":"**********","port":10004,"weight":1.0,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@system-x","metadata":{"preserved.register.source":"SPRING_CLOUD"},"ipDeleteTimeout":30000,"instanceHeartBeatInterval":5000,"instanceHeartBeatTimeOut":15000}]
[1;35m14:56:44.315[0;39m [32m[nacos-grpc-client-executor-***********-45][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[printIfInfoEnabled,63][0;39m - [872a1bcd-fd05-468c-acf2-0fb1301fdf10] Ack server push request, request = NotifySubscriberRequest, requestId = 7301
[1;35m15:23:38.501[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[deregister,95][0;39m - De-registering from Nacos Server now...
[1;35m15:23:38.502[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[deregisterService,272][0;39m - [DEREGISTER-SERVICE] pedestal deregistering service gateway-x with instance: Instance{instanceId='null', ip='**********', port=10001, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='', serviceName='null', metadata={}}
[1;35m15:23:38.519[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.c.n.r.NacosServiceRegistry[0;39m - [36m[deregister,115][0;39m - De-registration finished.
[1;35m15:23:38.520[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,180][0;39m - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown begin
[1;35m15:23:38.521[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,182][0;39m - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin
[1;35m15:23:38.521[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,184][0;39m - com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop
[1;35m15:23:38.521[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,182][0;39m - com.alibaba.nacos.client.naming.cache.ServiceInfoHolder do shutdown stop
[1;35m15:23:38.522[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,204][0;39m - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown begin
[1;35m15:23:38.522[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,147][0;39m - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown begin
[1;35m15:23:38.522[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,149][0;39m - com.alibaba.nacos.client.naming.core.ServiceInfoUpdateService do shutdown stop
[1;35m15:23:38.523[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,218][0;39m - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown begin
[1;35m15:23:38.524[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,223][0;39m - com.alibaba.nacos.client.naming.core.ServerListManager do shutdown stop
[1;35m15:23:38.525[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,468][0;39m - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown begin
[1;35m15:23:38.525[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,470][0;39m - com.alibaba.nacos.client.naming.remote.http.NamingHttpClientProxy do shutdown stop
[1;35m15:23:38.525[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,487][0;39m - Shutdown naming grpc client proxy for  uuid->872a1bcd-fd05-468c-acf2-0fb1301fdf10
[1;35m15:23:38.526[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,331][0;39m - Shutdown grpc redo service executor java.util.concurrent.ScheduledThreadPoolExecutor@48ce4405[Running, pool size = 1, active threads = 0, queued tasks = 1, completed tasks = 551]
[1;35m15:23:38.526[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[shutdown,425][0;39m - Shutdown rpc client, set status to shutdown
[1;35m15:23:38.526[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[shutdown,427][0;39m - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@49af774a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
[1;35m15:23:38.526[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.client[0;39m - [36m[closeConnection,585][0;39m - Close current connection 1756104959266_**********_63524
[1;35m15:23:38.529[0;39m [32m[nacos-grpc-client-executor-***********-664][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[printIfInfoEnabled,63][0;39m - [1756104959266_**********_63524]Ignore complete event,isRunning:false,isAbandon=false
[1;35m15:23:38.550[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.r.c.g.GrpcClient[0;39m - [36m[shutdown,178][0;39m - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d42d9b7[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 773]
[1;35m15:23:38.550[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutDownAndRemove,497][0;39m - shutdown and remove naming rpc client  for uuid ->872a1bcd-fd05-468c-acf2-0fb1301fdf10
[1;35m15:23:38.551[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.a.r.i.CredentialWatcher[0;39m - [36m[stop,107][0;39m - [null] CredentialWatcher is stopped
[1;35m15:23:38.551[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.c.a.r.i.CredentialService[0;39m - [36m[free,91][0;39m - [null] CredentialService is freed
[1;35m15:23:38.552[0;39m [32m[SpringApplicationShutdownHook][0;39m [34mINFO [0;39m [36mc.a.n.client.naming[0;39m - [36m[shutdown,211][0;39m - com.alibaba.nacos.client.naming.remote.NamingClientProxyDelegate do shutdown stop
