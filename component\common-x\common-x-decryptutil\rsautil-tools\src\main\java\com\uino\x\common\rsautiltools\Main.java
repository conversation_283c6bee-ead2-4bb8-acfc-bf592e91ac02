package com.uino.x.common.rsautiltools;

public class Main {
    public static void main(String[] args) {
//        if (args.length < 2) {
//            System.out.println("Usage: java Main -e <text_to_encrypt> | -d <encrypted_text>");
//            return;
//        }
//
//        String operation = args[0];
//        String input = args[1];
//
//        try {
//            // 设置 RSA 配置
//            RSAconfigration rsaConfig = new RSAconfigration("");
//            RSAUtil.setRsaConfig(rsaConfig);
//
//            switch (operation) {
//                case "-e":
//                    // 加密操作
//                    String encryptedText = RSAUtil.encrypt(input);
//                    System.out.println("Encrypted Text: " + encryptedText);
//                    break;
//
//                case "-d":
//                    // 解密操作
//                    String decryptedText = RSAUtil.decrypt(input);
//                    System.out.println("Decrypted Text: " + decryptedText);
//                    break;
//
//                default:
//                    System.out.println("Invalid operation. Use '-e' for encryption and '-d' for decryption.");
//                    break;
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        String decryptedText = RSAUtil.decrypt("VtKuLd7SeCUzuDLkJgX6ZCqAQ6UT+sVzUGaVoXAD+m6Jw3snQgoUhg2y/xA4Kd4qpqozKJ8MzAyP49WKBrovGUvDe+/adIOR/l0uH9NOj+f8BfZQ+RvnrfnXuSmEkwBDAS9YfHlXsTJ77VQlC26K44snAdTZLeovuRyMoAqV5E4b3N6DRjWEF14fcrZjEt08TEvGrz3SnzzumAt5jVX1NkiaPzZjMCCSiLuRcO0NCg5GIHtJeNsgU1QZWzrsQMVH1vETEFrALbUImzytP+SCaotaa6+vBd6Gy0PeZlurr25kAjy04ESdKuZDraLgzfS3SG55lXgq0EMI6OWONEH2gQ==");
        System.out.println("Decrypted Text: " + decryptedText);
    }
}