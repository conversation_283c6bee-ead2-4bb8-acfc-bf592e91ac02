{"version": 3, "sources": ["../../validator/lib/util/assertString.js", "../../validator/lib/util/merge.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = assertString;\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction assertString(input) {\n  var isString = typeof input === 'string' || input instanceof String;\n  if (!isString) {\n    var invalidType = _typeof(input);\n    if (input === null) invalidType = 'null';else if (invalidType === 'object') invalidType = input.constructor.name;\n    throw new TypeError(\"Expected a string but received a \".concat(invalidType));\n  }\n}\nmodule.exports = exports.default;\nmodule.exports.default = exports.default;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = merge;\nfunction merge() {\n  var obj = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var defaults = arguments.length > 1 ? arguments[1] : undefined;\n  for (var key in defaults) {\n    if (typeof obj[key] === 'undefined') {\n      obj[key] = defaults[key];\n    }\n  }\n  return obj;\n}\nmodule.exports = exports.default;\nmodule.exports.default = exports.default;"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,aAAS,QAAQ,GAAG;AAAE;AAA2B,aAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAAE,eAAO,OAAOA;AAAA,MAAG,IAAI,SAAUA,IAAG;AAAE,eAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,MAAG,GAAG,QAAQ,CAAC;AAAA,IAAG;AAC7T,aAAS,aAAa,OAAO;AAC3B,UAAI,WAAW,OAAO,UAAU,YAAY,iBAAiB;AAC7D,UAAI,CAAC,UAAU;AACb,YAAI,cAAc,QAAQ,KAAK;AAC/B,YAAI,UAAU,KAAM,eAAc;AAAA,iBAAgB,gBAAgB,SAAU,eAAc,MAAM,YAAY;AAC5G,cAAM,IAAI,UAAU,oCAAoC,OAAO,WAAW,CAAC;AAAA,MAC7E;AAAA,IACF;AACA,WAAO,UAAU,QAAQ;AACzB,WAAO,QAAQ,UAAU,QAAQ;AAAA;AAAA;;;AChBjC;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,aAAS,QAAQ;AACf,UAAI,MAAM,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAC/E,UAAI,WAAW,UAAU,SAAS,IAAI,UAAU,CAAC,IAAI;AACrD,eAAS,OAAO,UAAU;AACxB,YAAI,OAAO,IAAI,GAAG,MAAM,aAAa;AACnC,cAAI,GAAG,IAAI,SAAS,GAAG;AAAA,QACzB;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,WAAO,UAAU,QAAQ;AACzB,WAAO,QAAQ,UAAU,QAAQ;AAAA;AAAA;", "names": ["o"]}