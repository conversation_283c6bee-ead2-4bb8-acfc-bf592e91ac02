// node_modules/js-binary-schema-parser/src/index.js
var parse = (stream, schema, result = {}, parent = result) => {
  if (Array.isArray(schema)) {
    schema.forEach((partSchema) => parse(stream, partSchema, result, parent));
  } else if (typeof schema === "function") {
    schema(stream, result, parent, parse);
  } else {
    const key = Object.keys(schema)[0];
    if (Array.isArray(schema[key])) {
      parent[key] = {};
      parse(stream, schema[key], result, parent[key]);
    } else {
      parent[key] = schema[key](stream, result, parent, parse);
    }
  }
  return result;
};
var conditional = (schema, conditionFunc) => (stream, result, parent, parse2) => {
  if (conditionFunc(stream, result, parent)) {
    parse2(stream, schema, result, parent);
  }
};
var loop = (schema, continueFunc) => (stream, result, parent, parse2) => {
  const arr = [];
  let lastStreamPos = stream.pos;
  while (continueFunc(stream, result, parent)) {
    const newParent = {};
    parse2(stream, schema, result, newParent);
    if (stream.pos === lastStreamPos) {
      break;
    }
    lastStreamPos = stream.pos;
    arr.push(newParent);
  }
  return arr;
};

export {
  parse,
  conditional,
  loop
};
//# sourceMappingURL=chunk-X3EZE54F.js.map
