{"version": 3, "sources": ["../../tinycolor2/esm/tinycolor.js"], "sourcesContent": ["// This file is autogenerated. It's used to publish ESM to npm.\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\n\n// https://github.com/bgrins/TinyColor\n// <PERSON>, MIT License\n\nvar trimLeft = /^\\s+/;\nvar trimRight = /\\s+$/;\nfunction tinycolor(color, opts) {\n  color = color ? color : \"\";\n  opts = opts || {};\n\n  // If input is already a tinycolor, return itself\n  if (color instanceof tinycolor) {\n    return color;\n  }\n  // If we are called as a function, call using new instead\n  if (!(this instanceof tinycolor)) {\n    return new tinycolor(color, opts);\n  }\n  var rgb = inputToRGB(color);\n  this._originalInput = color, this._r = rgb.r, this._g = rgb.g, this._b = rgb.b, this._a = rgb.a, this._roundA = Math.round(100 * this._a) / 100, this._format = opts.format || rgb.format;\n  this._gradientType = opts.gradientType;\n\n  // Don't let the range of [0,255] come back in [0,1].\n  // Potentially lose a little bit of precision here, but will fix issues where\n  // .5 gets interpreted as half of the total, instead of half of 1\n  // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n  if (this._r < 1) this._r = Math.round(this._r);\n  if (this._g < 1) this._g = Math.round(this._g);\n  if (this._b < 1) this._b = Math.round(this._b);\n  this._ok = rgb.ok;\n}\ntinycolor.prototype = {\n  isDark: function isDark() {\n    return this.getBrightness() < 128;\n  },\n  isLight: function isLight() {\n    return !this.isDark();\n  },\n  isValid: function isValid() {\n    return this._ok;\n  },\n  getOriginalInput: function getOriginalInput() {\n    return this._originalInput;\n  },\n  getFormat: function getFormat() {\n    return this._format;\n  },\n  getAlpha: function getAlpha() {\n    return this._a;\n  },\n  getBrightness: function getBrightness() {\n    //http://www.w3.org/TR/AERT#color-contrast\n    var rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  },\n  getLuminance: function getLuminance() {\n    //http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    var rgb = this.toRgb();\n    var RsRGB, GsRGB, BsRGB, R, G, B;\n    RsRGB = rgb.r / 255;\n    GsRGB = rgb.g / 255;\n    BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) R = RsRGB / 12.92;else R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    if (GsRGB <= 0.03928) G = GsRGB / 12.92;else G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    if (BsRGB <= 0.03928) B = BsRGB / 12.92;else B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  },\n  setAlpha: function setAlpha(value) {\n    this._a = boundAlpha(value);\n    this._roundA = Math.round(100 * this._a) / 100;\n    return this;\n  },\n  toHsv: function toHsv() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this._a\n    };\n  },\n  toHsvString: function toHsvString() {\n    var hsv = rgbToHsv(this._r, this._g, this._b);\n    var h = Math.round(hsv.h * 360),\n      s = Math.round(hsv.s * 100),\n      v = Math.round(hsv.v * 100);\n    return this._a == 1 ? \"hsv(\" + h + \", \" + s + \"%, \" + v + \"%)\" : \"hsva(\" + h + \", \" + s + \"%, \" + v + \"%, \" + this._roundA + \")\";\n  },\n  toHsl: function toHsl() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this._a\n    };\n  },\n  toHslString: function toHslString() {\n    var hsl = rgbToHsl(this._r, this._g, this._b);\n    var h = Math.round(hsl.h * 360),\n      s = Math.round(hsl.s * 100),\n      l = Math.round(hsl.l * 100);\n    return this._a == 1 ? \"hsl(\" + h + \", \" + s + \"%, \" + l + \"%)\" : \"hsla(\" + h + \", \" + s + \"%, \" + l + \"%, \" + this._roundA + \")\";\n  },\n  toHex: function toHex(allow3Char) {\n    return rgbToHex(this._r, this._g, this._b, allow3Char);\n  },\n  toHexString: function toHexString(allow3Char) {\n    return \"#\" + this.toHex(allow3Char);\n  },\n  toHex8: function toHex8(allow4Char) {\n    return rgbaToHex(this._r, this._g, this._b, this._a, allow4Char);\n  },\n  toHex8String: function toHex8String(allow4Char) {\n    return \"#\" + this.toHex8(allow4Char);\n  },\n  toRgb: function toRgb() {\n    return {\n      r: Math.round(this._r),\n      g: Math.round(this._g),\n      b: Math.round(this._b),\n      a: this._a\n    };\n  },\n  toRgbString: function toRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \")\" : \"rgba(\" + Math.round(this._r) + \", \" + Math.round(this._g) + \", \" + Math.round(this._b) + \", \" + this._roundA + \")\";\n  },\n  toPercentageRgb: function toPercentageRgb() {\n    return {\n      r: Math.round(bound01(this._r, 255) * 100) + \"%\",\n      g: Math.round(bound01(this._g, 255) * 100) + \"%\",\n      b: Math.round(bound01(this._b, 255) * 100) + \"%\",\n      a: this._a\n    };\n  },\n  toPercentageRgbString: function toPercentageRgbString() {\n    return this._a == 1 ? \"rgb(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%)\" : \"rgba(\" + Math.round(bound01(this._r, 255) * 100) + \"%, \" + Math.round(bound01(this._g, 255) * 100) + \"%, \" + Math.round(bound01(this._b, 255) * 100) + \"%, \" + this._roundA + \")\";\n  },\n  toName: function toName() {\n    if (this._a === 0) {\n      return \"transparent\";\n    }\n    if (this._a < 1) {\n      return false;\n    }\n    return hexNames[rgbToHex(this._r, this._g, this._b, true)] || false;\n  },\n  toFilter: function toFilter(secondColor) {\n    var hex8String = \"#\" + rgbaToArgbHex(this._r, this._g, this._b, this._a);\n    var secondHex8String = hex8String;\n    var gradientType = this._gradientType ? \"GradientType = 1, \" : \"\";\n    if (secondColor) {\n      var s = tinycolor(secondColor);\n      secondHex8String = \"#\" + rgbaToArgbHex(s._r, s._g, s._b, s._a);\n    }\n    return \"progid:DXImageTransform.Microsoft.gradient(\" + gradientType + \"startColorstr=\" + hex8String + \",endColorstr=\" + secondHex8String + \")\";\n  },\n  toString: function toString(format) {\n    var formatSet = !!format;\n    format = format || this._format;\n    var formattedString = false;\n    var hasAlpha = this._a < 1 && this._a >= 0;\n    var needsAlphaFormat = !formatSet && hasAlpha && (format === \"hex\" || format === \"hex6\" || format === \"hex3\" || format === \"hex4\" || format === \"hex8\" || format === \"name\");\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === \"name\" && this._a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === \"rgb\") {\n      formattedString = this.toRgbString();\n    }\n    if (format === \"prgb\") {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === \"hex\" || format === \"hex6\") {\n      formattedString = this.toHexString();\n    }\n    if (format === \"hex3\") {\n      formattedString = this.toHexString(true);\n    }\n    if (format === \"hex4\") {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === \"hex8\") {\n      formattedString = this.toHex8String();\n    }\n    if (format === \"name\") {\n      formattedString = this.toName();\n    }\n    if (format === \"hsl\") {\n      formattedString = this.toHslString();\n    }\n    if (format === \"hsv\") {\n      formattedString = this.toHsvString();\n    }\n    return formattedString || this.toHexString();\n  },\n  clone: function clone() {\n    return tinycolor(this.toString());\n  },\n  _applyModification: function _applyModification(fn, args) {\n    var color = fn.apply(null, [this].concat([].slice.call(args)));\n    this._r = color._r;\n    this._g = color._g;\n    this._b = color._b;\n    this.setAlpha(color._a);\n    return this;\n  },\n  lighten: function lighten() {\n    return this._applyModification(_lighten, arguments);\n  },\n  brighten: function brighten() {\n    return this._applyModification(_brighten, arguments);\n  },\n  darken: function darken() {\n    return this._applyModification(_darken, arguments);\n  },\n  desaturate: function desaturate() {\n    return this._applyModification(_desaturate, arguments);\n  },\n  saturate: function saturate() {\n    return this._applyModification(_saturate, arguments);\n  },\n  greyscale: function greyscale() {\n    return this._applyModification(_greyscale, arguments);\n  },\n  spin: function spin() {\n    return this._applyModification(_spin, arguments);\n  },\n  _applyCombination: function _applyCombination(fn, args) {\n    return fn.apply(null, [this].concat([].slice.call(args)));\n  },\n  analogous: function analogous() {\n    return this._applyCombination(_analogous, arguments);\n  },\n  complement: function complement() {\n    return this._applyCombination(_complement, arguments);\n  },\n  monochromatic: function monochromatic() {\n    return this._applyCombination(_monochromatic, arguments);\n  },\n  splitcomplement: function splitcomplement() {\n    return this._applyCombination(_splitcomplement, arguments);\n  },\n  // Disabled until https://github.com/bgrins/TinyColor/issues/254\n  // polyad: function (number) {\n  //   return this._applyCombination(polyad, [number]);\n  // },\n  triad: function triad() {\n    return this._applyCombination(polyad, [3]);\n  },\n  tetrad: function tetrad() {\n    return this._applyCombination(polyad, [4]);\n  }\n};\n\n// If input is an object, force 1 into \"1.0\" to handle ratios properly\n// String input requires \"1.0\" as input, so 1 will be treated as 1\ntinycolor.fromRatio = function (color, opts) {\n  if (_typeof(color) == \"object\") {\n    var newColor = {};\n    for (var i in color) {\n      if (color.hasOwnProperty(i)) {\n        if (i === \"a\") {\n          newColor[i] = color[i];\n        } else {\n          newColor[i] = convertToPercentage(color[i]);\n        }\n      }\n    }\n    color = newColor;\n  }\n  return tinycolor(color, opts);\n};\n\n// Given a string or object, convert that input to RGB\n// Possible string inputs:\n//\n//     \"red\"\n//     \"#f00\" or \"f00\"\n//     \"#ff0000\" or \"ff0000\"\n//     \"#ff000000\" or \"ff000000\"\n//     \"rgb 255 0 0\" or \"rgb (255, 0, 0)\"\n//     \"rgb 1.0 0 0\" or \"rgb (1, 0, 0)\"\n//     \"rgba (255, 0, 0, 1)\" or \"rgba 255, 0, 0, 1\"\n//     \"rgba (1.0, 0, 0, 1)\" or \"rgba 1.0, 0, 0, 1\"\n//     \"hsl(0, 100%, 50%)\" or \"hsl 0 100% 50%\"\n//     \"hsla(0, 100%, 50%, 1)\" or \"hsla 0 100% 50%, 1\"\n//     \"hsv(0, 100%, 100%)\" or \"hsv 0 100% 100%\"\n//\nfunction inputToRGB(color) {\n  var rgb = {\n    r: 0,\n    g: 0,\n    b: 0\n  };\n  var a = 1;\n  var s = null;\n  var v = null;\n  var l = null;\n  var ok = false;\n  var format = false;\n  if (typeof color == \"string\") {\n    color = stringInputToObject(color);\n  }\n  if (_typeof(color) == \"object\") {\n    if (isValidCSSUnit(color.r) && isValidCSSUnit(color.g) && isValidCSSUnit(color.b)) {\n      rgb = rgbToRgb(color.r, color.g, color.b);\n      ok = true;\n      format = String(color.r).substr(-1) === \"%\" ? \"prgb\" : \"rgb\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.v)) {\n      s = convertToPercentage(color.s);\n      v = convertToPercentage(color.v);\n      rgb = hsvToRgb(color.h, s, v);\n      ok = true;\n      format = \"hsv\";\n    } else if (isValidCSSUnit(color.h) && isValidCSSUnit(color.s) && isValidCSSUnit(color.l)) {\n      s = convertToPercentage(color.s);\n      l = convertToPercentage(color.l);\n      rgb = hslToRgb(color.h, s, l);\n      ok = true;\n      format = \"hsl\";\n    }\n    if (color.hasOwnProperty(\"a\")) {\n      a = color.a;\n    }\n  }\n  a = boundAlpha(a);\n  return {\n    ok: ok,\n    format: color.format || format,\n    r: Math.min(255, Math.max(rgb.r, 0)),\n    g: Math.min(255, Math.max(rgb.g, 0)),\n    b: Math.min(255, Math.max(rgb.b, 0)),\n    a: a\n  };\n}\n\n// Conversion Functions\n// --------------------\n\n// `rgbToHsl`, `rgbToHsv`, `hslToRgb`, `hsvToRgb` modified from:\n// <http://mjijackson.com/2008/02/rgb-to-hsl-and-rgb-to-hsv-color-model-conversion-algorithms-in-javascript>\n\n// `rgbToRgb`\n// Handle bounds / percentage checking to conform to CSS color spec\n// <http://www.w3.org/TR/css3-color/>\n// *Assumes:* r, g, b in [0, 255] or [0, 1]\n// *Returns:* { r, g, b } in [0, 255]\nfunction rgbToRgb(r, g, b) {\n  return {\n    r: bound01(r, 255) * 255,\n    g: bound01(g, 255) * 255,\n    b: bound01(b, 255) * 255\n  };\n}\n\n// `rgbToHsl`\n// Converts an RGB color value to HSL.\n// *Assumes:* r, g, and b are contained in [0, 255] or [0, 1]\n// *Returns:* { h, s, l } in [0,1]\nfunction rgbToHsl(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0; // achromatic\n  } else {\n    var d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    l: l\n  };\n}\n\n// `hslToRgb`\n// Converts an HSL color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and l are contained [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hslToRgb(h, s, l) {\n  var r, g, b;\n  h = bound01(h, 360);\n  s = bound01(s, 100);\n  l = bound01(l, 100);\n  function hue2rgb(p, q, t) {\n    if (t < 0) t += 1;\n    if (t > 1) t -= 1;\n    if (t < 1 / 6) return p + (q - p) * 6 * t;\n    if (t < 1 / 2) return q;\n    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;\n    return p;\n  }\n  if (s === 0) {\n    r = g = b = l; // achromatic\n  } else {\n    var q = l < 0.5 ? l * (1 + s) : l + s - l * s;\n    var p = 2 * l - q;\n    r = hue2rgb(p, q, h + 1 / 3);\n    g = hue2rgb(p, q, h);\n    b = hue2rgb(p, q, h - 1 / 3);\n  }\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHsv`\n// Converts an RGB color value to HSV\n// *Assumes:* r, g, and b are contained in the set [0, 255] or [0, 1]\n// *Returns:* { h, s, v } in [0,1]\nfunction rgbToHsv(r, g, b) {\n  r = bound01(r, 255);\n  g = bound01(g, 255);\n  b = bound01(b, 255);\n  var max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  var h,\n    s,\n    v = max;\n  var d = max - min;\n  s = max === 0 ? 0 : d / max;\n  if (max == min) {\n    h = 0; // achromatic\n  } else {\n    switch (max) {\n      case r:\n        h = (g - b) / d + (g < b ? 6 : 0);\n        break;\n      case g:\n        h = (b - r) / d + 2;\n        break;\n      case b:\n        h = (r - g) / d + 4;\n        break;\n    }\n    h /= 6;\n  }\n  return {\n    h: h,\n    s: s,\n    v: v\n  };\n}\n\n// `hsvToRgb`\n// Converts an HSV color value to RGB.\n// *Assumes:* h is contained in [0, 1] or [0, 360] and s and v are contained in [0, 1] or [0, 100]\n// *Returns:* { r, g, b } in the set [0, 255]\nfunction hsvToRgb(h, s, v) {\n  h = bound01(h, 360) * 6;\n  s = bound01(s, 100);\n  v = bound01(v, 100);\n  var i = Math.floor(h),\n    f = h - i,\n    p = v * (1 - s),\n    q = v * (1 - f * s),\n    t = v * (1 - (1 - f) * s),\n    mod = i % 6,\n    r = [v, q, p, p, t, v][mod],\n    g = [t, v, v, q, p, p][mod],\n    b = [p, p, t, v, v, q][mod];\n  return {\n    r: r * 255,\n    g: g * 255,\n    b: b * 255\n  };\n}\n\n// `rgbToHex`\n// Converts an RGB color to hex\n// Assumes r, g, and b are contained in the set [0, 255]\n// Returns a 3 or 6 character hex\nfunction rgbToHex(r, g, b, allow3Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n\n  // Return a 3 character hex if possible\n  if (allow3Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToHex`\n// Converts an RGBA color plus alpha transparency to hex\n// Assumes r, g, b are contained in the set [0, 255] and\n// a in [0, 1]. Returns a 4 or 8 character rgba hex\nfunction rgbaToHex(r, g, b, a, allow4Char) {\n  var hex = [pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16)), pad2(convertDecimalToHex(a))];\n\n  // Return a 4 character hex if possible\n  if (allow4Char && hex[0].charAt(0) == hex[0].charAt(1) && hex[1].charAt(0) == hex[1].charAt(1) && hex[2].charAt(0) == hex[2].charAt(1) && hex[3].charAt(0) == hex[3].charAt(1)) {\n    return hex[0].charAt(0) + hex[1].charAt(0) + hex[2].charAt(0) + hex[3].charAt(0);\n  }\n  return hex.join(\"\");\n}\n\n// `rgbaToArgbHex`\n// Converts an RGBA color to an ARGB Hex8 string\n// Rarely used, but required for \"toFilter()\"\nfunction rgbaToArgbHex(r, g, b, a) {\n  var hex = [pad2(convertDecimalToHex(a)), pad2(Math.round(r).toString(16)), pad2(Math.round(g).toString(16)), pad2(Math.round(b).toString(16))];\n  return hex.join(\"\");\n}\n\n// `equals`\n// Can be called with any tinycolor input\ntinycolor.equals = function (color1, color2) {\n  if (!color1 || !color2) return false;\n  return tinycolor(color1).toRgbString() == tinycolor(color2).toRgbString();\n};\ntinycolor.random = function () {\n  return tinycolor.fromRatio({\n    r: Math.random(),\n    g: Math.random(),\n    b: Math.random()\n  });\n};\n\n// Modification Functions\n// ----------------------\n// Thanks to less.js for some of the basics here\n// <https://github.com/cloudhead/less.js/blob/master/lib/less/functions.js>\n\nfunction _desaturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s -= amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _saturate(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.s += amount / 100;\n  hsl.s = clamp01(hsl.s);\n  return tinycolor(hsl);\n}\nfunction _greyscale(color) {\n  return tinycolor(color).desaturate(100);\n}\nfunction _lighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l += amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\nfunction _brighten(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var rgb = tinycolor(color).toRgb();\n  rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n  rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n  rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n  return tinycolor(rgb);\n}\nfunction _darken(color, amount) {\n  amount = amount === 0 ? 0 : amount || 10;\n  var hsl = tinycolor(color).toHsl();\n  hsl.l -= amount / 100;\n  hsl.l = clamp01(hsl.l);\n  return tinycolor(hsl);\n}\n\n// Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n// Values outside of this range will be wrapped into this range.\nfunction _spin(color, amount) {\n  var hsl = tinycolor(color).toHsl();\n  var hue = (hsl.h + amount) % 360;\n  hsl.h = hue < 0 ? 360 + hue : hue;\n  return tinycolor(hsl);\n}\n\n// Combination Functions\n// ---------------------\n// Thanks to jQuery xColor for some of the ideas behind these\n// <https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js>\n\nfunction _complement(color) {\n  var hsl = tinycolor(color).toHsl();\n  hsl.h = (hsl.h + 180) % 360;\n  return tinycolor(hsl);\n}\nfunction polyad(color, number) {\n  if (isNaN(number) || number <= 0) {\n    throw new Error(\"Argument to polyad must be a positive number\");\n  }\n  var hsl = tinycolor(color).toHsl();\n  var result = [tinycolor(color)];\n  var step = 360 / number;\n  for (var i = 1; i < number; i++) {\n    result.push(tinycolor({\n      h: (hsl.h + i * step) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }));\n  }\n  return result;\n}\nfunction _splitcomplement(color) {\n  var hsl = tinycolor(color).toHsl();\n  var h = hsl.h;\n  return [tinycolor(color), tinycolor({\n    h: (h + 72) % 360,\n    s: hsl.s,\n    l: hsl.l\n  }), tinycolor({\n    h: (h + 216) % 360,\n    s: hsl.s,\n    l: hsl.l\n  })];\n}\nfunction _analogous(color, results, slices) {\n  results = results || 6;\n  slices = slices || 30;\n  var hsl = tinycolor(color).toHsl();\n  var part = 360 / slices;\n  var ret = [tinycolor(color)];\n  for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n    hsl.h = (hsl.h + part) % 360;\n    ret.push(tinycolor(hsl));\n  }\n  return ret;\n}\nfunction _monochromatic(color, results) {\n  results = results || 6;\n  var hsv = tinycolor(color).toHsv();\n  var h = hsv.h,\n    s = hsv.s,\n    v = hsv.v;\n  var ret = [];\n  var modification = 1 / results;\n  while (results--) {\n    ret.push(tinycolor({\n      h: h,\n      s: s,\n      v: v\n    }));\n    v = (v + modification) % 1;\n  }\n  return ret;\n}\n\n// Utility Functions\n// ---------------------\n\ntinycolor.mix = function (color1, color2, amount) {\n  amount = amount === 0 ? 0 : amount || 50;\n  var rgb1 = tinycolor(color1).toRgb();\n  var rgb2 = tinycolor(color2).toRgb();\n  var p = amount / 100;\n  var rgba = {\n    r: (rgb2.r - rgb1.r) * p + rgb1.r,\n    g: (rgb2.g - rgb1.g) * p + rgb1.g,\n    b: (rgb2.b - rgb1.b) * p + rgb1.b,\n    a: (rgb2.a - rgb1.a) * p + rgb1.a\n  };\n  return tinycolor(rgba);\n};\n\n// Readability Functions\n// ---------------------\n// <http://www.w3.org/TR/2008/REC-WCAG20-20081211/#contrast-ratiodef (WCAG Version 2)\n\n// `contrast`\n// Analyze the 2 colors and returns the color contrast defined by (WCAG Version 2)\ntinycolor.readability = function (color1, color2) {\n  var c1 = tinycolor(color1);\n  var c2 = tinycolor(color2);\n  return (Math.max(c1.getLuminance(), c2.getLuminance()) + 0.05) / (Math.min(c1.getLuminance(), c2.getLuminance()) + 0.05);\n};\n\n// `isReadable`\n// Ensure that foreground and background color combinations meet WCAG2 guidelines.\n// The third argument is an optional Object.\n//      the 'level' property states 'AA' or 'AAA' - if missing or invalid, it defaults to 'AA';\n//      the 'size' property states 'large' or 'small' - if missing or invalid, it defaults to 'small'.\n// If the entire object is absent, isReadable defaults to {level:\"AA\",size:\"small\"}.\n\n// *Example*\n//    tinycolor.isReadable(\"#000\", \"#111\") => false\n//    tinycolor.isReadable(\"#000\", \"#111\",{level:\"AA\",size:\"large\"}) => false\ntinycolor.isReadable = function (color1, color2, wcag2) {\n  var readability = tinycolor.readability(color1, color2);\n  var wcag2Parms, out;\n  out = false;\n  wcag2Parms = validateWCAG2Parms(wcag2);\n  switch (wcag2Parms.level + wcag2Parms.size) {\n    case \"AAsmall\":\n    case \"AAAlarge\":\n      out = readability >= 4.5;\n      break;\n    case \"AAlarge\":\n      out = readability >= 3;\n      break;\n    case \"AAAsmall\":\n      out = readability >= 7;\n      break;\n  }\n  return out;\n};\n\n// `mostReadable`\n// Given a base color and a list of possible foreground or background\n// colors for that base, returns the most readable color.\n// Optionally returns Black or White if the most readable color is unreadable.\n// *Example*\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:false}).toHexString(); // \"#112255\"\n//    tinycolor.mostReadable(tinycolor.mostReadable(\"#123\", [\"#124\", \"#125\"],{includeFallbackColors:true}).toHexString();  // \"#ffffff\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"large\"}).toHexString(); // \"#faf3f3\"\n//    tinycolor.mostReadable(\"#a8015a\", [\"#faf3f3\"],{includeFallbackColors:true,level:\"AAA\",size:\"small\"}).toHexString(); // \"#ffffff\"\ntinycolor.mostReadable = function (baseColor, colorList, args) {\n  var bestColor = null;\n  var bestScore = 0;\n  var readability;\n  var includeFallbackColors, level, size;\n  args = args || {};\n  includeFallbackColors = args.includeFallbackColors;\n  level = args.level;\n  size = args.size;\n  for (var i = 0; i < colorList.length; i++) {\n    readability = tinycolor.readability(baseColor, colorList[i]);\n    if (readability > bestScore) {\n      bestScore = readability;\n      bestColor = tinycolor(colorList[i]);\n    }\n  }\n  if (tinycolor.isReadable(baseColor, bestColor, {\n    level: level,\n    size: size\n  }) || !includeFallbackColors) {\n    return bestColor;\n  } else {\n    args.includeFallbackColors = false;\n    return tinycolor.mostReadable(baseColor, [\"#fff\", \"#000\"], args);\n  }\n};\n\n// Big List of Colors\n// ------------------\n// <https://www.w3.org/TR/css-color-4/#named-colors>\nvar names = tinycolor.names = {\n  aliceblue: \"f0f8ff\",\n  antiquewhite: \"faebd7\",\n  aqua: \"0ff\",\n  aquamarine: \"7fffd4\",\n  azure: \"f0ffff\",\n  beige: \"f5f5dc\",\n  bisque: \"ffe4c4\",\n  black: \"000\",\n  blanchedalmond: \"ffebcd\",\n  blue: \"00f\",\n  blueviolet: \"8a2be2\",\n  brown: \"a52a2a\",\n  burlywood: \"deb887\",\n  burntsienna: \"ea7e5d\",\n  cadetblue: \"5f9ea0\",\n  chartreuse: \"7fff00\",\n  chocolate: \"d2691e\",\n  coral: \"ff7f50\",\n  cornflowerblue: \"6495ed\",\n  cornsilk: \"fff8dc\",\n  crimson: \"dc143c\",\n  cyan: \"0ff\",\n  darkblue: \"00008b\",\n  darkcyan: \"008b8b\",\n  darkgoldenrod: \"b8860b\",\n  darkgray: \"a9a9a9\",\n  darkgreen: \"006400\",\n  darkgrey: \"a9a9a9\",\n  darkkhaki: \"bdb76b\",\n  darkmagenta: \"8b008b\",\n  darkolivegreen: \"556b2f\",\n  darkorange: \"ff8c00\",\n  darkorchid: \"9932cc\",\n  darkred: \"8b0000\",\n  darksalmon: \"e9967a\",\n  darkseagreen: \"8fbc8f\",\n  darkslateblue: \"483d8b\",\n  darkslategray: \"2f4f4f\",\n  darkslategrey: \"2f4f4f\",\n  darkturquoise: \"00ced1\",\n  darkviolet: \"9400d3\",\n  deeppink: \"ff1493\",\n  deepskyblue: \"00bfff\",\n  dimgray: \"696969\",\n  dimgrey: \"696969\",\n  dodgerblue: \"1e90ff\",\n  firebrick: \"b22222\",\n  floralwhite: \"fffaf0\",\n  forestgreen: \"228b22\",\n  fuchsia: \"f0f\",\n  gainsboro: \"dcdcdc\",\n  ghostwhite: \"f8f8ff\",\n  gold: \"ffd700\",\n  goldenrod: \"daa520\",\n  gray: \"808080\",\n  green: \"008000\",\n  greenyellow: \"adff2f\",\n  grey: \"808080\",\n  honeydew: \"f0fff0\",\n  hotpink: \"ff69b4\",\n  indianred: \"cd5c5c\",\n  indigo: \"4b0082\",\n  ivory: \"fffff0\",\n  khaki: \"f0e68c\",\n  lavender: \"e6e6fa\",\n  lavenderblush: \"fff0f5\",\n  lawngreen: \"7cfc00\",\n  lemonchiffon: \"fffacd\",\n  lightblue: \"add8e6\",\n  lightcoral: \"f08080\",\n  lightcyan: \"e0ffff\",\n  lightgoldenrodyellow: \"fafad2\",\n  lightgray: \"d3d3d3\",\n  lightgreen: \"90ee90\",\n  lightgrey: \"d3d3d3\",\n  lightpink: \"ffb6c1\",\n  lightsalmon: \"ffa07a\",\n  lightseagreen: \"20b2aa\",\n  lightskyblue: \"87cefa\",\n  lightslategray: \"789\",\n  lightslategrey: \"789\",\n  lightsteelblue: \"b0c4de\",\n  lightyellow: \"ffffe0\",\n  lime: \"0f0\",\n  limegreen: \"32cd32\",\n  linen: \"faf0e6\",\n  magenta: \"f0f\",\n  maroon: \"800000\",\n  mediumaquamarine: \"66cdaa\",\n  mediumblue: \"0000cd\",\n  mediumorchid: \"ba55d3\",\n  mediumpurple: \"9370db\",\n  mediumseagreen: \"3cb371\",\n  mediumslateblue: \"7b68ee\",\n  mediumspringgreen: \"00fa9a\",\n  mediumturquoise: \"48d1cc\",\n  mediumvioletred: \"c71585\",\n  midnightblue: \"191970\",\n  mintcream: \"f5fffa\",\n  mistyrose: \"ffe4e1\",\n  moccasin: \"ffe4b5\",\n  navajowhite: \"ffdead\",\n  navy: \"000080\",\n  oldlace: \"fdf5e6\",\n  olive: \"808000\",\n  olivedrab: \"6b8e23\",\n  orange: \"ffa500\",\n  orangered: \"ff4500\",\n  orchid: \"da70d6\",\n  palegoldenrod: \"eee8aa\",\n  palegreen: \"98fb98\",\n  paleturquoise: \"afeeee\",\n  palevioletred: \"db7093\",\n  papayawhip: \"ffefd5\",\n  peachpuff: \"ffdab9\",\n  peru: \"cd853f\",\n  pink: \"ffc0cb\",\n  plum: \"dda0dd\",\n  powderblue: \"b0e0e6\",\n  purple: \"800080\",\n  rebeccapurple: \"663399\",\n  red: \"f00\",\n  rosybrown: \"bc8f8f\",\n  royalblue: \"4169e1\",\n  saddlebrown: \"8b4513\",\n  salmon: \"fa8072\",\n  sandybrown: \"f4a460\",\n  seagreen: \"2e8b57\",\n  seashell: \"fff5ee\",\n  sienna: \"a0522d\",\n  silver: \"c0c0c0\",\n  skyblue: \"87ceeb\",\n  slateblue: \"6a5acd\",\n  slategray: \"708090\",\n  slategrey: \"708090\",\n  snow: \"fffafa\",\n  springgreen: \"00ff7f\",\n  steelblue: \"4682b4\",\n  tan: \"d2b48c\",\n  teal: \"008080\",\n  thistle: \"d8bfd8\",\n  tomato: \"ff6347\",\n  turquoise: \"40e0d0\",\n  violet: \"ee82ee\",\n  wheat: \"f5deb3\",\n  white: \"fff\",\n  whitesmoke: \"f5f5f5\",\n  yellow: \"ff0\",\n  yellowgreen: \"9acd32\"\n};\n\n// Make it easy to access colors via `hexNames[hex]`\nvar hexNames = tinycolor.hexNames = flip(names);\n\n// Utilities\n// ---------\n\n// `{ 'name1': 'val1' }` becomes `{ 'val1': 'name1' }`\nfunction flip(o) {\n  var flipped = {};\n  for (var i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\n// Return a valid alpha value [0,1] with all invalid values being set to 1\nfunction boundAlpha(a) {\n  a = parseFloat(a);\n  if (isNaN(a) || a < 0 || a > 1) {\n    a = 1;\n  }\n  return a;\n}\n\n// Take input from [0, n] and return it as [0, 1]\nfunction bound01(n, max) {\n  if (isOnePointZero(n)) n = \"100%\";\n  var processPercent = isPercentage(n);\n  n = Math.min(max, Math.max(0, parseFloat(n)));\n\n  // Automatically convert percentage into number\n  if (processPercent) {\n    n = parseInt(n * max, 10) / 100;\n  }\n\n  // Handle floating point rounding errors\n  if (Math.abs(n - max) < 0.000001) {\n    return 1;\n  }\n\n  // Convert into [0, 1] range if it isn't already\n  return n % max / parseFloat(max);\n}\n\n// Force a number between 0 and 1\nfunction clamp01(val) {\n  return Math.min(1, Math.max(0, val));\n}\n\n// Parse a base-16 hex value into a base-10 integer\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\n// Need to handle 1.0 as 100%, since once it is a number, there is no difference between it and 1\n// <http://stackoverflow.com/questions/7422072/javascript-how-to-detect-number-as-a-decimal-including-1-0>\nfunction isOnePointZero(n) {\n  return typeof n == \"string\" && n.indexOf(\".\") != -1 && parseFloat(n) === 1;\n}\n\n// Check to see if string passed in is a percentage\nfunction isPercentage(n) {\n  return typeof n === \"string\" && n.indexOf(\"%\") != -1;\n}\n\n// Force a hex value to have 2 characters\nfunction pad2(c) {\n  return c.length == 1 ? \"0\" + c : \"\" + c;\n}\n\n// Replace a decimal with it's percentage value\nfunction convertToPercentage(n) {\n  if (n <= 1) {\n    n = n * 100 + \"%\";\n  }\n  return n;\n}\n\n// Converts a decimal to a hex value\nfunction convertDecimalToHex(d) {\n  return Math.round(parseFloat(d) * 255).toString(16);\n}\n// Converts a hex value to a decimal\nfunction convertHexToDecimal(h) {\n  return parseIntFromHex(h) / 255;\n}\nvar matchers = function () {\n  // <http://www.w3.org/TR/css3-values/#integers>\n  var CSS_INTEGER = \"[-\\\\+]?\\\\d+%?\";\n\n  // <http://www.w3.org/TR/css3-values/#number-value>\n  var CSS_NUMBER = \"[-\\\\+]?\\\\d*\\\\.\\\\d+%?\";\n\n  // Allow positive/negative integer/number.  Don't capture the either/or, just the entire outcome.\n  var CSS_UNIT = \"(?:\" + CSS_NUMBER + \")|(?:\" + CSS_INTEGER + \")\";\n\n  // Actual matching.\n  // Parentheses and commas are optional, but not required.\n  // Whitespace can take the place of commas or opening paren\n  var PERMISSIVE_MATCH3 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  var PERMISSIVE_MATCH4 = \"[\\\\s|\\\\(]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")[,|\\\\s]+(\" + CSS_UNIT + \")\\\\s*\\\\)?\";\n  return {\n    CSS_UNIT: new RegExp(CSS_UNIT),\n    rgb: new RegExp(\"rgb\" + PERMISSIVE_MATCH3),\n    rgba: new RegExp(\"rgba\" + PERMISSIVE_MATCH4),\n    hsl: new RegExp(\"hsl\" + PERMISSIVE_MATCH3),\n    hsla: new RegExp(\"hsla\" + PERMISSIVE_MATCH4),\n    hsv: new RegExp(\"hsv\" + PERMISSIVE_MATCH3),\n    hsva: new RegExp(\"hsva\" + PERMISSIVE_MATCH4),\n    hex3: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex6: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,\n    hex4: /^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,\n    hex8: /^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/\n  };\n}();\n\n// `isValidCSSUnit`\n// Take in a single string / number and check to see if it looks like a CSS unit\n// (see `matchers` above for definition).\nfunction isValidCSSUnit(color) {\n  return !!matchers.CSS_UNIT.exec(color);\n}\n\n// `stringInputToObject`\n// Permissive string parsing.  Take in a number of formats, and output an object\n// based on detected format.  Returns `{ r, g, b }` or `{ h, s, l }` or `{ h, s, v}`\nfunction stringInputToObject(color) {\n  color = color.replace(trimLeft, \"\").replace(trimRight, \"\").toLowerCase();\n  var named = false;\n  if (names[color]) {\n    color = names[color];\n    named = true;\n  } else if (color == \"transparent\") {\n    return {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 0,\n      format: \"name\"\n    };\n  }\n\n  // Try to match string input using regular expressions.\n  // Keep most of the number bounding out of this function - don't worry about [0,1] or [0,100] or [0,360]\n  // Just return an object and let the conversion functions handle that.\n  // This way the result will be the same whether the tinycolor is initialized with string or object.\n  var match;\n  if (match = matchers.rgb.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3]\n    };\n  }\n  if (match = matchers.rgba.exec(color)) {\n    return {\n      r: match[1],\n      g: match[2],\n      b: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsl.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3]\n    };\n  }\n  if (match = matchers.hsla.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      l: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hsv.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3]\n    };\n  }\n  if (match = matchers.hsva.exec(color)) {\n    return {\n      h: match[1],\n      s: match[2],\n      v: match[3],\n      a: match[4]\n    };\n  }\n  if (match = matchers.hex8.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      a: convertHexToDecimal(match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex6.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1]),\n      g: parseIntFromHex(match[2]),\n      b: parseIntFromHex(match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  if (match = matchers.hex4.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      a: convertHexToDecimal(match[4] + \"\" + match[4]),\n      format: named ? \"name\" : \"hex8\"\n    };\n  }\n  if (match = matchers.hex3.exec(color)) {\n    return {\n      r: parseIntFromHex(match[1] + \"\" + match[1]),\n      g: parseIntFromHex(match[2] + \"\" + match[2]),\n      b: parseIntFromHex(match[3] + \"\" + match[3]),\n      format: named ? \"name\" : \"hex\"\n    };\n  }\n  return false;\n}\nfunction validateWCAG2Parms(parms) {\n  // return valid WCAG2 parms for isReadable.\n  // If input parms are invalid, return {\"level\":\"AA\", \"size\":\"small\"}\n  var level, size;\n  parms = parms || {\n    level: \"AA\",\n    size: \"small\"\n  };\n  level = (parms.level || \"AA\").toUpperCase();\n  size = (parms.size || \"small\").toLowerCase();\n  if (level !== \"AA\" && level !== \"AAA\") {\n    level = \"AA\";\n  }\n  if (size !== \"small\" && size !== \"large\") {\n    size = \"small\";\n  }\n  return {\n    level: level,\n    size: size\n  };\n}\n\nexport { tinycolor as default };\n"], "mappings": ";;;AACA,SAAS,QAAQ,KAAK;AACpB;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,MAAK;AAClG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,MAAK;AACjB,WAAOA,QAAO,cAAc,OAAO,UAAUA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,EAC1H,GAAG,QAAQ,GAAG;AAChB;AAKA,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,SAAS,UAAU,OAAO,MAAM;AAC9B,UAAQ,QAAQ,QAAQ;AACxB,SAAO,QAAQ,CAAC;AAGhB,MAAI,iBAAiB,WAAW;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,EAAE,gBAAgB,YAAY;AAChC,WAAO,IAAI,UAAU,OAAO,IAAI;AAAA,EAClC;AACA,MAAI,MAAM,WAAW,KAAK;AAC1B,OAAK,iBAAiB,OAAO,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,UAAU,KAAK,MAAM,MAAM,KAAK,EAAE,IAAI,KAAK,KAAK,UAAU,KAAK,UAAU,IAAI;AACnL,OAAK,gBAAgB,KAAK;AAM1B,MAAI,KAAK,KAAK,EAAG,MAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC7C,MAAI,KAAK,KAAK,EAAG,MAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC7C,MAAI,KAAK,KAAK,EAAG,MAAK,KAAK,KAAK,MAAM,KAAK,EAAE;AAC7C,OAAK,MAAM,IAAI;AACjB;AACA,UAAU,YAAY;AAAA,EACpB,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,cAAc,IAAI;AAAA,EAChC;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,WAAO,CAAC,KAAK,OAAO;AAAA,EACtB;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,SAAS,mBAAmB;AAC5C,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO,KAAK;AAAA,EACd;AAAA,EACA,eAAe,SAAS,gBAAgB;AAEtC,QAAI,MAAM,KAAK,MAAM;AACrB,YAAQ,IAAI,IAAI,MAAM,IAAI,IAAI,MAAM,IAAI,IAAI,OAAO;AAAA,EACrD;AAAA,EACA,cAAc,SAAS,eAAe;AAEpC,QAAI,MAAM,KAAK,MAAM;AACrB,QAAI,OAAO,OAAO,OAAO,GAAG,GAAG;AAC/B,YAAQ,IAAI,IAAI;AAChB,YAAQ,IAAI,IAAI;AAChB,YAAQ,IAAI,IAAI;AAChB,QAAI,SAAS,QAAS,KAAI,QAAQ;AAAA,QAAW,KAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACtF,QAAI,SAAS,QAAS,KAAI,QAAQ;AAAA,QAAW,KAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACtF,QAAI,SAAS,QAAS,KAAI,QAAQ;AAAA,QAAW,KAAI,KAAK,KAAK,QAAQ,SAAS,OAAO,GAAG;AACtF,WAAO,SAAS,IAAI,SAAS,IAAI,SAAS;AAAA,EAC5C;AAAA,EACA,UAAU,SAAS,SAAS,OAAO;AACjC,SAAK,KAAK,WAAW,KAAK;AAC1B,SAAK,UAAU,KAAK,MAAM,MAAM,KAAK,EAAE,IAAI;AAC3C,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,WAAO;AAAA,MACL,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,MACP,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,QAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC5B,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC1B,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC5B,WAAO,KAAK,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,UAAU,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,KAAK,UAAU;AAAA,EAC/H;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,WAAO;AAAA,MACL,GAAG,IAAI,IAAI;AAAA,MACX,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,MACP,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,QAAI,MAAM,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AAC5C,QAAI,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC5B,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG,GAC1B,IAAI,KAAK,MAAM,IAAI,IAAI,GAAG;AAC5B,WAAO,KAAK,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,UAAU,IAAI,OAAO,IAAI,QAAQ,IAAI,QAAQ,KAAK,UAAU;AAAA,EAC/H;AAAA,EACA,OAAO,SAAS,MAAM,YAAY;AAChC,WAAO,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,UAAU;AAAA,EACvD;AAAA,EACA,aAAa,SAAS,YAAY,YAAY;AAC5C,WAAO,MAAM,KAAK,MAAM,UAAU;AAAA,EACpC;AAAA,EACA,QAAQ,SAAS,OAAO,YAAY;AAClC,WAAO,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,UAAU;AAAA,EACjE;AAAA,EACA,cAAc,SAAS,aAAa,YAAY;AAC9C,WAAO,MAAM,KAAK,OAAO,UAAU;AAAA,EACrC;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,WAAO;AAAA,MACL,GAAG,KAAK,MAAM,KAAK,EAAE;AAAA,MACrB,GAAG,KAAK,MAAM,KAAK,EAAE;AAAA,MACrB,GAAG,KAAK,MAAM,KAAK,EAAE;AAAA,MACrB,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,aAAa,SAAS,cAAc;AAClC,WAAO,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,MAAM,UAAU,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,OAAO,KAAK,UAAU;AAAA,EACvO;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,WAAO;AAAA,MACL,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MAC7C,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MAC7C,GAAG,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI;AAAA,MAC7C,GAAG,KAAK;AAAA,IACV;AAAA,EACF;AAAA,EACA,uBAAuB,SAAS,wBAAwB;AACtD,WAAO,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,OAAO,UAAU,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,MAAM,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,QAAQ,KAAK,UAAU;AAAA,EACrW;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,QAAI,KAAK,OAAO,GAAG;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,KAAK,GAAG;AACf,aAAO;AAAA,IACT;AACA,WAAO,SAAS,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK;AAAA,EAChE;AAAA,EACA,UAAU,SAAS,SAAS,aAAa;AACvC,QAAI,aAAa,MAAM,cAAc,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,EAAE;AACvE,QAAI,mBAAmB;AACvB,QAAI,eAAe,KAAK,gBAAgB,uBAAuB;AAC/D,QAAI,aAAa;AACf,UAAI,IAAI,UAAU,WAAW;AAC7B,yBAAmB,MAAM,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;AAAA,IAC/D;AACA,WAAO,gDAAgD,eAAe,mBAAmB,aAAa,kBAAkB,mBAAmB;AAAA,EAC7I;AAAA,EACA,UAAU,SAAS,SAAS,QAAQ;AAClC,QAAI,YAAY,CAAC,CAAC;AAClB,aAAS,UAAU,KAAK;AACxB,QAAI,kBAAkB;AACtB,QAAI,WAAW,KAAK,KAAK,KAAK,KAAK,MAAM;AACzC,QAAI,mBAAmB,CAAC,aAAa,aAAa,WAAW,SAAS,WAAW,UAAU,WAAW,UAAU,WAAW,UAAU,WAAW,UAAU,WAAW;AACrK,QAAI,kBAAkB;AAGpB,UAAI,WAAW,UAAU,KAAK,OAAO,GAAG;AACtC,eAAO,KAAK,OAAO;AAAA,MACrB;AACA,aAAO,KAAK,YAAY;AAAA,IAC1B;AACA,QAAI,WAAW,OAAO;AACpB,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,sBAAsB;AAAA,IAC/C;AACA,QAAI,WAAW,SAAS,WAAW,QAAQ;AACzC,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,YAAY,IAAI;AAAA,IACzC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,aAAa,IAAI;AAAA,IAC1C;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,aAAa;AAAA,IACtC;AACA,QAAI,WAAW,QAAQ;AACrB,wBAAkB,KAAK,OAAO;AAAA,IAChC;AACA,QAAI,WAAW,OAAO;AACpB,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,QAAI,WAAW,OAAO;AACpB,wBAAkB,KAAK,YAAY;AAAA,IACrC;AACA,WAAO,mBAAmB,KAAK,YAAY;AAAA,EAC7C;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,WAAO,UAAU,KAAK,SAAS,CAAC;AAAA,EAClC;AAAA,EACA,oBAAoB,SAAS,mBAAmB,IAAI,MAAM;AACxD,QAAI,QAAQ,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;AAC7D,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAChB,SAAK,SAAS,MAAM,EAAE;AACtB,WAAO;AAAA,EACT;AAAA,EACA,SAAS,SAAS,UAAU;AAC1B,WAAO,KAAK,mBAAmB,UAAU,SAAS;AAAA,EACpD;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO,KAAK,mBAAmB,WAAW,SAAS;AAAA,EACrD;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,mBAAmB,SAAS,SAAS;AAAA,EACnD;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,WAAO,KAAK,mBAAmB,aAAa,SAAS;AAAA,EACvD;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO,KAAK,mBAAmB,WAAW,SAAS;AAAA,EACrD;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,WAAO,KAAK,mBAAmB,YAAY,SAAS;AAAA,EACtD;AAAA,EACA,MAAM,SAAS,OAAO;AACpB,WAAO,KAAK,mBAAmB,OAAO,SAAS;AAAA,EACjD;AAAA,EACA,mBAAmB,SAAS,kBAAkB,IAAI,MAAM;AACtD,WAAO,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,MAAM,KAAK,IAAI,CAAC,CAAC;AAAA,EAC1D;AAAA,EACA,WAAW,SAAS,YAAY;AAC9B,WAAO,KAAK,kBAAkB,YAAY,SAAS;AAAA,EACrD;AAAA,EACA,YAAY,SAAS,aAAa;AAChC,WAAO,KAAK,kBAAkB,aAAa,SAAS;AAAA,EACtD;AAAA,EACA,eAAe,SAAS,gBAAgB;AACtC,WAAO,KAAK,kBAAkB,gBAAgB,SAAS;AAAA,EACzD;AAAA,EACA,iBAAiB,SAAS,kBAAkB;AAC1C,WAAO,KAAK,kBAAkB,kBAAkB,SAAS;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,QAAQ;AACtB,WAAO,KAAK,kBAAkB,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3C;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO,KAAK,kBAAkB,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3C;AACF;AAIA,UAAU,YAAY,SAAU,OAAO,MAAM;AAC3C,MAAI,QAAQ,KAAK,KAAK,UAAU;AAC9B,QAAI,WAAW,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,UAAI,MAAM,eAAe,CAAC,GAAG;AAC3B,YAAI,MAAM,KAAK;AACb,mBAAS,CAAC,IAAI,MAAM,CAAC;AAAA,QACvB,OAAO;AACL,mBAAS,CAAC,IAAI,oBAAoB,MAAM,CAAC,CAAC;AAAA,QAC5C;AAAA,MACF;AAAA,IACF;AACA,YAAQ;AAAA,EACV;AACA,SAAO,UAAU,OAAO,IAAI;AAC9B;AAiBA,SAAS,WAAW,OAAO;AACzB,MAAI,MAAM;AAAA,IACR,GAAG;AAAA,IACH,GAAG;AAAA,IACH,GAAG;AAAA,EACL;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,KAAK;AACT,MAAI,SAAS;AACb,MAAI,OAAO,SAAS,UAAU;AAC5B,YAAQ,oBAAoB,KAAK;AAAA,EACnC;AACA,MAAI,QAAQ,KAAK,KAAK,UAAU;AAC9B,QAAI,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACjF,YAAM,SAAS,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC;AACxC,WAAK;AACL,eAAS,OAAO,MAAM,CAAC,EAAE,OAAO,EAAE,MAAM,MAAM,SAAS;AAAA,IACzD,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX,WAAW,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,KAAK,eAAe,MAAM,CAAC,GAAG;AACxF,UAAI,oBAAoB,MAAM,CAAC;AAC/B,UAAI,oBAAoB,MAAM,CAAC;AAC/B,YAAM,SAAS,MAAM,GAAG,GAAG,CAAC;AAC5B,WAAK;AACL,eAAS;AAAA,IACX;AACA,QAAI,MAAM,eAAe,GAAG,GAAG;AAC7B,UAAI,MAAM;AAAA,IACZ;AAAA,EACF;AACA,MAAI,WAAW,CAAC;AAChB,SAAO;AAAA,IACL;AAAA,IACA,QAAQ,MAAM,UAAU;AAAA,IACxB,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC,GAAG,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,IACnC;AAAA,EACF;AACF;AAaA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,SAAO;AAAA,IACL,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,IACrB,GAAG,QAAQ,GAAG,GAAG,IAAI;AAAA,EACvB;AACF;AAMA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACxB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACxB,MAAI,GACF,GACA,KAAK,MAAM,OAAO;AACpB,MAAI,OAAO,KAAK;AACd,QAAI,IAAI;AAAA,EACV,OAAO;AACL,QAAI,IAAI,MAAM;AACd,QAAI,IAAI,MAAM,KAAK,IAAI,MAAM,OAAO,KAAK,MAAM;AAC/C,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,IACJ;AACA,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,GAAG,GAAG;AACV,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,WAAS,QAAQC,IAAGC,IAAG,GAAG;AACxB,QAAI,IAAI,EAAG,MAAK;AAChB,QAAI,IAAI,EAAG,MAAK;AAChB,QAAI,IAAI,IAAI,EAAG,QAAOD,MAAKC,KAAID,MAAK,IAAI;AACxC,QAAI,IAAI,IAAI,EAAG,QAAOC;AACtB,QAAI,IAAI,IAAI,EAAG,QAAOD,MAAKC,KAAID,OAAM,IAAI,IAAI,KAAK;AAClD,WAAOA;AAAA,EACT;AACA,MAAI,MAAM,GAAG;AACX,QAAI,IAAI,IAAI;AAAA,EACd,OAAO;AACL,QAAI,IAAI,IAAI,MAAM,KAAK,IAAI,KAAK,IAAI,IAAI,IAAI;AAC5C,QAAI,IAAI,IAAI,IAAI;AAChB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAC3B,QAAI,QAAQ,GAAG,GAAG,CAAC;AACnB,QAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC;AAAA,EAC7B;AACA,SAAO;AAAA,IACL,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAMA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,GACxB,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AACxB,MAAI,GACF,GACA,IAAI;AACN,MAAI,IAAI,MAAM;AACd,MAAI,QAAQ,IAAI,IAAI,IAAI;AACxB,MAAI,OAAO,KAAK;AACd,QAAI;AAAA,EACN,OAAO;AACL,YAAQ,KAAK;AAAA,MACX,KAAK;AACH,aAAK,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;AAC/B;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACF,KAAK;AACH,aAAK,IAAI,KAAK,IAAI;AAClB;AAAA,IACJ;AACA,SAAK;AAAA,EACP;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AAMA,SAAS,SAAS,GAAG,GAAG,GAAG;AACzB,MAAI,QAAQ,GAAG,GAAG,IAAI;AACtB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,QAAQ,GAAG,GAAG;AAClB,MAAI,IAAI,KAAK,MAAM,CAAC,GAClB,IAAI,IAAI,GACR,IAAI,KAAK,IAAI,IACb,IAAI,KAAK,IAAI,IAAI,IACjB,IAAI,KAAK,KAAK,IAAI,KAAK,IACvB,MAAM,IAAI,GACV,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAC1B,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG,GAC1B,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,EAAE,GAAG;AAC5B,SAAO;AAAA,IACL,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT;AACF;AAMA,SAAS,SAAS,GAAG,GAAG,GAAG,YAAY;AACrC,MAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAG/G,MAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AACtI,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EAC9D;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAMA,SAAS,UAAU,GAAG,GAAG,GAAG,GAAG,YAAY;AACzC,MAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,oBAAoB,CAAC,CAAC,CAAC;AAG7I,MAAI,cAAc,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,GAAG;AAC9K,WAAO,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,EAAE,OAAO,CAAC;AAAA,EACjF;AACA,SAAO,IAAI,KAAK,EAAE;AACpB;AAKA,SAAS,cAAc,GAAG,GAAG,GAAG,GAAG;AACjC,MAAI,MAAM,CAAC,KAAK,oBAAoB,CAAC,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC;AAC7I,SAAO,IAAI,KAAK,EAAE;AACpB;AAIA,UAAU,SAAS,SAAU,QAAQ,QAAQ;AAC3C,MAAI,CAAC,UAAU,CAAC,OAAQ,QAAO;AAC/B,SAAO,UAAU,MAAM,EAAE,YAAY,KAAK,UAAU,MAAM,EAAE,YAAY;AAC1E;AACA,UAAU,SAAS,WAAY;AAC7B,SAAO,UAAU,UAAU;AAAA,IACzB,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,IACf,GAAG,KAAK,OAAO;AAAA,EACjB,CAAC;AACH;AAOA,SAAS,YAAY,OAAO,QAAQ;AAClC,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,UAAU,OAAO,QAAQ;AAChC,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,WAAW,OAAO;AACzB,SAAO,UAAU,KAAK,EAAE,WAAW,GAAG;AACxC;AACA,SAAS,SAAS,OAAO,QAAQ;AAC/B,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,UAAU,OAAO,QAAQ;AAChC,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,MAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,MAAI,IAAI,KAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,IAAI,KAAK,MAAM,MAAM,EAAE,SAAS,IAAI,CAAC,CAAC;AAC5E,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,SAAS;AAClB,MAAI,IAAI,QAAQ,IAAI,CAAC;AACrB,SAAO,UAAU,GAAG;AACtB;AAIA,SAAS,MAAM,OAAO,QAAQ;AAC5B,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,OAAO,IAAI,IAAI,UAAU;AAC7B,MAAI,IAAI,MAAM,IAAI,MAAM,MAAM;AAC9B,SAAO,UAAU,GAAG;AACtB;AAOA,SAAS,YAAY,OAAO;AAC1B,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,KAAK,IAAI,IAAI,OAAO;AACxB,SAAO,UAAU,GAAG;AACtB;AACA,SAAS,OAAO,OAAO,QAAQ;AAC7B,MAAI,MAAM,MAAM,KAAK,UAAU,GAAG;AAChC,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAChE;AACA,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,SAAS,CAAC,UAAU,KAAK,CAAC;AAC9B,MAAI,OAAO,MAAM;AACjB,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,WAAO,KAAK,UAAU;AAAA,MACpB,IAAI,IAAI,IAAI,IAAI,QAAQ;AAAA,MACxB,GAAG,IAAI;AAAA,MACP,GAAG,IAAI;AAAA,IACT,CAAC,CAAC;AAAA,EACJ;AACA,SAAO;AACT;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,IAAI,IAAI;AACZ,SAAO,CAAC,UAAU,KAAK,GAAG,UAAU;AAAA,IAClC,IAAI,IAAI,MAAM;AAAA,IACd,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT,CAAC,GAAG,UAAU;AAAA,IACZ,IAAI,IAAI,OAAO;AAAA,IACf,GAAG,IAAI;AAAA,IACP,GAAG,IAAI;AAAA,EACT,CAAC,CAAC;AACJ;AACA,SAAS,WAAW,OAAO,SAAS,QAAQ;AAC1C,YAAU,WAAW;AACrB,WAAS,UAAU;AACnB,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,OAAO,MAAM;AACjB,MAAI,MAAM,CAAC,UAAU,KAAK,CAAC;AAC3B,OAAK,IAAI,KAAK,IAAI,KAAK,OAAO,WAAW,KAAK,OAAO,KAAK,EAAE,WAAU;AACpE,QAAI,KAAK,IAAI,IAAI,QAAQ;AACzB,QAAI,KAAK,UAAU,GAAG,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AACA,SAAS,eAAe,OAAO,SAAS;AACtC,YAAU,WAAW;AACrB,MAAI,MAAM,UAAU,KAAK,EAAE,MAAM;AACjC,MAAI,IAAI,IAAI,GACV,IAAI,IAAI,GACR,IAAI,IAAI;AACV,MAAI,MAAM,CAAC;AACX,MAAI,eAAe,IAAI;AACvB,SAAO,WAAW;AAChB,QAAI,KAAK,UAAU;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AACF,SAAK,IAAI,gBAAgB;AAAA,EAC3B;AACA,SAAO;AACT;AAKA,UAAU,MAAM,SAAU,QAAQ,QAAQ,QAAQ;AAChD,WAAS,WAAW,IAAI,IAAI,UAAU;AACtC,MAAI,OAAO,UAAU,MAAM,EAAE,MAAM;AACnC,MAAI,OAAO,UAAU,MAAM,EAAE,MAAM;AACnC,MAAI,IAAI,SAAS;AACjB,MAAI,OAAO;AAAA,IACT,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,IAChC,IAAI,KAAK,IAAI,KAAK,KAAK,IAAI,KAAK;AAAA,EAClC;AACA,SAAO,UAAU,IAAI;AACvB;AAQA,UAAU,cAAc,SAAU,QAAQ,QAAQ;AAChD,MAAI,KAAK,UAAU,MAAM;AACzB,MAAI,KAAK,UAAU,MAAM;AACzB,UAAQ,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI,SAAS,KAAK,IAAI,GAAG,aAAa,GAAG,GAAG,aAAa,CAAC,IAAI;AACrH;AAYA,UAAU,aAAa,SAAU,QAAQ,QAAQ,OAAO;AACtD,MAAI,cAAc,UAAU,YAAY,QAAQ,MAAM;AACtD,MAAI,YAAY;AAChB,QAAM;AACN,eAAa,mBAAmB,KAAK;AACrC,UAAQ,WAAW,QAAQ,WAAW,MAAM;AAAA,IAC1C,KAAK;AAAA,IACL,KAAK;AACH,YAAM,eAAe;AACrB;AAAA,IACF,KAAK;AACH,YAAM,eAAe;AACrB;AAAA,IACF,KAAK;AACH,YAAM,eAAe;AACrB;AAAA,EACJ;AACA,SAAO;AACT;AAWA,UAAU,eAAe,SAAU,WAAW,WAAW,MAAM;AAC7D,MAAI,YAAY;AAChB,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI,uBAAuB,OAAO;AAClC,SAAO,QAAQ,CAAC;AAChB,0BAAwB,KAAK;AAC7B,UAAQ,KAAK;AACb,SAAO,KAAK;AACZ,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,kBAAc,UAAU,YAAY,WAAW,UAAU,CAAC,CAAC;AAC3D,QAAI,cAAc,WAAW;AAC3B,kBAAY;AACZ,kBAAY,UAAU,UAAU,CAAC,CAAC;AAAA,IACpC;AAAA,EACF;AACA,MAAI,UAAU,WAAW,WAAW,WAAW;AAAA,IAC7C;AAAA,IACA;AAAA,EACF,CAAC,KAAK,CAAC,uBAAuB;AAC5B,WAAO;AAAA,EACT,OAAO;AACL,SAAK,wBAAwB;AAC7B,WAAO,UAAU,aAAa,WAAW,CAAC,QAAQ,MAAM,GAAG,IAAI;AAAA,EACjE;AACF;AAKA,IAAI,QAAQ,UAAU,QAAQ;AAAA,EAC5B,WAAW;AAAA,EACX,cAAc;AAAA,EACd,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,WAAW;AAAA,EACX,aAAa;AAAA,EACb,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,OAAO;AAAA,EACP,gBAAgB;AAAA,EAChB,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,UAAU;AAAA,EACV,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,WAAW;AAAA,EACX,UAAU;AAAA,EACV,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,aAAa;AAAA,EACb,SAAS;AAAA,EACT,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,aAAa;AAAA,EACb,SAAS;AAAA,EACT,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,MAAM;AAAA,EACN,WAAW;AAAA,EACX,MAAM;AAAA,EACN,OAAO;AAAA,EACP,aAAa;AAAA,EACb,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,eAAe;AAAA,EACf,WAAW;AAAA,EACX,cAAc;AAAA,EACd,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,sBAAsB;AAAA,EACtB,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,eAAe;AAAA,EACf,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB;AAAA,EAClB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AAAA,EACP,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,WAAW;AAAA,EACX,eAAe;AAAA,EACf,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,eAAe;AAAA,EACf,KAAK;AAAA,EACL,WAAW;AAAA,EACX,WAAW;AAAA,EACX,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,WAAW;AAAA,EACX,MAAM;AAAA,EACN,aAAa;AAAA,EACb,WAAW;AAAA,EACX,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,aAAa;AACf;AAGA,IAAI,WAAW,UAAU,WAAW,KAAK,KAAK;AAM9C,SAAS,KAAK,GAAG;AACf,MAAI,UAAU,CAAC;AACf,WAAS,KAAK,GAAG;AACf,QAAI,EAAE,eAAe,CAAC,GAAG;AACvB,cAAQ,EAAE,CAAC,CAAC,IAAI;AAAA,IAClB;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,WAAW,GAAG;AACrB,MAAI,WAAW,CAAC;AAChB,MAAI,MAAM,CAAC,KAAK,IAAI,KAAK,IAAI,GAAG;AAC9B,QAAI;AAAA,EACN;AACA,SAAO;AACT;AAGA,SAAS,QAAQ,GAAG,KAAK;AACvB,MAAI,eAAe,CAAC,EAAG,KAAI;AAC3B,MAAI,iBAAiB,aAAa,CAAC;AACnC,MAAI,KAAK,IAAI,KAAK,KAAK,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;AAG5C,MAAI,gBAAgB;AAClB,QAAI,SAAS,IAAI,KAAK,EAAE,IAAI;AAAA,EAC9B;AAGA,MAAI,KAAK,IAAI,IAAI,GAAG,IAAI,MAAU;AAChC,WAAO;AAAA,EACT;AAGA,SAAO,IAAI,MAAM,WAAW,GAAG;AACjC;AAGA,SAAS,QAAQ,KAAK;AACpB,SAAO,KAAK,IAAI,GAAG,KAAK,IAAI,GAAG,GAAG,CAAC;AACrC;AAGA,SAAS,gBAAgB,KAAK;AAC5B,SAAO,SAAS,KAAK,EAAE;AACzB;AAIA,SAAS,eAAe,GAAG;AACzB,SAAO,OAAO,KAAK,YAAY,EAAE,QAAQ,GAAG,KAAK,MAAM,WAAW,CAAC,MAAM;AAC3E;AAGA,SAAS,aAAa,GAAG;AACvB,SAAO,OAAO,MAAM,YAAY,EAAE,QAAQ,GAAG,KAAK;AACpD;AAGA,SAAS,KAAK,GAAG;AACf,SAAO,EAAE,UAAU,IAAI,MAAM,IAAI,KAAK;AACxC;AAGA,SAAS,oBAAoB,GAAG;AAC9B,MAAI,KAAK,GAAG;AACV,QAAI,IAAI,MAAM;AAAA,EAChB;AACA,SAAO;AACT;AAGA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,EAAE,SAAS,EAAE;AACpD;AAEA,SAAS,oBAAoB,GAAG;AAC9B,SAAO,gBAAgB,CAAC,IAAI;AAC9B;AACA,IAAI,WAAW,WAAY;AAEzB,MAAI,cAAc;AAGlB,MAAI,aAAa;AAGjB,MAAI,WAAW,QAAQ,aAAa,UAAU,cAAc;AAK5D,MAAI,oBAAoB,gBAAgB,WAAW,eAAe,WAAW,eAAe,WAAW;AACvG,MAAI,oBAAoB,gBAAgB,WAAW,eAAe,WAAW,eAAe,WAAW,eAAe,WAAW;AACjI,SAAO;AAAA,IACL,UAAU,IAAI,OAAO,QAAQ;AAAA,IAC7B,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,IACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,IAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,IACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,IAC3C,KAAK,IAAI,OAAO,QAAQ,iBAAiB;AAAA,IACzC,MAAM,IAAI,OAAO,SAAS,iBAAiB;AAAA,IAC3C,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,EACR;AACF,EAAE;AAKF,SAAS,eAAe,OAAO;AAC7B,SAAO,CAAC,CAAC,SAAS,SAAS,KAAK,KAAK;AACvC;AAKA,SAAS,oBAAoB,OAAO;AAClC,UAAQ,MAAM,QAAQ,UAAU,EAAE,EAAE,QAAQ,WAAW,EAAE,EAAE,YAAY;AACvE,MAAI,QAAQ;AACZ,MAAI,MAAM,KAAK,GAAG;AAChB,YAAQ,MAAM,KAAK;AACnB,YAAQ;AAAA,EACV,WAAW,SAAS,eAAe;AACjC,WAAO;AAAA,MACL,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,QAAQ;AAAA,IACV;AAAA,EACF;AAMA,MAAI;AACJ,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK,GAAG;AACpC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK,GAAG;AACpC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,IAAI,KAAK,KAAK,GAAG;AACpC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,MACV,GAAG,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,oBAAoB,MAAM,CAAC,CAAC;AAAA,MAC/B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,GAAG,gBAAgB,MAAM,CAAC,CAAC;AAAA,MAC3B,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,oBAAoB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC/C,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,MAAI,QAAQ,SAAS,KAAK,KAAK,KAAK,GAAG;AACrC,WAAO;AAAA,MACL,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,GAAG,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,MAC3C,QAAQ,QAAQ,SAAS;AAAA,IAC3B;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,OAAO;AAGjC,MAAI,OAAO;AACX,UAAQ,SAAS;AAAA,IACf,OAAO;AAAA,IACP,MAAM;AAAA,EACR;AACA,WAAS,MAAM,SAAS,MAAM,YAAY;AAC1C,UAAQ,MAAM,QAAQ,SAAS,YAAY;AAC3C,MAAI,UAAU,QAAQ,UAAU,OAAO;AACrC,YAAQ;AAAA,EACV;AACA,MAAI,SAAS,WAAW,SAAS,SAAS;AACxC,WAAO;AAAA,EACT;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;", "names": ["obj", "p", "q"]}