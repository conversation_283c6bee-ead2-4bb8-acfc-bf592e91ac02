import "./chunk-PLDDJCW6.js";

// node_modules/vue3-ts-jsoneditor/dist/styles-handler-nPP1xL5s.js
var n = (t, o) => {
  if (window != null && window.document.getElementById(t)) return;
  const a = window == null ? void 0 : window.document.getElementsByTagName("head")[0], e = window == null ? void 0 : window.document.createElement("style");
  e.setAttribute("id", t), e.textContent = o, a.appendChild(e);
};
var d = async () => {
  const { fullWidthButton: t } = await import("./string-styles-DT93GIY_-VTWFRJ6K.js");
  n("full-width-button", t);
};
var l = async () => {
  const { darkTheme: t } = await import("./string-styles-DT93GIY_-VTWFRJ6K.js");
  n("dark-theme", t);
};
export {
  l as setDarkThemeStyle,
  d as setFullWidthButtonStyle
};
//# sourceMappingURL=styles-handler-nPP1xL5s-UM7LI7SO.js.map
